# Accompany 陪伴服务平台

🌟 **连接人心，传递温暖** - 一个基于现代技术栈构建的陪伴服务平台

## 📖 项目概述

Accompany是一个创新的陪伴服务平台，致力于为用户提供多样化的陪伴服务，包括聊天陪伴、运动陪伴、学习陪伴等。平台通过严格的认证体系和智能匹配算法，确保服务质量和用户安全。

### 🎯 核心价值观
- **信任** - 通过实名认证和信用体系建立可靠的服务环境
- **温暖** - 传递人与人之间的温暖和关怀
- **专业** - 提供高质量、专业化的陪伴服务
- **便捷** - 简单易用的平台操作体验

## 🏗 技术架构

### 后端技术栈
- **Spring Boot 3.5.3** - 现代化Java框架
- **Spring Cloud 2023.0.3** - 微服务架构
- **Spring Security** - 安全认证框架
- **MyBatis Plus** - 数据库操作框架
- **MySQL 8.0** - 关系型数据库
- **MongoDB** - 文档数据库（聊天记录）
- **Redis** - 缓存和会话存储
- **JWT** - 无状态认证

### 前端技术栈

#### Web端
- **Vue 3** - 现代化前端框架
- **Vite** - 快速构建工具
- **TypeScript** - 类型安全
- **Element Plus** - UI组件库
- **Pinia** - 状态管理

#### 移动端
- **iOS** - Swift + SwiftUI
- **Android** - Kotlin + Jetpack Compose
- **微信小程序** - 微信生态集成

## 📁 项目结构

```
Accompany/
├── accompany-backend/           # 后端微服务
│   ├── pom.xml                 # 父项目Maven配置
│   ├── accompany-common/       # 通用模块
│   │   ├── src/main/java/com/bigsincerity/accompany/common/
│   │   │   └── result/         # 统一响应结果
│   │   └── pom.xml
│   └── accompany-user/         # 用户服务模块
│       ├── src/main/java/com/bigsincerity/accompany/user/
│       │   ├── controller/     # 控制器层
│       │   ├── service/        # 业务逻辑层
│       │   ├── entity/         # 实体类
│       │   └── dto/           # 数据传输对象
│       ├── src/main/resources/
│       │   └── application.yml # 配置文件
│       └── pom.xml
├── accompany-frontend/          # 前端应用
│   ├── accompany-web/          # Vue 3 Web应用
│   │   ├── src/
│   │   │   ├── api/           # API接口层
│   │   │   ├── components/    # 通用组件
│   │   │   ├── router/        # 路由配置
│   │   │   ├── stores/        # 状态管理
│   │   │   ├── styles/        # 样式文件
│   │   │   └── views/         # 页面组件
│   │   └── package.json
│   ├── ios/                    # iOS应用 (SwiftUI)
│   ├── android/                # Android应用 (Kotlin)
│   └── wechat/                 # 微信小程序
├── docs/                       # 项目文档
│   ├── 1-产品需求文档.md
│   ├── 2-UI设计规范.md
│   ├── 3-技术架构设计.md
│   ├── 4-开发计划.md
│   └── 5-测试策略.md
├── dbdata/                     # 数据库脚本
│   ├── mysql.sql              # MySQL建表脚本
│   └── mongo.sql              # MongoDB初始化脚本
└── README.md                   # 项目说明文档
```

## ⚡ 快速开始

### 环境要求

- **Java 17+** - 后端运行环境
- **Node.js 16+** - 前端开发环境
- **MySQL 8.0+** - 数据库
- **Redis 6.0+** - 缓存服务
- **Maven 3.6+** - 项目构建工具

### 1. 克隆项目

```bash
git clone <repository-url>
cd Accompany
```

### 2. 后端启动

```bash
# 进入后端目录
cd accompany-backend

# 设置Java环境变量
export JAVA_HOME=/opt/homebrew/opt/openjdk@17

# 启动用户服务
cd accompany-user
mvn spring-boot:run
```

后端服务将在 `http://localhost:8081` 启动

### 3. 前端启动

```bash
# 进入前端目录
cd accompany-frontend/accompany-web

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

前端服务将在 `http://localhost:3000` 启动

### 4. 访问应用

- **Web应用**: http://localhost:3000
- **API文档**: http://localhost:8081/api (开发中)
- **用户注册页面**: http://localhost:3000/register

## 🎨 UI设计规范

项目遵循统一的UI设计规范，详见 [UI设计规范文档](docs/2-UI设计规范.md)

### 核心设计理念
- **品牌愿景**: 连接人心，传递温暖
- **设计原则**: 简约而不简单，温暖而不失专业
- **视觉风格**: 温暖、友好、简约现代、扁平化设计

### 色彩方案
- **品牌主色**: #FF6B6B (温暖珊瑚红)
- **辅助主色**: #4ECDC4 (清新薄荷绿)  
- **强调色**: #45B7D1 (天空蓝)

## 🚀 已实现功能

### ✅ 用户注册功能
- [x] 前端注册页面（Vue 3 + Element Plus）
- [x] 后端注册接口（Spring Boot）
- [x] 用户名/邮箱/手机号唯一性验证
- [x] 短信验证码发送（模拟）
- [x] 密码强度验证
- [x] 表单验证和错误处理
- [x] 响应式设计适配

### ✅ 项目基础架构
- [x] Spring Boot 3.5.3 + Spring Cloud 2023.0.3 微服务架构
- [x] Vue 3 + Vite + TypeScript 前端架构
- [x] Maven多模块项目结构
- [x] 统一的API响应格式
- [x] 跨域配置和代理设置
- [x] Lombok和MyBatis Plus集成

### ✅ 开发规范
- [x] 包名统一规范 (com.bigsincerity.accompany)
- [x] 代码结构清晰的分层架构
- [x] 完善的项目文档
- [x] Git版本控制

## 🚧 开发路线图

### Phase 1 - 用户系统 (已完成 90%)
- [x] 用户注册
- [ ] 用户登录
- [ ] 个人资料管理
- [ ] 实名认证
- [ ] 第三方登录集成

### Phase 2 - 服务系统 (计划中)
- [ ] 服务发布功能
- [ ] 服务搜索和筛选
- [ ] 智能匹配算法
- [ ] 预约和订单管理

### Phase 3 - 交易系统 (计划中)
- [ ] 钱包和余额管理
- [ ] 支付接口集成
- [ ] 订单结算系统
- [ ] 退款处理

### Phase 4 - 通讯系统 (计划中)
- [ ] 即时聊天功能
- [ ] 音视频通话
- [ ] 文件传输
- [ ] 消息推送

### Phase 5 - 社区功能 (计划中)
- [ ] 评价和评分系统
- [ ] 社区动态发布
- [ ] 举报和客服系统
- [ ] 积分和等级体系

## 🔧 开发指南

### 后端开发

1. **环境配置**
   ```bash
   # 设置Java环境
   export JAVA_HOME=/opt/homebrew/opt/openjdk@17
   
   # 编译项目
   mvn clean compile
   
   # 运行测试
   mvn test
   ```

2. **数据库配置**
   ```yaml
   # accompany-user/src/main/resources/application.yml
   spring:
     datasource:
       url: ****************************************
       username: root
       password: 123456
   ```

3. **API开发规范**
   - 使用 `Result<T>` 统一响应格式
   - 遵循 RESTful API 设计规范
   - 完善的参数验证和错误处理

### 前端开发

1. **环境配置**
   ```bash
   cd accompany-frontend/accompany-web
   npm install
   npm run dev
   ```

2. **状态管理**
   - 使用 Pinia 进行状态管理
   - 组件间数据共享和持久化

3. **样式规范**
   - 遵循 CSS 变量设计系统
   - 使用 Scoped CSS
   - 响应式设计适配

## 📱 多端适配

### Web端
- 现代浏览器支持 (Chrome 87+, Firefox 78+, Safari 14+)
- 响应式设计，支持桌面和移动端访问
- PWA支持 (计划中)

### 移动端
- **iOS应用**: Swift + SwiftUI 原生开发
- **Android应用**: Kotlin + Jetpack Compose
- **微信小程序**: 微信生态深度集成

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交变更 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 提交 Pull Request

### 代码规范
- Java: 遵循 Google Java Style Guide
- Vue: 遵循 Vue 3 官方风格指南
- 提交信息: 使用语义化提交规范

## 📄 许可证

本项目遵循 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 👥 团队

- **产品设计**: Accompany团队
- **后端开发**: Spring Boot 微服务架构
- **前端开发**: Vue 3 现代化前端
- **移动端开发**: 原生应用开发

## 📞 联系我们

- **项目地址**: [GitHub Repository]
- **问题反馈**: [GitHub Issues]
- **技术讨论**: [Discussions]

---

💝 **感谢您对 Accompany 项目的关注和支持！**
