import SwiftUI

/**
 * 应用主视图
 * 根据应用状态显示不同的界面
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
struct ContentView: View {
    
    // MARK: - 环境对象
    @EnvironmentObject var appState: AppStateManager
    @EnvironmentObject var userManager: UserManager
    
    var body: some View {
        ZStack {
            // 根据应用状态显示相应视图
            switch appState.currentView {
            case .loading:
                LoadingView()
            case .onboarding:
                OnboardingView()
            case .login:
                LoginView()
            case .main:
                MainTabView()
            }
        }
        .alert("提示", isPresented: $appState.showingAlert) {
            Button("确定") {
                appState.showingAlert = false
            }
        } message: {
            Text(appState.alertMessage)
        }
    }
}

// MARK: - 加载视图

/**
 * 应用启动加载视图
 */
struct LoadingView: View {
    @State private var isAnimating = false
    
    var body: some View {
        ZStack {
            // 品牌主色背景
            LinearGradient(
                colors: [Color(hex: "FF6B6B"), Color(hex: "4ECDC4")],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            VStack(spacing: 30) {
                // Logo
                Image(systemName: "heart.circle.fill")
                    .font(.system(size: 80))
                    .foregroundColor(.white)
                    .scaleEffect(isAnimating ? 1.2 : 1.0)
                    .animation(.easeInOut(duration: 1.0).repeatForever(), value: isAnimating)
                
                // 应用名称
                Text("陪伴")
                    .font(.system(size: 32, weight: .bold, design: .rounded))
                    .foregroundColor(.white)
                
                // 副标题
                Text("连接人心，传递温暖")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))
                
                // 加载指示器
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    .scaleEffect(1.2)
            }
        }
        .onAppear {
            isAnimating = true
            // 模拟加载时间
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                // 这里由AppStateManager控制视图切换
            }
        }
    }
}

// MARK: - 引导页视图

/**
 * 应用引导页视图
 */
struct OnboardingView: View {
    @EnvironmentObject var appState: AppStateManager
    @State private var currentPage = 0
    
    private let onboardingPages = [
        OnboardingPage(
            image: "heart.hands",
            title: "寻找陪伴",
            description: "在这里找到理解你的人，分享生活的美好时光",
            color: Color(hex: "FF6B6B")
        ),
        OnboardingPage(
            image: "person.2.circle",
            title: "专业服务",
            description: "经过认证的陪伴者，为你提供专业贴心的服务",
            color: Color(hex: "4ECDC4")
        ),
        OnboardingPage(
            image: "shield.checkered",
            title: "安全保障",
            description: "多重安全机制，让每一次陪伴都安心放心",
            color: Color(hex: "45B7D1")
        )
    ]
    
    var body: some View {
        VStack {
            // 页面指示器
            HStack {
                ForEach(0..<onboardingPages.count, id: \.self) { index in
                    Circle()
                        .fill(index == currentPage ? Color.primary : Color.gray.opacity(0.3))
                        .frame(width: 8, height: 8)
                        .animation(.easeInOut, value: currentPage)
                }
            }
            .padding(.top, 50)
            
            // 页面内容
            TabView(selection: $currentPage) {
                ForEach(0..<onboardingPages.count, id: \.self) { index in
                    OnboardingPageView(page: onboardingPages[index])
                        .tag(index)
                }
            }
            .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            
            // 操作按钮
            VStack(spacing: 16) {
                if currentPage == onboardingPages.count - 1 {
                    // 最后一页显示开始按钮
                    Button("开始使用") {
                        UserDefaults.standard.set(true, forKey: "has_shown_onboarding")
                        appState.currentView = .login
                    }
                    .buttonStyle(PrimaryButtonStyle())
                } else {
                    // 其他页面显示下一步
                    Button("下一步") {
                        withAnimation {
                            currentPage += 1
                        }
                    }
                    .buttonStyle(PrimaryButtonStyle())
                }
                
                // 跳过按钮
                Button("跳过") {
                    UserDefaults.standard.set(true, forKey: "has_shown_onboarding")
                    appState.currentView = .login
                }
                .foregroundColor(.gray)
            }
            .padding(.horizontal, 32)
            .padding(.bottom, 50)
        }
    }
}

/**
 * 引导页页面视图
 */
struct OnboardingPageView: View {
    let page: OnboardingPage
    
    var body: some View {
        VStack(spacing: 40) {
            Spacer()
            
            // 图标
            Image(systemName: page.image)
                .font(.system(size: 100))
                .foregroundColor(page.color)
            
            VStack(spacing: 16) {
                // 标题
                Text(page.title)
                    .font(.system(size: 28, weight: .bold))
                    .foregroundColor(.primary)
                
                // 描述
                Text(page.description)
                    .font(.system(size: 16))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 32)
            }
            
            Spacer()
        }
    }
}

// MARK: - 登录视图

/**
 * 登录视图
 */
struct LoginView: View {
    @EnvironmentObject var appState: AppStateManager
    @EnvironmentObject var userManager: UserManager
    
    @State private var phoneNumber = ""
    @State private var verificationCode = ""
    @State private var isCodeSent = false
    @State private var countdown = 60
    @State private var isLoading = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 30) {
                    // Logo和标题
                    VStack(spacing: 16) {
                        Image(systemName: "heart.circle.fill")
                            .font(.system(size: 60))
                            .foregroundColor(Color(hex: "FF6B6B"))
                        
                        Text("欢迎来到陪伴")
                            .font(.system(size: 24, weight: .bold))
                        
                        Text("连接人心，传递温暖")
                            .font(.system(size: 14))
                            .foregroundColor(.secondary)
                    }
                    .padding(.top, 50)
                    
                    // 登录表单
                    VStack(spacing: 20) {
                        // 手机号输入
                        VStack(alignment: .leading, spacing: 8) {
                            Text("手机号")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.secondary)
                            
                            HStack {
                                Text("+86")
                                    .font(.system(size: 16))
                                    .foregroundColor(.secondary)
                                
                                TextField("请输入手机号", text: $phoneNumber)
                                    .keyboardType(.phonePad)
                                    .textFieldStyle(RoundedBorderTextFieldStyle())
                            }
                        }
                        
                        // 验证码输入
                        VStack(alignment: .leading, spacing: 8) {
                            Text("验证码")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.secondary)
                            
                            HStack {
                                TextField("请输入验证码", text: $verificationCode)
                                    .keyboardType(.numberPad)
                                    .textFieldStyle(RoundedBorderTextFieldStyle())
                                
                                Button(isCodeSent ? "\(countdown)s" : "发送验证码") {
                                    sendVerificationCode()
                                }
                                .disabled(isCodeSent || phoneNumber.count != 11)
                                .foregroundColor(isCodeSent ? .gray : Color(hex: "FF6B6B"))
                            }
                        }
                        
                        // 登录按钮
                        Button("登录") {
                            login()
                        }
                        .disabled(phoneNumber.count != 11 || verificationCode.count != 6 || isLoading)
                        .buttonStyle(PrimaryButtonStyle())
                        
                        // 第三方登录
                        VStack(spacing: 16) {
                            Text("或使用以下方式登录")
                                .font(.system(size: 12))
                                .foregroundColor(.secondary)
                            
                            HStack(spacing: 20) {
                                SocialLoginButton(type: .wechat) {
                                    socialLogin(.wechat)
                                }
                                
                                SocialLoginButton(type: .qq) {
                                    socialLogin(.qq)
                                }
                                
                                SocialLoginButton(type: .apple) {
                                    socialLogin(.apple)
                                }
                            }
                        }
                        .padding(.top, 20)
                    }
                    .padding(.horizontal, 32)
                    
                    // 协议同意
                    VStack(spacing: 8) {
                        Text("登录即表示同意")
                            .font(.system(size: 12))
                            .foregroundColor(.secondary)
                        
                        HStack {
                            Button("《用户协议》") {
                                // TODO: 显示用户协议
                            }
                            .font(.system(size: 12))
                            .foregroundColor(Color(hex: "FF6B6B"))
                            
                            Text("和")
                                .font(.system(size: 12))
                                .foregroundColor(.secondary)
                            
                            Button("《隐私政策》") {
                                // TODO: 显示隐私政策
                            }
                            .font(.system(size: 12))
                            .foregroundColor(Color(hex: "FF6B6B"))
                        }
                    }
                    .padding(.top, 20)
                }
            }
            .navigationBarHidden(true)
        }
        .navigationViewStyle(StackNavigationViewStyle())
    }
    
    // MARK: - 私有方法
    
    private func sendVerificationCode() {
        guard phoneNumber.count == 11 else { return }
        
        isCodeSent = true
        countdown = 60
        
        // 启动倒计时
        Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { timer in
            countdown -= 1
            if countdown <= 0 {
                timer.invalidate()
                isCodeSent = false
                countdown = 60
            }
        }
        
        // TODO: 调用发送验证码API
        print("发送验证码到: \(phoneNumber)")
    }
    
    private func login() {
        guard phoneNumber.count == 11 && verificationCode.count == 6 else { return }
        
        isLoading = true
        
        // TODO: 调用登录API
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            isLoading = false
            userManager.isLoggedIn = true
            appState.currentView = .main
        }
    }
    
    private func socialLogin(_ type: SocialLoginType) {
        // TODO: 实现第三方登录
        print("第三方登录: \(type)")
    }
}

// MARK: - 主标签页视图

/**
 * 主应用标签页视图
 */
struct MainTabView: View {
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            // 首页
            HomeView()
                .tabItem {
                    Image(systemName: "house.fill")
                    Text("首页")
                }
                .tag(0)
            
            // 赏金
            BountyView()
                .tabItem {
                    Image(systemName: "star.fill")
                    Text("赏金")
                }
                .tag(1)
            
            // 发布
            PublishView()
                .tabItem {
                    Image(systemName: "plus.circle.fill")
                    Text("发布")
                }
                .tag(2)
            
            // 消息
            MessageView()
                .tabItem {
                    Image(systemName: "message.fill")
                    Text("消息")
                }
                .tag(3)
            
            // 我的
            ProfileView()
                .tabItem {
                    Image(systemName: "person.fill")
                    Text("我的")
                }
                .tag(4)
        }
        .accentColor(Color(hex: "FF6B6B"))
    }
}

// MARK: - 临时视图定义

struct HomeView: View {
    var body: some View {
        NavigationView {
            Text("首页")
                .navigationTitle("首页")
        }
    }
}

struct BountyView: View {
    var body: some View {
        NavigationView {
            Text("赏金")
                .navigationTitle("赏金")
        }
    }
}

struct PublishView: View {
    var body: some View {
        NavigationView {
            Text("发布")
                .navigationTitle("发布")
        }
    }
}

struct MessageView: View {
    var body: some View {
        NavigationView {
            Text("消息")
                .navigationTitle("消息")
        }
    }
}

struct ProfileView: View {
    var body: some View {
        NavigationView {
            Text("我的")
                .navigationTitle("我的")
        }
    }
}

// MARK: - 支持类型和组件

struct OnboardingPage {
    let image: String
    let title: String
    let description: String
    let color: Color
}

enum SocialLoginType {
    case wechat, qq, apple
}

struct SocialLoginButton: View {
    let type: SocialLoginType
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Circle()
                .fill(backgroundColor)
                .frame(width: 50, height: 50)
                .overlay(
                    Image(systemName: iconName)
                        .font(.system(size: 24))
                        .foregroundColor(.white)
                )
        }
    }
    
    private var backgroundColor: Color {
        switch type {
        case .wechat: return Color(hex: "07C160")
        case .qq: return Color(hex: "12B7F5")
        case .apple: return Color.black
        }
    }
    
    private var iconName: String {
        switch type {
        case .wechat: return "message.fill"
        case .qq: return "q.circle.fill"
        case .apple: return "apple.logo"
        }
    }
}

struct PrimaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .frame(maxWidth: .infinity)
            .frame(height: 50)
            .background(Color(hex: "FF6B6B"))
            .foregroundColor(.white)
            .font(.system(size: 16, weight: .medium))
            .cornerRadius(25)
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - Color扩展

extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }
        
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

// MARK: - 预览

struct ContentView_Previews: PreviewProvider {
    static var previews: some View {
        ContentView()
            .environmentObject(AppStateManager())
            .environmentObject(UserManager())
            .environmentObject(LocationManager())
            .environmentObject(NotificationManager())
    }
} 