import { defineStore } from 'pinia'
import { userApi } from '@/api/user'

export const useUserStore = defineStore('user', {
  state: () => ({
    // 用户信息
    userInfo: null,
    // 登录状态
    isLoggedIn: false,
    // Token
    token: localStorage.getItem('token') || null,
  }),

  getters: {
    // 是否已登录
    isAuthenticated: (state) => !!state.token && !!state.userInfo,
    // 用户昵称
    userName: (state) => state.userInfo?.userName || '',
    // 用户头像
    avatar: (state) => state.userInfo?.avatarUrl || '',
  },

  actions: {
    /**
     * 设置Token
     */
    setToken(token) {
      this.token = token
      if (token) {
        localStorage.setItem('token', token)
      } else {
        localStorage.removeItem('token')
      }
    },

    /**
     * 设置用户信息
     */
    setUserInfo(userInfo) {
      this.userInfo = userInfo
      this.isLoggedIn = !!userInfo
    },

    /**
     * 用户注册
     */
    async register(registerData) {
      try {
        const response = await userApi.register(registerData)
        
        if (response.data) {
          // 注册成功，如果返回了token则自动登录
          if (response.data.token) {
            this.setToken(response.data.token)
            this.setUserInfo(response.data.userInfo)
          }
        }
        
        return response
      } catch (error) {
        console.error('注册失败:', error)
        throw error
      }
    },

    /**
     * 用户登录
     */
    async login(loginData) {
      try {
        const response = await userApi.login(loginData)
        
        if (response.data) {
          this.setToken(response.data.token)
          
          // 获取用户信息
          await this.getCurrentUser()
        }
        
        return response
      } catch (error) {
        console.error('登录失败:', error)
        throw error
      }
    },

    /**
     * 获取当前用户信息
     */
    async getCurrentUser() {
      try {
        const response = await userApi.getCurrentUser()
        
        if (response.data) {
          this.setUserInfo(response.data)
        }
        
        return response
      } catch (error) {
        console.error('获取用户信息失败:', error)
        // 如果获取用户信息失败，可能token已过期，清除登录状态
        this.logout()
        throw error
      }
    },

    /**
     * 用户登出
     */
    async logout() {
      try {
        // 调用登出接口
        await userApi.logout()
      } catch (error) {
        console.error('登出接口调用失败:', error)
      } finally {
        // 无论接口是否成功，都清除本地状态
        this.setToken(null)
        this.setUserInfo(null)
      }
    },

    /**
     * 检查登录状态
     */
    async checkAuthStatus() {
      if (this.token && !this.userInfo) {
        try {
          await this.getCurrentUser()
        } catch (error) {
          // 检查失败，清除token
          this.logout()
        }
      }
    },
  },
}) 