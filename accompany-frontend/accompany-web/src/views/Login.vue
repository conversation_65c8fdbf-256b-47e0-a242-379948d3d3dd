<template>
  <div class="login-page">
    <!-- 页面容器 -->
    <div class="page-container">
      <!-- 左侧品牌区域 -->
      <div class="brand-section">
        <div class="brand-content">
          <div class="logo">
            <h1>Accompany</h1>
            <p class="slogan">连接人心，传递温暖</p>
          </div>
          
          <div class="welcome-message">
            <h2>欢迎回来</h2>
            <p>继续您的陪伴之旅，重新连接美好生活</p>
          </div>
          
          <div class="testimonials">
            <div class="testimonial">
              <div class="quote">"通过Accompany，我找到了真正的温暖和陪伴"</div>
              <div class="author">- 李女士</div>
            </div>
            
            <div class="testimonial">
              <div class="quote">"专业的服务让我感受到了家的温暖"</div>
              <div class="author">- 张先生</div>
            </div>
          </div>
          
          <div class="stats">
            <div class="stat">
              <div class="number">98.5%</div>
              <div class="label">用户留存率</div>
            </div>
            <div class="stat">
              <div class="number">24/7</div>
              <div class="label">全天候服务</div>
            </div>
            <div class="stat">
              <div class="number">5★</div>
              <div class="label">平均评分</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右侧表单区域 -->
      <div class="form-section">
        <div class="form-container">
          <div class="form-header">
            <h2>登录账户</h2>
            <p>使用您的账户信息登录</p>
          </div>
          
          <el-form 
            ref="loginForm"
            :model="formData"
            :rules="formRules"
            class="login-form"
            size="large"
          >
            <el-form-item prop="account">
              <el-input
                v-model="formData.account"
                placeholder="请输入用户名/手机号/邮箱"
                prefix-icon="User"
                clearable
              />
            </el-form-item>
            
            <el-form-item prop="password">
              <el-input
                v-model="formData.password"
                type="password"
                placeholder="请输入密码"
                prefix-icon="Lock"
                show-password
              />
            </el-form-item>
            
            <el-form-item>
              <div class="login-options">
                <el-checkbox v-model="formData.rememberMe">
                  记住我
                </el-checkbox>
                <a href="#" @click.prevent="showForgotPassword" class="forgot-link">
                  忘记密码？
                </a>
              </div>
            </el-form-item>
            
            <el-form-item>
              <el-button 
                type="primary" 
                size="large"
                :loading="loading"
                @click="handleLogin"
                class="login-btn"
              >
                {{ loading ? '登录中...' : '立即登录' }}
              </el-button>
            </el-form-item>
          </el-form>
          
          <div class="form-footer">
            <p>还没有账号？<a href="#" @click.prevent="$router.push('/register')">立即注册</a></p>
          </div>
          
          <div class="social-login">
            <div class="divider">
              <span>或使用第三方账号登录</span>
            </div>
            
            <div class="social-buttons">
              <el-button 
                @click="socialLogin('wechat')" 
                class="social-btn"
              >
                <span class="icon">📱</span>
                微信登录
              </el-button>
              
              <el-button 
                @click="socialLogin('qq')" 
                class="social-btn"
              >
                <span class="icon">🐧</span>
                QQ登录
              </el-button>
            </div>
            
            <div class="divider">
              <span>快速登录</span>
            </div>
            
            <div class="phone-login-section">
              <el-button 
                @click="showPhoneLogin"
                class="phone-login-btn"
                size="large"
              >
                📱 手机验证码登录
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { authAPI } from '@/api/auth'

const router = useRouter()

// 表单数据
const formData = reactive({
  account: '',
  password: '',
  rememberMe: false
})

// 状态
const loading = ref(false)

// 表单验证规则
const formRules = {
  account: [
    { required: true, message: '请输入用户名/手机号/邮箱', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少6位', trigger: 'blur' }
  ]
}

// 登录处理
const loginForm = ref()
const handleLogin = async () => {
  if (!loginForm.value) return
  
  try {
    const valid = await loginForm.value.validate()
    if (!valid) return
    
    loading.value = true
    
    // 调用登录API
    const response = await authAPI.login({
      account: formData.account,
      password: formData.password,
      rememberMe: formData.rememberMe
    })
    
    if (response.success) {
      // 保存登录信息到本地存储
      const { data } = response
      localStorage.setItem('accessToken', data.accessToken)
      localStorage.setItem('refreshToken', data.refreshToken)
      localStorage.setItem('userInfo', JSON.stringify({
        userId: data.userId,
        userName: data.userName,
        nickName: data.nickName,
        avatar: data.avatar,
        phone: data.phone,
        email: data.email
      }))
      
      ElMessage.success('登录成功！欢迎回来')
      router.push('/dashboard')
    } else {
      ElMessage.error(response.message || '登录失败')
    }
  } catch (error) {
    console.error('登录错误:', error)
    if (error.response?.data?.message) {
      ElMessage.error(error.response.data.message)
    } else {
      ElMessage.error('登录失败，请检查网络连接')
    }
  } finally {
    loading.value = false
  }
}

// 第三方登录
const socialLogin = (provider) => {
  ElMessage.info(`${provider}登录功能开发中...`)
}

// 忘记密码
const showForgotPassword = () => {
  ElMessage.info('忘记密码功能开发中...')
}

// 手机登录
const showPhoneLogin = () => {
  ElMessage.info('手机验证码登录功能开发中...')
}
</script>

<style scoped>
.login-page {
  display: flex;
  min-height: 100vh;
  width: 100%;
  max-width: 1200px;
  background: white;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
}

/* 页面容器 */
.page-container {
  display: flex;
  width: 100%;
  min-height: 100vh;
  background: white;
}

/* 左侧品牌区域 */
.brand-section {
  flex: 1;
  min-width: 350px;
  background: linear-gradient(135deg, #FF6B6B 0%, #45B7D1 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.brand-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
}

.brand-content {
  position: relative;
  z-index: 2;
  padding: 60px;
  width: 100%;
  max-width: 500px;
  color: white;
  text-align: center;
}

.logo h1 {
  font-size: 3.2rem;
  font-weight: 700;
  margin-bottom: 16px;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
  line-height: 1.1;
  letter-spacing: 1px;
  white-space: nowrap;
  overflow: visible;
}

.slogan {
  font-size: 1.25rem;
  margin-bottom: 60px;
  opacity: 0.9;
}

.welcome-message {
  margin-bottom: 60px;
}

.welcome-message h2 {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 16px;
}

.welcome-message p {
  font-size: 1.125rem;
  opacity: 0.8;
  line-height: 1.6;
}

.testimonials {
  margin-bottom: 60px;
  text-align: left;
}

.testimonial {
  margin-bottom: 32px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  text-align: left;
}

.quote {
  font-size: 1.125rem;
  line-height: 1.6;
  margin-bottom: 12px;
  font-style: italic;
}

.author {
  opacity: 0.8;
  text-align: right;
}

.stats {
  display: flex;
  gap: 40px;
  justify-content: center;
  text-align: center;
}

.stat {
  text-align: center;
}

.number {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 8px;
}

.label {
  opacity: 0.8;
}

/* 右侧表单区域 */
.form-section {
  flex: 1;
  min-width: 350px;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: 100px 40px 40px 40px;
  background: white;
  overflow-y: auto;
}

.form-container {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  padding-top: 40px;
}

.form-header {
  text-align: center;
  margin-bottom: 32px;
  padding-top: 30px;
}

.form-header h2 {
  font-size: 1.875rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 12px;
  line-height: 1.2;
}

.form-header p {
  color: #6b7280;
  font-size: 1rem;
  line-height: 1.4;
}

.login-form {
  margin-bottom: 24px;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.forgot-link {
  color: #FF6B6B;
  text-decoration: none;
  font-size: 0.9rem;
}

.forgot-link:hover {
  color: #E55555;
}

.login-btn {
  width: 100%;
  height: 44px;
  font-size: 1rem;
  font-weight: 600;
  margin-top: 8px;
}

.form-footer {
  text-align: center;
}

.form-footer p {
  color: #6b7280;
  margin-bottom: 20px;
  font-size: 0.9rem;
}

.form-footer a {
  color: #FF6B6B;
  text-decoration: none;
  font-weight: 600;
}

.form-footer a:hover {
  color: #E55555;
}

.social-login {
  margin-top: 32px;
}

.divider {
  position: relative;
  margin: 24px 0;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e5e7eb;
}

.divider span {
  background: white;
  padding: 0 16px;
  color: #9ca3af;
  font-size: 0.875rem;
}

.social-buttons {
  display: flex;
  gap: 12px;
}

.social-btn {
  flex: 1;
  height: 48px;
  border: 1px solid #e5e7eb;
  background: white;
  color: #374151;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s;
}

.social-btn:hover {
  border-color: #d1d5db;
  background: #f9fafb;
}

.social-btn .icon {
  font-size: 1.125rem;
}

.phone-login-section {
  margin-top: 20px;
  text-align: center;
}

.phone-login-btn {
  width: 100%;
  color: #6b7280;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
}

.phone-login-btn:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .login-page {
    max-width: 100%;
    box-shadow: none;
  }
  
  .page-container {
    flex-direction: column;
    min-height: auto;
  }
  
  .brand-section {
    flex: none;
    min-width: auto;
    height: 280px;
  }
  
  .brand-content {
    padding: 30px 20px;
  }
  
  .logo h1 {
    font-size: 2.5rem;
  }
  
  .testimonials {
    display: none;
  }
  
  .stats {
    justify-content: center;
  }
  
  .form-section {
    flex: none;
    min-width: auto;
    padding: 60px 20px 30px 20px;
    min-height: auto;
    align-items: flex-start;
  }
  
  .form-container {
    margin-top: 10px;
    padding-top: 30px;
  }
  
  .form-header {
    padding-top: 20px;
  }
}

@media (max-width: 768px) {
  .brand-section {
    height: 250px;
  }
  
  .brand-content {
    padding: 20px;
  }
  
  .logo h1 {
    font-size: 2rem;
  }
  
  .stats {
    gap: 20px;
  }
  
  .form-section {
    padding: 40px 20px;
  }
  
  .form-container {
    padding-top: 20px;
  }
  
  .form-header {
    margin-bottom: 24px;
    padding-top: 15px;
  }
  
  .social-buttons {
    flex-direction: column;
  }
}

@media (max-width: 500px) {
  .login-page {
    padding: 0;
    margin: 0;
  }
  
  .page-container {
    min-height: 100vh;
  }
  
  .brand-section {
    height: 200px;
  }
  
  .brand-content {
    padding: 15px;
  }
  
  .logo h1 {
    font-size: 1.8rem;
  }
  
  .form-section {
    padding: 20px 15px;
  }
  
  .form-container {
    max-width: 100%;
  }
}

/* Element Plus 样式覆盖 */
:deep(.el-form-item) {
  margin-bottom: 24px;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #d1d5db;
}

:deep(.el-input__wrapper.is-focus) {
  border-color: #FF6B6B;
  box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
}

:deep(.el-button--primary) {
  background-color: #FF6B6B;
  border-color: #FF6B6B;
}

:deep(.el-button--primary:hover) {
  background-color: #E55555;
  border-color: #E55555;
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #FF6B6B;
  border-color: #FF6B6B;
}
</style> 