<template>
  <div class="register-page">
    <!-- 页面容器 -->
    <div class="page-container">
      <!-- 左侧品牌区域 -->
      <div class="brand-section">
        <div class="brand-content">
          <div class="logo">
            <h1>Accompany</h1>
            <p class="slogan">连接人心，传递温暖</p>
          </div>
          
          <div class="features">
            <div class="feature-item">
              <div class="feature-icon">🤝</div>
              <div class="feature-text">
                <h3>真实认证</h3>
                <p>实名制认证，确保每一位用户的真实性</p>
              </div>
            </div>
            
            <div class="feature-item">
              <div class="feature-icon">🛡️</div>
              <div class="feature-text">
                <h3>安全保障</h3>
                <p>全程加密保护，守护您的隐私安全</p>
              </div>
            </div>
            
            <div class="feature-item">
              <div class="feature-icon">💝</div>
              <div class="feature-text">
                <h3>温暖陪伴</h3>
                <p>专业的陪伴服务，带给您温暖的体验</p>
              </div>
            </div>
          </div>
          
          <div class="stats">
            <div class="stat">
              <div class="number">10W+</div>
              <div class="label">注册用户</div>
            </div>
            <div class="stat">
              <div class="number">50W+</div>
              <div class="label">服务次数</div>
            </div>
            <div class="stat">
              <div class="number">99%</div>
              <div class="label">满意度</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右侧表单区域 -->
      <div class="form-section">
        <div class="form-container">
          <div class="form-header">
            <h2>欢迎注册</h2>
            <p>开启你的陪伴之旅</p>
          </div>
          
          <el-form 
            ref="registerForm"
            :model="formData"
            :rules="formRules"
            class="register-form"
            size="large"
          >
            <el-form-item prop="userName">
              <el-input
                v-model="formData.userName"
                placeholder="请输入用户名"
                prefix-icon="User"
                clearable
              />
            </el-form-item>
            
            <el-form-item prop="phone">
              <el-input
                v-model="formData.phone"
                placeholder="请输入手机号"
                prefix-icon="Phone"
                clearable
              />
            </el-form-item>
            
            <el-form-item prop="email">
              <el-input
                v-model="formData.email"
                placeholder="请输入邮箱地址"
                prefix-icon="Message"
                clearable
              />
            </el-form-item>
            
            <el-form-item prop="verificationCode">
              <div class="code-input">
                <el-input
                  v-model="formData.verificationCode"
                  placeholder="请输入验证码"
                  prefix-icon="ChatDotRound"
                />
                <el-button 
                  type="primary" 
                  :disabled="codeCountdown > 0"
                  @click="sendCode"
                  class="code-btn"
                >
                  {{ codeCountdown > 0 ? `${codeCountdown}s后重发` : '发送验证码' }}
                </el-button>
              </div>
            </el-form-item>
            
            <el-form-item prop="password">
              <el-input
                v-model="formData.password"
                type="password"
                placeholder="请输入密码"
                prefix-icon="Lock"
                show-password
              />
            </el-form-item>
            
            <el-form-item prop="confirmPassword">
              <el-input
                v-model="formData.confirmPassword"
                type="password"
                placeholder="请确认密码"
                prefix-icon="Lock"
                show-password
              />
            </el-form-item>
            
            <el-form-item prop="inviteCode">
              <el-input
                v-model="formData.inviteCode"
                placeholder="邀请码(可选)"
                prefix-icon="Present"
                clearable
              />
            </el-form-item>
            
            <el-form-item prop="agreeTerms">
              <el-checkbox v-model="formData.agreeTerms">
                我已阅读并同意
                <a href="#" @click.prevent="showTerms">《用户协议》</a>
                和
                <a href="#" @click.prevent="showPrivacy">《隐私政策》</a>
              </el-checkbox>
            </el-form-item>
            
            <el-form-item>
              <el-button 
                type="primary" 
                size="large"
                :loading="loading"
                @click="handleRegister"
                class="register-btn"
              >
                {{ loading ? '注册中...' : '立即注册' }}
              </el-button>
            </el-form-item>
          </el-form>
          
          <div class="form-footer">
            <p>已有账号？<router-link to="/login">立即登录</router-link></p>
            
            <div class="social-login">
              <div class="divider">
                <span>或使用第三方账号注册</span>
              </div>
              <div class="social-buttons">
                <el-button class="social-btn wechat" @click="socialRegister('wechat')">
                  <span class="icon">📱</span>
                  微信注册
                </el-button>
                <el-button class="social-btn qq" @click="socialRegister('qq')">
                  <span class="icon">🐧</span>
                  QQ注册
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { authAPI } from '@/api/auth'

const router = useRouter()

// 表单数据
const formData = reactive({
  userName: '',
  phone: '',
  email: '',
  verificationCode: '',
  password: '',
  confirmPassword: '',
  inviteCode: '',
  agreeTerms: false
})

// 状态
const loading = ref(false)
const codeCountdown = ref(0)
const sendingCode = ref(false)

// 表单验证规则
const formRules = {
  userName: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名长度在2-20个字符', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  verificationCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== formData.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  agreeTerms: [
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error('请同意用户协议和隐私政策'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}

// 发送验证码
const sendCode = async () => {
  if (!formData.phone || !/^1[3-9]\d{9}$/.test(formData.phone)) {
    ElMessage.error('请输入正确的手机号')
    return
  }
  
  if (codeCountdown.value > 0) {
    return
  }
  
  try {
    sendingCode.value = true
    
    // 调用发送验证码API
    const response = await authAPI.sendSms(formData.phone, 'register')
    
    if (response.success) {
      ElMessage.success('验证码发送成功，请查收短信')
      
      // 开始倒计时
      codeCountdown.value = 60
      const timer = setInterval(() => {
        codeCountdown.value--
        if (codeCountdown.value <= 0) {
          clearInterval(timer)
        }
      }, 1000)
    } else {
      ElMessage.error(response.message || '验证码发送失败')
    }
  } catch (error) {
    console.error('发送验证码错误:', error)
    if (error.response?.data?.message) {
      ElMessage.error(error.response.data.message)
    } else {
      ElMessage.error('验证码发送失败，请稍后重试')
    }
  } finally {
    sendingCode.value = false
  }
}

// 注册处理
const registerForm = ref()
const handleRegister = async () => {
  if (!registerForm.value) return
  
  try {
    const valid = await registerForm.value.validate()
    if (!valid) return
    
    loading.value = true
    
    // 调用注册API
    const response = await authAPI.register({
      username: formData.userName,
      phone: formData.phone,
      email: formData.email,
      password: formData.password,
      confirmPassword: formData.confirmPassword,
      verificationCode: formData.verificationCode,
      inviteCode: formData.inviteCode
    })
    
    if (response.success) {
      ElMessage.success('注册成功！欢迎加入Accompany')
      // 注册成功后跳转到登录页
      router.push('/login')
    } else {
      ElMessage.error(response.message || '注册失败')
    }
  } catch (error) {
    console.error('注册错误:', error)
    if (error.response?.data?.message) {
      ElMessage.error(error.response.data.message)
    } else {
      ElMessage.error('注册失败，请检查网络连接')
    }
  } finally {
    loading.value = false
  }
}

// 第三方注册
const socialRegister = (provider) => {
  ElMessage.info(`${provider}注册功能开发中...`)
}

// 显示协议
const showTerms = () => {
  ElMessage.info('用户协议页面开发中...')
}

const showPrivacy = () => {
  ElMessage.info('隐私政策页面开发中...')
}
</script>

<style scoped>
.register-page {
  display: flex;
  min-height: 100vh;
  width: 100%;
  max-width: 1200px;
  background: white;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
}

/* 页面容器 */
.page-container {
  display: flex;
  width: 100%;
  min-height: 100vh;
  background: white;
}

/* 左侧品牌区域 */
.brand-section {
  flex: 1;
  min-width: 350px;
  background: linear-gradient(135deg, #FF6B6B 0%, #45B7D1 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.brand-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
}

.brand-content {
  position: relative;
  z-index: 2;
  padding: 40px;
  width: 100%;
  max-width: 500px;
  color: white;
  text-align: center;
}

.logo h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 12px;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
  line-height: 1.1;
  letter-spacing: 1px;
  white-space: nowrap;
  overflow: visible;
}

.slogan {
  font-size: 1.1rem;
  margin-bottom: 50px;
  opacity: 0.9;
}

.features {
  margin-bottom: 50px;
  text-align: left;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 28px;
  text-align: left;
}

.feature-icon {
  font-size: 1.8rem;
  margin-right: 16px;
  flex-shrink: 0;
  margin-top: 2px;
}

.feature-text h3 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 6px;
  line-height: 1.3;
}

.feature-text p {
  opacity: 0.9;
  line-height: 1.4;
  font-size: 0.9rem;
}

.stats {
  display: flex;
  gap: 30px;
  justify-content: center;
  text-align: center;
}

.stat {
  text-align: center;
}

.number {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 6px;
  line-height: 1.2;
}

.label {
  opacity: 0.8;
  font-size: 0.9rem;
  line-height: 1.3;
}

/* 右侧表单区域 */
.form-section {
  flex: 1;
  min-width: 350px;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: 100px 40px 40px 40px;
  background: white;
  overflow-y: auto;
}

.form-container {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  padding-top: 40px;
}

.form-header {
  text-align: center;
  margin-bottom: 32px;
  padding-top: 30px;
}

.form-header h2 {
  font-size: 1.875rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 12px;
  line-height: 1.2;
}

.form-header p {
  color: #6b7280;
  font-size: 1rem;
  line-height: 1.4;
}

.register-form {
  margin-bottom: 24px;
}

.code-input {
  display: flex;
  gap: 12px;
}

.code-input .el-input {
  flex: 1;
}

.code-btn {
  flex-shrink: 0;
  min-width: 100px;
  height: 40px;
}

.register-btn {
  width: 100%;
  height: 44px;
  font-size: 1rem;
  font-weight: 600;
  margin-top: 8px;
}

.form-footer {
  text-align: center;
}

.form-footer p {
  color: #6b7280;
  margin-bottom: 20px;
  font-size: 0.9rem;
}

.form-footer a {
  color: #FF6B6B;
  text-decoration: none;
  font-weight: 600;
}

.form-footer a:hover {
  color: #E55555;
}

.social-login {
  margin-top: 32px;
}

.divider {
  position: relative;
  margin: 24px 0;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e5e7eb;
}

.divider span {
  background: white;
  padding: 0 16px;
  color: #9ca3af;
  font-size: 0.875rem;
}

.social-buttons {
  display: flex;
  gap: 12px;
}

.social-btn {
  flex: 1;
  height: 48px;
  border: 1px solid #e5e7eb;
  background: white;
  color: #374151;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s;
}

.social-btn:hover {
  border-color: #d1d5db;
  background: #f9fafb;
}

.social-btn .icon {
  font-size: 1.125rem;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .register-page {
    max-width: 100%;
    box-shadow: none;
  }
  
  .page-container {
    flex-direction: column;
    min-height: auto;
  }
  
  .brand-section {
    flex: none;
    min-width: auto;
    height: 280px;
  }
  
  .brand-content {
    padding: 30px 20px;
  }
  
  .logo h1 {
    font-size: 2.5rem;
  }
  
  .features {
    display: none;
  }
  
  .stats {
    justify-content: center;
  }
  
  .form-section {
    flex: none;
    min-width: auto;
    padding: 60px 20px 30px 20px;
    min-height: auto;
    align-items: flex-start;
  }
  
  .form-container {
    margin-top: 10px;
    padding-top: 30px;
  }
  
  .form-header {
    padding-top: 20px;
  }
}

@media (max-width: 768px) {
  .brand-section {
    height: 250px;
  }
  
  .brand-content {
    padding: 20px;
  }
  
  .logo h1 {
    font-size: 2rem;
  }
  
  .stats {
    gap: 20px;
  }
  
  .form-section {
    padding: 40px 20px;
  }
  
  .form-container {
    padding-top: 20px;
  }
  
  .form-header {
    margin-bottom: 24px;
    padding-top: 15px;
  }
  
  .social-buttons {
    flex-direction: column;
  }
}

@media (max-width: 500px) {
  .register-page {
    padding: 0;
    margin: 0;
  }
  
  .page-container {
    min-height: 100vh;
  }
  
  .brand-section {
    height: 200px;
  }
  
  .brand-content {
    padding: 15px;
  }
  
  .logo h1 {
    font-size: 1.8rem;
  }
  
  .form-section {
    padding: 20px 15px;
  }
  
  .form-container {
    max-width: 100%;
  }
}

/* Element Plus 样式覆盖 */
:deep(.el-form-item) {
  margin-bottom: 24px;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #d1d5db;
}

:deep(.el-input__wrapper.is-focus) {
  border-color: #FF6B6B;
  box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
}

:deep(.el-button--primary) {
  background-color: #FF6B6B;
  border-color: #FF6B6B;
}

:deep(.el-button--primary:hover) {
  background-color: #E55555;
  border-color: #E55555;
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #FF6B6B;
  border-color: #FF6B6B;
}
</style> 