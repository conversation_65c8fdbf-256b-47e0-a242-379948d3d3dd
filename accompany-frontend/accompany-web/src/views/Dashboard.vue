<template>
  <div class="dashboard-page">
    <div class="dashboard-header">
      <div class="container">
        <h1>欢迎来到 Accompany</h1>
        <p>连接人心，传递温暖的陪伴平台</p>
        <el-button type="primary" @click="logout">退出登录</el-button>
      </div>
    </div>
    
    <div class="dashboard-content">
      <div class="container">
        <div class="welcome-card card">
          <h2>🎉 注册成功！</h2>
          <p>感谢您加入 Accompany 平台，开启您的陪伴之旅。</p>
          <p>更多功能正在开发中，敬请期待...</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

const logout = async () => {
  try {
    await userStore.logout()
    ElMessage.success('已退出登录')
    router.push('/login')
  } catch (error) {
    ElMessage.error('退出登录失败')
  }
}
</script>

<style scoped>
.dashboard-page {
  min-height: 100vh;
  background: var(--color-background);
}

.dashboard-header {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-accent) 100%);
  color: var(--color-white);
  padding: var(--spacing-xxl) 0;
  text-align: center;
}

.dashboard-header h1 {
  margin-bottom: var(--spacing-md);
}

.dashboard-header p {
  margin-bottom: var(--spacing-xl);
  opacity: 0.9;
}

.dashboard-content {
  padding: var(--spacing-xxl) 0;
}

.welcome-card {
  max-width: 600px;
  margin: 0 auto;
  text-align: center;
}

.welcome-card h2 {
  color: var(--color-primary);
  margin-bottom: var(--spacing-lg);
}

.welcome-card p {
  margin-bottom: var(--spacing-md);
  color: var(--color-text-secondary);
}
</style> 