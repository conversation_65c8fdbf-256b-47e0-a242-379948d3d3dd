/* Accompany 全局样式 - 基于UI设计规范 */

:root {
  /* 品牌主色 */
  --color-primary: #FF6B6B; /* 温暖珊瑚红 */
  --color-primary-light: #FF8A8A;
  --color-primary-dark: #E55555;
  
  /* 辅助主色 */
  --color-secondary: #4ECDC4; /* 清新薄荷绿 */
  --color-accent: #45B7D1; /* 天空蓝 */
  
  /* 功能色彩 */
  --color-success: #96CEB4;
  --color-warning: #FFEAA7;
  --color-error: #FD79A8;
  --color-info: #74B9FF;
  
  /* 中性色彩 */
  --color-text-primary: #2D3436;
  --color-text-secondary: #636E72;
  --color-text-tertiary: #B2BEC3;
  --color-border: #DDD6FE;
  --color-background: #FAFAFB;
  --color-white: #FFFFFF;
  
  /* 字体大小 */
  --font-size-h1: 24px;
  --font-size-h2: 20px;
  --font-size-h3: 18px;
  --font-size-h4: 16px;
  --font-size-body: 14px;
  --font-size-small: 12px;
  
  /* 行高 */
  --line-height-h1: 32px;
  --line-height-h2: 28px;
  --line-height-h3: 24px;
  --line-height-h4: 22px;
  --line-height-body: 20px;
  --line-height-small: 18px;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 24px;
  --spacing-xxl: 32px;
  
  /* 圆角 */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  
  /* 阴影 */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* 重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.5;
  color: var(--color-text-primary);
  background-color: var(--color-background);
  height: 100%;
  overflow-x: hidden; /* 防止水平滚动条 */
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: var(--font-size-body);
  line-height: var(--line-height-body);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
}

#app {
  min-height: 100vh;
  width: 100%;
}

/* 标题样式 */
h1 {
  font-size: var(--font-size-h1);
  line-height: var(--line-height-h1);
  font-weight: 700;
}

h2 {
  font-size: var(--font-size-h2);
  line-height: var(--line-height-h2);
  font-weight: 700;
}

h3 {
  font-size: var(--font-size-h3);
  line-height: var(--line-height-h3);
  font-weight: 600;
}

h4 {
  font-size: var(--font-size-h4);
  line-height: var(--line-height-h4);
  font-weight: 600;
}

/* 链接样式 */
a {
  color: var(--color-accent);
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: var(--color-primary);
}

/* 按钮基础样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-h4);
  font-weight: 600;
  line-height: var(--line-height-h4);
  border: none;
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  min-height: 44px;
}

.btn-primary {
  background-color: var(--color-primary);
  color: var(--color-white);
}

.btn-primary:hover {
  background-color: var(--color-primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: transparent;
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
}

.btn-secondary:hover {
  background-color: var(--color-primary);
  color: var(--color-white);
}

/* 输入框样式 */
.input {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: var(--font-size-body);
  line-height: var(--line-height-body);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-md);
  background-color: var(--color-white);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  min-height: 44px;
}

.input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
}

.input::placeholder {
  color: var(--color-text-tertiary);
}

/* 卡片样式 */
.card {
  background-color: var(--color-white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-xl);
}

/* 页面容器 - 优化布局 */
.container {
  max-width: 1400px; /* 增加最大宽度 */
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  width: 100%;
}

/* 页面内容区域 */
.page-content {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* 居中布局 - 优化空白问题 */
.center-layout {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: var(--spacing-lg); /* 减少内边距 */
  width: 100%;
  box-sizing: border-box;
}

/* 全屏布局容器 */
.fullscreen-container {
  width: 100vw;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
}

/* 表单样式 */
.form-group {
  margin-bottom: var(--spacing-xl);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
  color: var(--color-text-primary);
}

.form-error {
  margin-top: var(--spacing-sm);
  font-size: var(--font-size-small);
  color: var(--color-error);
}

/* 响应式设计 - 优化各种屏幕尺寸 */
@media (max-width: 1400px) {
  .container {
    max-width: 1200px;
  }
}

@media (max-width: 1200px) {
  .container {
    max-width: 100%;
    padding: 0 var(--spacing-xl);
  }
  
  .center-layout {
    padding: var(--spacing-md);
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-md);
  }
  
  .center-layout {
    padding: var(--spacing-sm);
  }
  
  .card {
    padding: var(--spacing-lg);
  }
}

@media (max-width: 480px) {
  .center-layout {
    padding: var(--spacing-xs);
  }
  
  .container {
    padding: 0 var(--spacing-sm);
  }
}

/* 超宽屏优化 */
@media (min-width: 1920px) {
  .container {
    max-width: 1600px;
  }
}

/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* Element Plus 自定义样式 */
.el-button--primary {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.el-button--primary:hover {
  background-color: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
}

.el-input__wrapper {
  border-radius: var(--border-radius-md);
}

.el-input__wrapper.is-focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(255, 107, 107, 0.1);
} 