import axios from 'axios'

// 配置API基础URL
const API_BASE_URL = 'http://localhost:8080/api/auth'

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 添加token到请求头
    const token = localStorage.getItem('accessToken')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    console.error('API请求失败:', error)
    if (error.response?.status === 401) {
      // token过期，清除本地存储并跳转到登录页
      localStorage.removeItem('accessToken')
      localStorage.removeItem('refreshToken')
      localStorage.removeItem('userInfo')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// 认证API
export const authAPI = {
  // 用户注册
  register(data) {
    return apiClient.post('/register', {
      userName: data.username,
      phone: data.phone,
      email: data.email,
      password: data.password,
      confirmPassword: data.confirmPassword,
      verificationCode: data.verificationCode,
      inviteCode: data.inviteCode || null
    })
  },

  // 用户登录
  login(data) {
    return apiClient.post('/login', {
      account: data.account,
      password: data.password,
      loginType: 1, // 1-密码登录
      deviceType: 3, // 3-Web
      rememberMe: data.rememberMe || false
    })
  },

  // 发送短信验证码
  sendSms(phone, type = 'register') {
    return apiClient.post('/sms/send', {
      phone,
      type
    })
  },

  // 检查用户名是否可用
  checkUsername(username) {
    return apiClient.get('/check/username', {
      params: { username }
    })
  },

  // 检查手机号是否可用
  checkPhone(phone) {
    return apiClient.get('/check/phone', {
      params: { phone }
    })
  },

  // 检查邮箱是否可用
  checkEmail(email) {
    return apiClient.get('/check/email', {
      params: { email }
    })
  },

  // 刷新Token
  refreshToken(refreshToken) {
    return apiClient.post('/refresh', null, {
      params: { refreshToken }
    })
  },

  // 用户登出
  logout() {
    return apiClient.post('/logout')
  }
}

export default authAPI 