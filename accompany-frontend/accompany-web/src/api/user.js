import request from './request'

/**
 * 用户相关API
 */
export const userApi = {
  /**
   * 用户注册
   * @param {Object} data 注册数据
   * @param {string} data.userName 用户名
   * @param {string} data.password 密码
   * @param {string} data.email 邮箱
   * @param {string} data.phone 手机号
   * @param {string} data.verificationCode 验证码
   * @param {string} data.inviteCode 邀请码(可选)
   */
  register(data) {
    return request.post('/users/register', data)
  },

  /**
   * 用户登录
   * @param {Object} data 登录数据
   * @param {string} data.username 用户名/手机号/邮箱
   * @param {string} data.password 密码
   * @param {string} data.deviceId 设备ID
   * @param {number} data.deviceType 设备类型
   * @param {boolean} data.rememberMe 记住登录
   */
  login(data) {
    return request.post('/users/login', data)
  },

  /**
   * 发送验证码
   * @param {string} phone 手机号
   * @param {string} purpose 用途: register|login|reset_password|bind_phone
   */
  sendVerificationCode(phone, purpose = 'register') {
    return request.post('/users/send-code', { phone, purpose })
  },

  /**
   * 验证验证码
   * @param {string} phone 手机号
   * @param {string} code 验证码
   * @param {string} purpose 用途
   */
  verifyCode(phone, code, purpose) {
    return request.post('/users/verify-code', { phone, code, purpose })
  },

  /**
   * 检查用户名是否可用
   * @param {string} userName 用户名
   */
  checkUserName(userName) {
    return request.get('/users/check-username', { params: { userName } })
  },

  /**
   * 检查邮箱是否可用
   * @param {string} email 邮箱
   */
  checkEmail(email) {
    return request.get('/users/check-email', { params: { email } })
  },

  /**
   * 检查手机号是否可用
   * @param {string} phone 手机号
   */
  checkPhone(phone) {
    return request.get('/users/check-phone', { params: { phone } })
  },

  /**
   * 获取当前用户信息
   */
  getCurrentUser() {
    return request.get('/users/profile')
  },

  /**
   * 登出
   */
  logout() {
    return request.post('/users/logout')
  },
} 