# Accompany Web Frontend

基于Vue 3 + Vite + TypeScript + Element Plus构建的现代化前端应用。

## 🚀 技术栈

- **Vue 3** - 渐进式JavaScript框架
- **Vite** - 快速构建工具
- **TypeScript** - 类型安全的JavaScript超集
- **Element Plus** - Vue 3组件库
- **Pinia** - Vue状态管理
- **Vue Router** - 官方路由管理器
- **Axios** - HTTP客户端

## 🎨 设计规范

项目严格遵循Accompany UI设计规范：

- **品牌色彩**：温暖珊瑚红 (#FF6B6B) + 清新薄荷绿 (#4ECDC4) + 天空蓝 (#45B7D1)
- **设计风格**：温暖、友好、简约现代、扁平化设计
- **字体规范**：支持中英文字体，响应式字体大小
- **组件规范**：统一的按钮、输入框、卡片等组件样式

## 📁 项目结构

```
src/
├── api/                  # API接口层
│   ├── request.js       # Axios封装
│   └── user.js          # 用户相关API
├── components/          # 通用组件
├── router/              # 路由配置
├── stores/              # Pinia状态管理
│   └── user.js         # 用户状态管理
├── styles/              # 样式文件
│   └── global.css      # 全局样式
├── views/               # 页面组件
│   ├── Register.vue    # 注册页面
│   ├── Login.vue       # 登录页面
│   └── Dashboard.vue   # 仪表板
├── App.vue             # 根组件
└── main.js             # 入口文件
```

## 🛠 开发环境安装

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
pnpm dev
```

访问 [http://localhost:3000](http://localhost:3000)

### 构建生产版本

```bash
npm run build
```

### 预览生产构建

```bash
npm run preview
```

## 🔧 开发配置

### 代理配置

开发环境已配置API代理，自动将 `/api` 请求代理到后端服务 `http://localhost:8081`

### 自动导入

项目配置了自动导入功能：
- Vue相关API自动导入
- Element Plus组件自动导入
- 路由和状态管理自动导入

## 📄 页面功能

### ✅ 已实现功能

1. **注册页面** (`/register`)
   - 用户名验证（实时检查可用性）
   - 手机号验证（实时检查可用性）
   - 邮箱验证（实时检查可用性）
   - 短信验证码发送（带倒计时）
   - 密码强度验证
   - 表单验证和提交
   - 响应式设计

2. **登录页面** (`/login`)
   - 用户名/手机号/邮箱登录
   - 记住登录状态
   - 响应式设计

3. **仪表板** (`/dashboard`)
   - 欢迎页面
   - 退出登录

### 🚧 开发中功能

- 用户个人资料管理
- 服务发布和搜索
- 即时通讯
- 支付系统
- 等级和积分系统

## 🎯 核心特性

### 状态管理

使用Pinia进行状态管理，包括：
- 用户登录状态
- 用户信息缓存
- Token管理
- 自动登录检查

### 请求拦截

统一的请求和响应处理：
- 自动添加Token
- 错误统一处理
- 请求追踪ID
- 超时处理

### 路由守卫

- 登录状态检查
- 页面标题设置
- 权限控制

### 响应式设计

- 移动端适配
- 断点设计
- 弹性布局

## 🔄 API接口

### 用户相关API

- `POST /api/users/register` - 用户注册
- `POST /api/users/login` - 用户登录
- `POST /api/users/logout` - 用户登出
- `POST /api/users/send-code` - 发送验证码
- `GET /api/users/check-username` - 检查用户名
- `GET /api/users/check-email` - 检查邮箱
- `GET /api/users/check-phone` - 检查手机号
- `GET /api/users/profile` - 获取用户信息

## 🎨 样式规范

### CSS自定义属性

项目使用CSS变量统一管理设计token：

```css
:root {
  /* 品牌色彩 */
  --color-primary: #FF6B6B;
  --color-secondary: #4ECDC4;
  --color-accent: #45B7D1;
  
  /* 字体大小 */
  --font-size-h1: 24px;
  --font-size-body: 14px;
  
  /* 间距 */
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  
  /* 圆角 */
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
}
```

### 组件样式

- 使用Scoped CSS避免样式污染
- Element Plus主题定制
- 响应式断点设计

## 🛡 最佳实践

### 代码规范

- 使用Composition API
- TypeScript类型安全
- 组件拆分和复用
- 状态管理规范

### 性能优化

- 路由懒加载
- 组件按需导入
- 图片资源优化
- 构建优化配置

### 安全考虑

- XSS防护
- CSRF防护
- Token安全存储
- 输入验证

## 🚀 部署

### 构建优化

项目已配置生产环境优化：
- 代码分割
- Tree Shaking
- 资源压缩
- 缓存策略

### 环境变量

支持多环境配置：
- `.env.development` - 开发环境
- `.env.production` - 生产环境

## 📱 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 🤝 贡献指南

1. Fork项目
2. 创建特性分支
3. 提交变更
4. 推送到分支
5. 提交Pull Request

## �� 许可证

本项目遵循MIT许可证
