/**
 * Accompany陪伴服务平台微信小程序
 * 
 * 基于微信小程序框架开发的轻量级陪伴服务应用
 * 支持微信原生API，提供流畅的用户体验
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

// 引入工具类和配置
const config = require('./config/index.js')
const utils = require('./utils/index.js')
const api = require('./api/index.js')

App({
  /**
   * 小程序全局数据
   */
  globalData: {
    // 用户信息
    userInfo: null,
    isLoggedIn: false,
    userType: 'normal', // normal|companion|admin
    
    // 系统信息
    systemInfo: null,
    statusBarHeight: 0,
    navigationBarHeight: 44,
    safeAreaBottom: 0,
    
    // 位置信息
    location: null,
    locationAuthorized: false,
    
    // 网络状态
    networkType: 'unknown',
    isConnected: true,
    
    // 应用配置
    config: config,
    baseURL: config.baseURL,
    version: '1.0.0'
  },

  /**
   * 小程序初始化
   */
  onLaunch(options) {
    console.log('🚀 Accompany小程序启动', options)
    
    // 初始化系统信息
    this.initSystemInfo()
    
    // 初始化网络监听
    this.initNetworkListener()
    
    // 检查登录状态
    this.checkLoginStatus()
    
    // 处理启动场景
    this.handleLaunchScene(options)
    
    // 初始化统计上报
    this.initAnalytics(options)
  },

  /**
   * 小程序显示
   */
  onShow(options) {
    console.log('📱 小程序显示', options)
    
    // 更新网络状态
    this.updateNetworkStatus()
    
    // 刷新用户状态
    if (this.globalData.isLoggedIn) {
      this.refreshUserStatus()
    }
    
    // 处理场景值
    this.handleShowScene(options)
  },

  /**
   * 小程序隐藏
   */
  onHide() {
    console.log('📱 小程序隐藏')
    
    // 保存应用状态
    this.saveAppState()
    
    // 更新用户在线状态
    if (this.globalData.isLoggedIn) {
      this.updateUserOnlineStatus(false)
    }
  },

  /**
   * 小程序错误处理
   */
  onError(error) {
    console.error('❌ 小程序错误:', error)
    
    // 错误上报
    this.reportError(error)
    
    // 显示用户友好的错误提示
    wx.showToast({
      title: '系统繁忙，请稍后再试',
      icon: 'none'
    })
  },

  // ========== 初始化方法 ==========

  /**
   * 初始化系统信息
   */
  initSystemInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync()
      this.globalData.systemInfo = systemInfo
      
      // 计算状态栏高度和安全区域
      this.globalData.statusBarHeight = systemInfo.statusBarHeight || 20
      this.globalData.safeAreaBottom = systemInfo.safeArea 
        ? systemInfo.windowHeight - systemInfo.safeArea.bottom 
        : 0
        
      console.log('📱 系统信息初始化完成', {
        platform: systemInfo.platform,
        version: systemInfo.version,
        statusBarHeight: this.globalData.statusBarHeight
      })
    } catch (error) {
      console.error('系统信息获取失败:', error)
    }
  },

  /**
   * 初始化网络监听
   */
  initNetworkListener() {
    // 获取当前网络状态
    wx.getNetworkType({
      success: (res) => {
        this.globalData.networkType = res.networkType
        this.globalData.isConnected = res.networkType !== 'none'
      }
    })

    // 监听网络状态变化
    wx.onNetworkStatusChange((res) => {
      this.globalData.networkType = res.networkType
      this.globalData.isConnected = res.isConnected
      
      if (res.isConnected) {
        console.log('🟢 网络已连接:', res.networkType)
        // 网络恢复后重新同步数据
        this.syncDataOnNetworkRestore()
      } else {
        console.log('🔴 网络已断开')
        wx.showToast({
          title: '网络连接已断开',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const token = wx.getStorageSync('auth_token')
    const userInfo = wx.getStorageSync('user_info')
    
    if (token && userInfo) {
      // 验证token有效性
      api.validateToken(token).then(result => {
        if (result.success) {
          this.globalData.isLoggedIn = true
          this.globalData.userInfo = userInfo
          this.globalData.userType = userInfo.userType || 'normal'
          console.log('✅ 用户已登录:', userInfo.userName)
        } else {
          // token无效，清除本地数据
          this.clearUserData()
        }
      }).catch(error => {
        console.error('Token验证失败:', error)
        this.clearUserData()
      })
    } else {
      console.log('🔓 用户未登录')
    }
  },

  /**
   * 处理启动场景
   */
  handleLaunchScene(options) {
    const scene = options.scene
    const query = options.query || {}
    
    console.log('🎯 启动场景:', scene, query)
    
    switch (scene) {
      case 1001: // 发现栏小程序主入口
      case 1089: // 微信聊天主界面下拉
        // 正常启动，无需特殊处理
        break
        
      case 1007: // 单人聊天会话中的小程序消息卡片
      case 1008: // 群聊会话中的小程序消息卡片
        // 处理分享进入
        this.handleShareEntry(query)
        break
        
      case 1011: // 扫描二维码
        // 处理二维码扫描进入
        this.handleQRCodeEntry(query)
        break
        
      case 1047: // 扫描小程序码
        // 处理小程序码扫描进入
        this.handleMiniCodeEntry(query)
        break
        
      case 1103: // 发现-附近的小程序
        // 记录附近小程序发现
        this.recordNearbyDiscovery()
        break
        
      default:
        console.log('其他启动场景:', scene)
    }
  },

  // ========== 业务方法 ==========

  /**
   * 用户登录
   */
  login(userInfo) {
    this.globalData.isLoggedIn = true
    this.globalData.userInfo = userInfo
    this.globalData.userType = userInfo.userType || 'normal'
    
    // 保存到本地存储
    wx.setStorageSync('user_info', userInfo)
    
    // 更新在线状态
    this.updateUserOnlineStatus(true)
    
    console.log('✅ 用户登录成功:', userInfo.userName)
  },

  /**
   * 用户登出
   */
  logout() {
    // 更新离线状态
    this.updateUserOnlineStatus(false)
    
    // 清除用户数据
    this.clearUserData()
    
    // 跳转到登录页面
    wx.reLaunch({
      url: '/pages/login/login'
    })
    
    console.log('🔓 用户已登出')
  },

  /**
   * 清除用户数据
   */
  clearUserData() {
    this.globalData.isLoggedIn = false
    this.globalData.userInfo = null
    this.globalData.userType = 'normal'
    
    // 清除本地存储
    wx.removeStorageSync('auth_token')
    wx.removeStorageSync('user_info')
    wx.removeStorageSync('refresh_token')
  },

  /**
   * 获取位置信息
   */
  getLocation() {
    return new Promise((resolve, reject) => {
      // 先检查授权状态
      wx.getSetting({
        success: (res) => {
          if (res.authSetting['scope.userLocation']) {
            // 已授权，直接获取位置
            this.doGetLocation(resolve, reject)
          } else {
            // 请求授权
            wx.authorize({
              scope: 'scope.userLocation',
              success: () => {
                this.doGetLocation(resolve, reject)
              },
              fail: () => {
                // 授权失败，引导用户手动开启
                this.showLocationAuthDialog(resolve, reject)
              }
            })
          }
        }
      })
    })
  },

  /**
   * 执行位置获取
   */
  doGetLocation(resolve, reject) {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        this.globalData.location = {
          latitude: res.latitude,
          longitude: res.longitude,
          accuracy: res.accuracy
        }
        this.globalData.locationAuthorized = true
        resolve(this.globalData.location)
      },
      fail: (error) => {
        console.error('位置获取失败:', error)
        reject(error)
      }
    })
  },

  /**
   * 显示位置授权对话框
   */
  showLocationAuthDialog(resolve, reject) {
    wx.showModal({
      title: '需要位置权限',
      content: '为了为您推荐附近的陪伴者，需要获取您的位置信息',
      confirmText: '去设置',
      success: (res) => {
        if (res.confirm) {
          wx.openSetting({
            success: (settingRes) => {
              if (settingRes.authSetting['scope.userLocation']) {
                this.doGetLocation(resolve, reject)
              } else {
                reject(new Error('用户拒绝位置授权'))
              }
            }
          })
        } else {
          reject(new Error('用户取消位置授权'))
        }
      }
    })
  },

  /**
   * 更新用户在线状态
   */
  updateUserOnlineStatus(isOnline) {
    if (!this.globalData.isLoggedIn) return
    
    api.updateUserStatus({
      isOnline: isOnline,
      lastActiveAt: new Date().toISOString()
    }).catch(error => {
      console.error('更新用户状态失败:', error)
    })
  },

  /**
   * 刷新用户状态
   */
  refreshUserStatus() {
    if (!this.globalData.isLoggedIn) return
    
    api.getUserInfo().then(result => {
      if (result.success) {
        this.globalData.userInfo = result.data
        wx.setStorageSync('user_info', result.data)
      }
    }).catch(error => {
      console.error('刷新用户状态失败:', error)
    })
  },

  // ========== 工具方法 ==========

  /**
   * 显示加载提示
   */
  showLoading(title = '加载中...') {
    wx.showLoading({
      title: title,
      mask: true
    })
  },

  /**
   * 隐藏加载提示
   */
  hideLoading() {
    wx.hideLoading()
  },

  /**
   * 显示成功提示
   */
  showSuccess(title) {
    wx.showToast({
      title: title,
      icon: 'success',
      duration: 2000
    })
  },

  /**
   * 显示错误提示
   */
  showError(title) {
    wx.showToast({
      title: title,
      icon: 'none',
      duration: 3000
    })
  },

  /**
   * 网络恢复后数据同步
   */
  syncDataOnNetworkRestore() {
    if (this.globalData.isLoggedIn) {
      // 同步用户信息
      this.refreshUserStatus()
      
      // 同步消息状态
      this.syncMessageStatus()
    }
  },

  /**
   * 保存应用状态
   */
  saveAppState() {
    const appState = {
      lastActiveTime: Date.now(),
      location: this.globalData.location
    }
    wx.setStorageSync('app_state', appState)
  },

  /**
   * 错误上报
   */
  reportError(error) {
    // TODO: 实现错误上报逻辑
    console.log('📊 错误上报:', error)
  },

  /**
   * 初始化统计
   */
  initAnalytics(options) {
    // TODO: 初始化数据统计
    console.log('📊 统计初始化')
  },

  /**
   * 处理分享进入
   */
  handleShareEntry(query) {
    console.log('📤 分享进入:', query)
    // TODO: 处理分享参数
  },

  /**
   * 处理二维码进入
   */
  handleQRCodeEntry(query) {
    console.log('📷 二维码进入:', query)
    // TODO: 处理二维码参数
  },

  /**
   * 处理小程序码进入
   */
  handleMiniCodeEntry(query) {
    console.log('🎯 小程序码进入:', query)
    // TODO: 处理小程序码参数
  },

  /**
   * 记录附近发现
   */
  recordNearbyDiscovery() {
    console.log('📍 附近发现记录')
    // TODO: 记录附近发现统计
  },

  /**
   * 处理显示场景
   */
  handleShowScene(options) {
    // TODO: 处理小程序显示场景
  },

  /**
   * 同步消息状态
   */
  syncMessageStatus() {
    // TODO: 同步消息状态
  },

  /**
   * 更新网络状态
   */
  updateNetworkStatus() {
    wx.getNetworkType({
      success: (res) => {
        this.globalData.networkType = res.networkType
        this.globalData.isConnected = res.networkType !== 'none'
      }
    })
  }
}) 