# ========== Common Messages ==========
common.success=Operation successful
common.param.error=Parameter error
common.unauthorized=Unauthorized, please login first
common.forbidden=Insufficient permissions
common.not.found=Resource not found
common.method.not.allowed=Request method not allowed
common.request.timeout=Request timeout
common.conflict=Resource conflict
common.unsupported.media.type=Unsupported media type
common.too.many.requests=Too many requests, please try again later
common.error=System error
common.not.implemented=Feature not implemented
common.service.unavailable=Service unavailable
common.gateway.timeout=Gateway timeout

# ========== Authentication & Authorization Messages ==========
auth.token.expired=Token expired
auth.token.invalid=Invalid token
auth.token.missing=Token missing
auth.token.format.error=Token format error
auth.token.signature.error=Token signature error

auth.login.failed=Login failed
auth.login.credentials.error=Invalid username or password
auth.login.account.disabled=Account disabled
auth.login.account.locked=Account locked
auth.login.too.many.attempts=Too many login attempts
auth.login.captcha.error=Captcha error
auth.login.verification.code.error=Verification code error
auth.login.verification.code.expired=Verification code expired

auth.account.locked=Account locked
auth.account.disabled=Account disabled
auth.account.not.activated=Account not activated
auth.account.suspended=Account suspended
auth.account.expired=Account expired

auth.permission.denied=Permission denied
auth.role.not.found=Role not found
auth.role.disabled=Role disabled
auth.permission.not.found=Permission not found

auth.session.expired=Session expired
auth.session.invalid=Invalid session
auth.session.conflict=Session conflict

auth.security.violation=Security violation
auth.ip.blacklisted=IP address blacklisted
auth.device.not.trusted=Device not trusted
auth.suspicious.activity=Suspicious activity detected

auth.third.party.auth.failed=Third-party authentication failed
auth.oauth.token.invalid=Invalid OAuth token
auth.oauth.token.expired=OAuth token expired
auth.oauth.provider.error=OAuth provider error

auth.mfa.required=Multi-factor authentication required
auth.mfa.code.error=Multi-factor authentication code error
auth.mfa.code.expired=Multi-factor authentication code expired
auth.mfa.not.enabled=Multi-factor authentication not enabled

# ========== User Module Messages ==========
user.not.found=User not found
user.already.exists=User already exists
user.username.taken=Username already taken
user.email.taken=Email already registered
user.phone.taken=Phone number already registered
user.info.incomplete=User information incomplete
user.profile.not.found=User profile not found
user.status.invalid=Invalid user status

user.password.error=Password error
user.old.password.error=Old password error
user.password.too.weak=Password too weak
user.password.expired=Password expired
user.password.reset.failed=Password reset failed
user.password.change.failed=Password change failed

user.verification.code.error=Verification code error
user.verification.code.expired=Verification code expired
user.verification.code.send.failed=Verification code send failed
user.verification.code.too.frequent=Verification code sent too frequently
user.verification.code.not.found=Verification code not found

user.verification.pending=Real-name verification pending
user.verification.failed=Real-name verification failed
user.id.card.invalid=Invalid ID card number
user.face.verification.failed=Face recognition verification failed
user.already.verified=User already verified
user.verification.document.invalid=Invalid verification document
user.verification.timeout=Verification timeout

user.settings.not.found=User settings not found
user.settings.update.failed=Settings update failed
user.privacy.settings.invalid=Invalid privacy settings
user.notification.settings.invalid=Invalid notification settings

user.address.not.found=Address not found
user.address.invalid=Invalid address information
user.address.limit.exceeded=Address limit exceeded
user.default.address.not.found=Default address not found

user.preference.not.found=User preference not found
user.preference.invalid=Invalid user preference
user.preference.update.failed=Preference update failed

user.statistics.not.found=User statistics not found
user.statistics.update.failed=Statistics update failed

user.relationship.not.found=User relationship not found
user.relationship.already.exists=User relationship already exists
user.relationship.invalid=Invalid user relationship
user.friend.request.failed=Friend request failed
user.friend.request.already.sent=Friend request already sent

user.tag.not.found=User tag not found
user.tag.already.exists=User tag already exists
user.tag.invalid=Invalid user tag
user.tag.limit.exceeded=Tag limit exceeded

# ========== Service Module Messages ==========
service.not.found=Service not found
service.unavailable=Service unavailable
service.disabled=Service disabled
service.expired=Service expired
service.already.exists=Service already exists
service.status.invalid=Invalid service status
service.category.not.found=Service category not found

service.time.conflict=Service time conflict
service.time.invalid=Invalid service time
service.time.expired=Service time expired
service.time.not.available=Service time not available
service.time.overlap=Service time overlap

service.area.not.supported=Service area not supported
service.area.invalid=Invalid service area
service.area.out.of.range=Service area out of range
service.area.not.covered=Service area not covered

service.quota.exceeded=Service quota exceeded
service.quota.insufficient=Insufficient service quota
service.quota.limit.reached=Service quota limit reached
service.quota.reset.failed=Service quota reset failed

service.provider.not.available=Service provider not available
service.provider.not.found=Service provider not found
service.provider.disabled=Service provider disabled
service.provider.busy=Service provider busy
service.provider.offline=Service provider offline
service.provider.rating.insufficient=Service provider rating insufficient

service.review.not.found=Service review not found
service.review.already.exists=Already reviewed
service.review.permission.denied=No permission to review
service.review.time.expired=Review time expired
service.review.invalid=Invalid review content

service.booking.failed=Service booking failed
service.booking.conflict=Service booking conflict
service.booking.expired=Service booking expired
service.booking.cancelled=Service booking cancelled
service.booking.not.found=Service booking not found

service.price.invalid=Invalid service price
service.price.changed=Service price changed
service.price.not.set=Service price not set
service.price.calculation.error=Service price calculation error

service.config.not.found=Service configuration not found
service.config.invalid=Invalid service configuration
service.config.update.failed=Service configuration update failed

service.statistics.not.found=Service statistics not found
service.statistics.update.failed=Service statistics update failed
service.statistics.invalid=Invalid service statistics 