# ========== éç¨æ¶æ¯ ==========
common.success=æä½æå
common.param.error=åæ°éè¯¯
common.unauthorized=æªææï¼è¯·åç»å½
common.forbidden=æéä¸è¶³
common.not.found=èµæºä¸å­å¨
common.method.not.allowed=è¯·æ±æ¹æ³ä¸æ¯æ
common.request.timeout=è¯·æ±è¶æ¶
common.conflict=èµæºå²çª
common.unsupported.media.type=ä¸æ¯æçåªä½ç±»å
common.too.many.requests=è¯·æ±è¿äºé¢ç¹ï¼è¯·ç¨ååè¯
common.error=ç³»ç»å¼å¸¸
common.not.implemented=åè½æªå®ç°
common.service.unavailable=æå¡ä¸å¯ç¨
common.gateway.timeout=ç½å³è¶æ¶

# ========== è®¤è¯æææ¶æ¯ ==========
auth.token.expired=Tokenå·²è¿æ
auth.token.invalid=Tokenæ æ
auth.token.missing=Tokenç¼ºå¤±
auth.token.format.error=Tokenæ ¼å¼éè¯¯
auth.token.signature.error=Tokenç­¾åéè¯¯

auth.login.failed=ç»å½å¤±è´¥
auth.login.credentials.error=ç¨æ·åæå¯ç éè¯¯
auth.login.account.disabled=è´¦å·å·²è¢«ç¦ç¨
auth.login.account.locked=è´¦å·å·²è¢«éå®
auth.login.too.many.attempts=ç»å½å°è¯æ¬¡æ°è¿å¤
auth.login.captcha.error=éªè¯ç éè¯¯
auth.login.verification.code.error=éªè¯ç éè¯¯
auth.login.verification.code.expired=éªè¯ç å·²è¿æ

auth.account.locked=è´¦å·å·²è¢«éå®
auth.account.disabled=è´¦å·å·²è¢«ç¦ç¨
auth.account.not.activated=è´¦å·æªæ¿æ´»
auth.account.suspended=è´¦å·å·²è¢«æå
auth.account.expired=è´¦å·å·²è¿æ

auth.permission.denied=æéä¸è¶³
auth.role.not.found=è§è²ä¸å­å¨
auth.role.disabled=è§è²å·²è¢«ç¦ç¨
auth.permission.not.found=æéä¸å­å¨

auth.session.expired=ä¼è¯å·²è¿æ
auth.session.invalid=ä¼è¯æ æ
auth.session.conflict=ä¼è¯å²çª

auth.security.violation=å®å¨è¿è§
auth.ip.blacklisted=IPå°åå·²è¢«æé»
auth.device.not.trusted=è®¾å¤æªåä¿¡ä»»
auth.suspicious.activity=æ£æµå°å¯çæ´»å¨

auth.third.party.auth.failed=ç¬¬ä¸æ¹è®¤è¯å¤±è´¥
auth.oauth.token.invalid=OAuth Tokenæ æ
auth.oauth.token.expired=OAuth Tokenå·²è¿æ
auth.oauth.provider.error=OAuthæä¾åéè¯¯

auth.mfa.required=éè¦å¤å ç´ è®¤è¯
auth.mfa.code.error=å¤å ç´ è®¤è¯ç éè¯¯
auth.mfa.code.expired=å¤å ç´ è®¤è¯ç å·²è¿æ
auth.mfa.not.enabled=å¤å ç´ è®¤è¯æªå¯ç¨

# ========== ç¨æ·æ¨¡åæ¶æ¯ ==========
user.not.found=ç¨æ·ä¸å­å¨
user.already.exists=ç¨æ·å·²å­å¨
user.username.taken=ç¨æ·åå·²è¢«å ç¨
user.email.taken=é®ç®±å·²è¢«æ³¨å
user.phone.taken=ææºå·å·²è¢«æ³¨å
user.info.incomplete=ç¨æ·ä¿¡æ¯ä¸å®æ´
user.profile.not.found=ç¨æ·æ¡£æ¡ä¸å­å¨
user.status.invalid=ç¨æ·ç¶ææ æ

user.password.error=å¯ç éè¯¯
user.old.password.error=åå¯ç éè¯¯
user.password.too.weak=å¯ç å¼ºåº¦ä¸è¶³
user.password.expired=å¯ç å·²è¿æ
user.password.reset.failed=å¯ç éç½®å¤±è´¥
user.password.change.failed=å¯ç ä¿®æ¹å¤±è´¥

user.verification.code.error=éªè¯ç éè¯¯
user.verification.code.expired=éªè¯ç å·²è¿æ
user.verification.code.send.failed=éªè¯ç åéå¤±è´¥
user.verification.code.too.frequent=éªè¯ç åéè¿äºé¢ç¹
user.verification.code.not.found=éªè¯ç ä¸å­å¨

user.verification.pending=å®åè®¤è¯å®¡æ ¸ä¸­
user.verification.failed=å®åè®¤è¯å¤±è´¥
user.id.card.invalid=èº«ä»½è¯å·ç æ æ
user.face.verification.failed=äººè¸è¯å«éªè¯å¤±è´¥
user.already.verified=ç¨æ·å·²éè¿å®åè®¤è¯
user.verification.document.invalid=è®¤è¯ææ¡£æ æ
user.verification.timeout=è®¤è¯è¶æ¶

user.settings.not.found=ç¨æ·è®¾ç½®ä¸å­å¨
user.settings.update.failed=è®¾ç½®æ´æ°å¤±è´¥
user.privacy.settings.invalid=éç§è®¾ç½®æ æ
user.notification.settings.invalid=éç¥è®¾ç½®æ æ

user.address.not.found=å°åä¸å­å¨
user.address.invalid=å°åä¿¡æ¯æ æ
user.address.limit.exceeded=å°åæ°éè¶åºéå¶
user.default.address.not.found=é»è®¤å°åä¸å­å¨

user.preference.not.found=ç¨æ·åå¥½ä¸å­å¨
user.preference.invalid=ç¨æ·åå¥½æ æ
user.preference.update.failed=åå¥½æ´æ°å¤±è´¥

user.statistics.not.found=ç¨æ·ç»è®¡ä¸å­å¨
user.statistics.update.failed=ç»è®¡æ´æ°å¤±è´¥

user.relationship.not.found=ç¨æ·å³ç³»ä¸å­å¨
user.relationship.already.exists=ç¨æ·å³ç³»å·²å­å¨
user.relationship.invalid=ç¨æ·å³ç³»æ æ
user.friend.request.failed=å¥½åè¯·æ±å¤±è´¥
user.friend.request.already.sent=å¥½åè¯·æ±å·²åé

user.tag.not.found=ç¨æ·æ ç­¾ä¸å­å¨
user.tag.already.exists=ç¨æ·æ ç­¾å·²å­å¨
user.tag.invalid=ç¨æ·æ ç­¾æ æ
user.tag.limit.exceeded=æ ç­¾æ°éè¶åºéå¶

# ========== æå¡æ¨¡åæ¶æ¯ ==========
service.not.found=æå¡ä¸å­å¨
service.unavailable=æå¡ä¸å¯ç¨
service.disabled=æå¡å·²ç¦ç¨
service.expired=æå¡å·²è¿æ
service.already.exists=æå¡å·²å­å¨
service.status.invalid=æå¡ç¶ææ æ
service.category.not.found=æå¡åç±»ä¸å­å¨

service.time.conflict=æå¡æ¶é´å²çª
service.time.invalid=æå¡æ¶é´æ æ
service.time.expired=æå¡æ¶é´å·²è¿æ
service.time.not.available=æå¡æ¶é´ä¸å¯ç¨
service.time.overlap=æå¡æ¶é´éå 

service.area.not.supported=æå¡åºåä¸æ¯æ
service.area.invalid=æå¡åºåæ æ
service.area.out.of.range=æå¡åºåè¶åºèå´
service.area.not.covered=æå¡åºåæªè¦ç

service.quota.exceeded=æå¡éé¢å·²æ»¡
service.quota.insufficient=æå¡éé¢ä¸è¶³
service.quota.limit.reached=æå¡éé¢å·²è¾¾ä¸é
service.quota.reset.failed=æå¡éé¢éç½®å¤±è´¥

service.provider.not.available=æå¡æä¾èä¸å¯ç¨
service.provider.not.found=æå¡æä¾èä¸å­å¨
service.provider.disabled=æå¡æä¾èå·²ç¦ç¨
service.provider.busy=æå¡æä¾èå¿ç¢
service.provider.offline=æå¡æä¾èç¦»çº¿
service.provider.rating.insufficient=æå¡æä¾èè¯åä¸è¶³

service.review.not.found=æå¡è¯ä»·ä¸å­å¨
service.review.already.exists=å·²ç»è¯ä»·è¿äº
service.review.permission.denied=æ æéè¯ä»·
service.review.time.expired=è¯ä»·æ¶é´å·²è¿æ
service.review.invalid=è¯ä»·åå®¹æ æ

service.booking.failed=æå¡é¢çº¦å¤±è´¥
service.booking.conflict=æå¡é¢çº¦å²çª
service.booking.expired=æå¡é¢çº¦å·²è¿æ
service.booking.cancelled=æå¡é¢çº¦å·²åæ¶
service.booking.not.found=æå¡é¢çº¦ä¸å­å¨

service.price.invalid=æå¡ä»·æ ¼æ æ
service.price.changed=æå¡ä»·æ ¼å·²åæ´
service.price.not.set=æå¡ä»·æ ¼æªè®¾ç½®
service.price.calculation.error=æå¡ä»·æ ¼è®¡ç®éè¯¯

service.config.not.found=æå¡éç½®ä¸å­å¨
service.config.invalid=æå¡éç½®æ æ
service.config.update.failed=æå¡éç½®æ´æ°å¤±è´¥

service.statistics.not.found=æå¡ç»è®¡ä¸å­å¨
service.statistics.update.failed=æå¡ç»è®¡æ´æ°å¤±è´¥
service.statistics.invalid=æå¡ç»è®¡æ æ 