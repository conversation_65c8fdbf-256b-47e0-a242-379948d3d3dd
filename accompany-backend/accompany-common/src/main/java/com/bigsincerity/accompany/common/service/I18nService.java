package com.bigsincerity.accompany.common.service;

import com.bigsincerity.accompany.common.entity.I18nMessage;

import java.util.List;

/**
 * 多语言服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface I18nService {

    /**
     * 获取多语言文本
     *
     * @param messageKey 消息键
     * @param language   语言代码（如：zh-CN, en-US）
     * @return 对应语言的文本
     */
    String getMessage(String messageKey, String language);

    /**
     * 获取多语言文本（带参数）
     *
     * @param messageKey 消息键
     * @param language   语言代码
     * @param args       参数
     * @return 格式化后的文本
     */
    String getMessage(String messageKey, String language, Object... args);

    /**
     * 获取中文文本
     */
    String getChineseMessage(String messageKey, Object... args);

    /**
     * 获取英文文本
     */
    String getEnglishMessage(String messageKey, Object... args);

    /**
     * 保存或更新多语言消息
     */
    I18nMessage saveMessage(I18nMessage message);

    /**
     * 批量保存多语言消息
     */
    List<I18nMessage> saveMessages(List<I18nMessage> messages);

    /**
     * 删除多语言消息
     */
    void deleteMessage(String messageKey);

    /**
     * 根据模块获取所有消息
     */
    List<I18nMessage> getMessagesByModule(String module);

    /**
     * 根据分类获取所有消息
     */
    List<I18nMessage> getMessagesByCategory(String category);

    /**
     * 刷新缓存
     */
    void refreshCache();

    /**
     * 预热缓存
     */
    void warmUpCache();

    /**
     * 检查消息键是否存在
     */
    boolean messageExists(String messageKey);

    /**
     * 获取支持的语言列表
     */
    List<String> getSupportedLanguages();
} 