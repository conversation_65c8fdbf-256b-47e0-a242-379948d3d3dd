package com.bigsincerity.accompany.common.enums;

import lombok.Getter;

/**
 * 支持的语言枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum Language {

    /**
     * 中文（简体）
     */
    CHINESE("zh-CN", "中文", "Chinese (Simplified)", true),

    /**
     * 英文（美式）
     */
    ENGLISH("en-US", "英文", "English (US)", true),

    /**
     * 中文（繁体）
     */
    CHINESE_TRADITIONAL("zh-TW", "繁體中文", "Chinese (Traditional)", false),

    /**
     * 日文
     */
    JAPANESE("ja-JP", "日本語", "Japanese", false),

    /**
     * 韩文
     */
    KOREAN("ko-KR", "한국어", "Korean", false),

    /**
     * 法文
     */
    FRENCH("fr-FR", "Français", "French", false),

    /**
     * 德文
     */
    GERMAN("de-DE", "Deutsch", "German", false),

    /**
     * 西班牙文
     */
    SPANISH("es-ES", "Español", "Spanish", false);

    /**
     * 语言代码（ISO 639-1 + ISO 3166-1）
     */
    private final String code;

    /**
     * 本地化显示名称
     */
    private final String localName;

    /**
     * 英文显示名称
     */
    private final String englishName;

    /**
     * 是否启用
     */
    private final Boolean enabled;

    Language(String code, String localName, String englishName, Boolean enabled) {
        this.code = code;
        this.localName = localName;
        this.englishName = englishName;
        this.enabled = enabled;
    }

    /**
     * 根据语言代码获取枚举
     */
    public static Language fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return CHINESE; // 默认中文
        }
        for (Language language : values()) {
            if (language.getCode().equalsIgnoreCase(code)) {
                return language;
            }
        }
        // 尝试匹配语言部分（如：zh-CN -> zh）
        String languagePart = code.split("-")[0];
        for (Language language : values()) {
            if (language.getCode().split("-")[0].equalsIgnoreCase(languagePart)) {
                return language;
            }
        }
        return CHINESE; // 默认中文
    }

    /**
     * 检查语言代码是否有效
     */
    public static boolean isValidCode(String code) {
        return fromCode(code) != null;
    }

    /**
     * 获取启用的语言列表
     */
    public static Language[] getEnabledLanguages() {
        return java.util.Arrays.stream(values())
                .filter(Language::getEnabled)
                .toArray(Language[]::new);
    }

    /**
     * 获取启用的语言代码列表
     */
    public static String[] getEnabledLanguageCodes() {
        return java.util.Arrays.stream(values())
                .filter(Language::getEnabled)
                .map(Language::getCode)
                .toArray(String[]::new);
    }

    /**
     * 检查是否为中文
     */
    public static boolean isChinese(String code) {
        return code != null && (code.startsWith("zh") || code.startsWith("ZH"));
    }

    /**
     * 检查是否为英文
     */
    public static boolean isEnglish(String code) {
        return code != null && (code.startsWith("en") || code.startsWith("EN"));
    }

    /**
     * 获取默认语言
     */
    public static Language getDefault() {
        return CHINESE;
    }

    /**
     * 获取备用语言（当默认语言不可用时）
     */
    public static Language getFallback() {
        return ENGLISH;
    }
} 