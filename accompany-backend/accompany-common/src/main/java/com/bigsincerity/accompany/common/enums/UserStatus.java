package com.bigsincerity.accompany.common.enums;

import lombok.Getter;

/**
 * 用户状态枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum UserStatus {

    /**
     * 禁用状态
     */
    DISABLED(0, "禁用", "账户已被禁用"),

    /**
     * 正常状态
     */
    ACTIVE(1, "正常", "账户正常"),

    /**
     * 锁定状态
     */
    LOCKED(2, "锁定", "账户已被锁定");

    /**
     * 状态代码
     */
    private final Integer code;

    /**
     * 状态名称
     */
    private final String name;

    /**
     * 状态描述
     */
    private final String description;

    UserStatus(Integer code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    /**
     * 根据代码获取状态枚举
     */
    public static UserStatus fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (UserStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 检查状态是否有效
     */
    public static boolean isValidCode(Integer code) {
        return fromCode(code) != null;
    }

    /**
     * 检查状态是否为正常
     */
    public static boolean isActive(Integer code) {
        return ACTIVE.getCode().equals(code);
    }

    /**
     * 检查状态是否为禁用
     */
    public static boolean isDisabled(Integer code) {
        return DISABLED.getCode().equals(code);
    }

    /**
     * 检查状态是否为锁定
     */
    public static boolean isLocked(Integer code) {
        return LOCKED.getCode().equals(code);
    }
}
