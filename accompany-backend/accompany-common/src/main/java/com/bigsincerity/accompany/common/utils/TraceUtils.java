package com.bigsincerity.accompany.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import java.util.UUID;

/**
 * 追踪ID工具类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class TraceUtils {

    /**
     * 追踪ID在MDC中的键名
     */
    public static final String TRACE_ID_KEY = "traceId";
    
    /**
     * 请求ID在MDC中的键名
     */
    public static final String REQUEST_ID_KEY = "requestId";

    /**
     * 生成追踪ID
     */
    public static String generateTraceId() {
        return UUID.randomUUID().toString().replace("-", "").toLowerCase();
    }

    /**
     * 生成请求ID（短格式）
     */
    public static String generateRequestId() {
        return UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 设置追踪ID到MDC
     */
    public static void setTraceId(String traceId) {
        if (traceId != null && !traceId.trim().isEmpty()) {
            MDC.put(TRACE_ID_KEY, traceId);
        }
    }

    /**
     * 设置请求ID到MDC
     */
    public static void setRequestId(String requestId) {
        if (requestId != null && !requestId.trim().isEmpty()) {
            MDC.put(REQUEST_ID_KEY, requestId);
        }
    }

    /**
     * 获取当前追踪ID
     */
    public static String getTraceId() {
        return MDC.get(TRACE_ID_KEY);
    }

    /**
     * 获取当前请求ID
     */
    public static String getRequestId() {
        return MDC.get(REQUEST_ID_KEY);
    }

    /**
     * 初始化追踪信息
     */
    public static void initTrace() {
        String traceId = generateTraceId();
        String requestId = generateRequestId();
        setTraceId(traceId);
        setRequestId(requestId);
        log.debug("初始化追踪信息: traceId={}, requestId={}", traceId, requestId);
    }

    /**
     * 初始化追踪信息（使用指定的追踪ID）
     */
    public static void initTrace(String traceId) {
        String requestId = generateRequestId();
        setTraceId(traceId != null ? traceId : generateTraceId());
        setRequestId(requestId);
        log.debug("初始化追踪信息: traceId={}, requestId={}", getTraceId(), requestId);
    }

    /**
     * 清除追踪信息
     */
    public static void clearTrace() {
        String traceId = getTraceId();
        String requestId = getRequestId();
        MDC.remove(TRACE_ID_KEY);
        MDC.remove(REQUEST_ID_KEY);
        log.debug("清除追踪信息: traceId={}, requestId={}", traceId, requestId);
    }

    /**
     * 获取追踪信息
     */
    public static TraceInfo getTraceInfo() {
        return new TraceInfo(getTraceId(), getRequestId());
    }

    /**
     * 追踪信息类
     */
    public static class TraceInfo {
        private final String traceId;
        private final String requestId;

        public TraceInfo(String traceId, String requestId) {
            this.traceId = traceId;
            this.requestId = requestId;
        }

        public String getTraceId() {
            return traceId;
        }

        public String getRequestId() {
            return requestId;
        }

        @Override
        public String toString() {
            return String.format("TraceInfo{traceId='%s', requestId='%s'}", traceId, requestId);
        }
    }
} 