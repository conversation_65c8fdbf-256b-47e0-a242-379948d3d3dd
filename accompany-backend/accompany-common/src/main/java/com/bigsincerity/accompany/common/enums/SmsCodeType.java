package com.bigsincerity.accompany.common.enums;

import lombok.Getter;

/**
 * 短信验证码类型枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum SmsCodeType {

    /**
     * 注册验证码
     */
    REGISTER(1, "register", "注册", "【Accompany】您的注册验证码是：%s，5分钟内有效。请勿泄露给他人。"),

    /**
     * 登录验证码
     */
    LOGIN(2, "login", "登录", "【Accompany】您的登录验证码是：%s，5分钟内有效。请勿泄露给他人。"),

    /**
     * 重置密码验证码
     */
    RESET_PASSWORD(3, "reset_password", "重置密码", "【Accompany】您的密码重置验证码是：%s，5分钟内有效。请勿泄露给他人。"),

    /**
     * 修改手机号验证码
     */
    CHANGE_PHONE(4, "change_phone", "修改手机号", "【Accompany】您的手机号变更验证码是：%s，5分钟内有效。请勿泄露给他人。");

    /**
     * 数字代码
     */
    private final Integer code;

    /**
     * 字符串标识
     */
    private final String type;

    /**
     * 描述
     */
    private final String description;

    /**
     * 短信模板
     */
    private final String messageTemplate;

    SmsCodeType(Integer code, String type, String description, String messageTemplate) {
        this.code = code;
        this.type = type;
        this.description = description;
        this.messageTemplate = messageTemplate;
    }

    /**
     * 根据数字代码获取枚举
     */
    public static SmsCodeType fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SmsCodeType codeType : values()) {
            if (codeType.getCode().equals(code)) {
                return codeType;
            }
        }
        return null;
    }

    /**
     * 根据字符串类型获取枚举
     */
    public static SmsCodeType fromType(String type) {
        if (type == null || type.trim().isEmpty()) {
            return null;
        }
        for (SmsCodeType codeType : values()) {
            if (codeType.getType().equals(type)) {
                return codeType;
            }
        }
        return null;
    }

    /**
     * 根据字符串类型获取数字代码
     */
    public static Integer getCodeByType(String type) {
        SmsCodeType codeType = fromType(type);
        return codeType != null ? codeType.getCode() : null;
    }

    /**
     * 根据数字代码获取短信模板
     */
    public static String getMessageTemplateByCode(Integer code) {
        SmsCodeType codeType = fromCode(code);
        return codeType != null ? codeType.getMessageTemplate() : "【Accompany】您的验证码是：%s，5分钟内有效。请勿泄露给他人。";
    }

    /**
     * 验证类型是否有效
     */
    public static boolean isValidType(String type) {
        return fromType(type) != null;
    }

    /**
     * 验证代码是否有效
     */
    public static boolean isValidCode(Integer code) {
        return fromCode(code) != null;
    }
}
