// package com.bigsincerity.accompany.common.result;

// import org.springframework.context.MessageSource;
// import org.springframework.context.i18n.LocaleContextHolder;
// import org.springframework.stereotype.Component;

// import java.util.HashMap;
// import java.util.Map;
// import java.util.concurrent.ConcurrentHashMap;

// /**
//  * 响应状态码管理器
//  * 统一管理所有模块的异常码，支持国际化消息查找
//  * 
//  * <AUTHOR>
//  * @since 1.0.0
//  */
// @Component
// public class ResultCodeManager {

//     private final MessageSource messageSource;
//     private final Map<Integer, IResultCode> codeMap = new ConcurrentHashMap<>();
//     private final Map<String, IResultCode> messageKeyMap = new ConcurrentHashMap<>();

//     public ResultCodeManager(MessageSource messageSource) {
//         this.messageSource = messageSource;
//         initializeResultCodes();
//     }

//     /**
//      * 初始化所有异常码
//      */
//     private void initializeResultCodes() {
//         // 注册通用异常码
//         registerResultCodes(CommonResultCode.values());
        
//         // 注册认证授权异常码
//         registerResultCodes(AuthResultCode.values());
        
//         // 注册用户模块异常码
//         registerResultCodes(UserResultCode.values());
        
//         // 注册服务模块异常码
//         registerResultCodes(ServiceResultCode.values());
        
//         // 可以继续注册其他模块的异常码
//         // registerResultCodes(OrderResultCode.values());
//         // registerResultCodes(PaymentResultCode.values());
//         // registerResultCodes(ChatResultCode.values());
//         // 等等...
//     }

//     /**
//      * 注册异常码
//      */
//     private void registerResultCodes(IResultCode[] resultCodes) {
//         for (IResultCode resultCode : resultCodes) {
//             codeMap.put(resultCode.getCode(), resultCode);
//             messageKeyMap.put(resultCode.getMessageKey(), resultCode);
//         }
//     }

//     /**
//      * 根据状态码查找异常码
//      */
//     public IResultCode findByCode(Integer code) {
//         return codeMap.get(code);
//     }

//     /**
//      * 根据消息键查找异常码
//      */
//     public IResultCode findByMessageKey(String messageKey) {
//         return messageKeyMap.get(messageKey);
//     }

//     /**
//      * 根据状态码获取国际化消息
//      */
//     public String getMessage(Integer code) {
//         IResultCode resultCode = findByCode(code);
//         if (resultCode == null) {
//             return "Unknown error code: " + code;
//         }
//         return getMessage(resultCode);
//     }

//     /**
//      * 根据异常码获取国际化消息
//      */
//     public String getMessage(IResultCode resultCode) {
//         if (resultCode == null) {
//             return "Unknown error";
//         }
        
//         try {
//             String message = messageSource.getMessage(
//                 resultCode.getMessageKey(), 
//                 null, 
//                 resultCode.getDefaultMessage(), 
//                 LocaleContextHolder.getLocale()
//             );
//             return message;
//         } catch (Exception e) {
//             // 如果国际化消息获取失败，返回默认消息
//             return resultCode.getDefaultMessage();
//         }
//     }

//     /**
//      * 根据状态码获取异常码信息（包含国际化消息）
//      */
//     public ResultCodeInfo getResultCodeInfo(Integer code) {
//         IResultCode resultCode = findByCode(code);
//         if (resultCode == null) {
//             return null;
//         }
        
//         return new ResultCodeInfo(
//             resultCode.getCode(),
//             getMessage(resultCode),
//             resultCode.getModule(),
//             resultCode.getErrorLevel()
//         );
//     }

//     /**
//      * 获取所有异常码
//      */
//     public Map<Integer, IResultCode> getAllResultCodes() {
//         return new HashMap<>(codeMap);
//     }

//     /**
//      * 根据模块获取异常码
//      */
//     public Map<Integer, IResultCode> getResultCodesByModule(String module) {
//         Map<Integer, IResultCode> moduleCodes = new HashMap<>();
//         for (IResultCode resultCode : codeMap.values()) {
//             if (module.equals(resultCode.getModule())) {
//                 moduleCodes.put(resultCode.getCode(), resultCode);
//             }
//         }
//         return moduleCodes;
//     }

//     /**
//      * 根据错误级别获取异常码
//      */
//     public Map<Integer, IResultCode> getResultCodesByErrorLevel(IResultCode.ErrorLevel errorLevel) {
//         Map<Integer, IResultCode> levelCodes = new HashMap<>();
//         for (IResultCode resultCode : codeMap.values()) {
//             if (errorLevel == resultCode.getErrorLevel()) {
//                 levelCodes.put(resultCode.getCode(), resultCode);
//             }
//         }
//         return levelCodes;
//     }

//     /**
//      * 异常码信息类
//      */
//     public static class ResultCodeInfo {
//         private final Integer code;
//         private final String message;
//         private final String module;
//         private final IResultCode.ErrorLevel errorLevel;

//         public ResultCodeInfo(Integer code, String message, String module, IResultCode.ErrorLevel errorLevel) {
//             this.code = code;
//             this.message = message;
//             this.module = module;
//             this.errorLevel = errorLevel;
//         }

//         public Integer getCode() {
//             return code;
//         }

//         public String getMessage() {
//             return message;
//         }

//         public String getModule() {
//             return module;
//         }

//         public IResultCode.ErrorLevel getErrorLevel() {
//             return errorLevel;
//         }

//         @Override
//         public String toString() {
//             return String.format("ResultCodeInfo{code=%d, message='%s', module='%s', errorLevel=%s}", 
//                 code, message, module, errorLevel);
//         }
//     }
// } 