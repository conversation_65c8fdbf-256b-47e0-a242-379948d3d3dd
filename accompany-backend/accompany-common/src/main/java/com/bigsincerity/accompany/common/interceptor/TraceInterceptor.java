package com.bigsincerity.accompany.common.interceptor;

import com.bigsincerity.accompany.common.utils.TraceUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 追踪拦截器
 * 为每个HTTP请求生成追踪ID
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class TraceInterceptor implements HandlerInterceptor {

    /**
     * HTTP头中的追踪ID字段名
     */
    public static final String TRACE_ID_HEADER = "X-Trace-Id";
    
    /**
     * HTTP头中的请求ID字段名
     */
    public static final String REQUEST_ID_HEADER = "X-Request-Id";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 从请求头获取追踪ID，如果没有则生成新的
        String traceId = request.getHeader(TRACE_ID_HEADER);
        if (traceId == null || traceId.trim().isEmpty()) {
            TraceUtils.initTrace();
        } else {
            TraceUtils.initTrace(traceId);
        }

        // 将追踪ID添加到响应头
        response.setHeader(TRACE_ID_HEADER, TraceUtils.getTraceId());
        response.setHeader(REQUEST_ID_HEADER, TraceUtils.getRequestId());

        log.info("开始处理请求: {} {}, traceId={}, requestId={}", 
                request.getMethod(), 
                request.getRequestURI(), 
                TraceUtils.getTraceId(),
                TraceUtils.getRequestId());

        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        try {
            log.info("完成处理请求: {} {}, status={}, traceId={}, requestId={}", 
                    request.getMethod(), 
                    request.getRequestURI(), 
                    response.getStatus(),
                    TraceUtils.getTraceId(),
                    TraceUtils.getRequestId());
        } finally {
            // 清除MDC中的追踪信息，避免内存泄漏
            TraceUtils.clearTrace();
        }
    }
} 