package com.bigsincerity.accompany.common.result;

/**
 * 响应状态码接口
 * 定义异常码的基本结构，支持国际化和模块化分类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IResultCode {

    /**
     * 获取状态码
     */
    Integer getCode();

    /**
     * 获取状态消息键（用于国际化）
     */
    String getMessageKey();

    /**
     * 获取默认状态消息
     */
    String getMessage();

    /**
     * 获取模块名称
     */
    String getModuleName();

    /**
     * 获取错误级别
     */
    String getErrorLevel();

    /**
     * 错误级别枚举
     */
    enum ErrorLevel {
        INFO,      // 信息级别
        WARNING,   // 警告级别
        ERROR,     // 错误级别
        CRITICAL   // 严重错误级别
    }

    enum ModuleName {
        COMMON,   //通用模块
        USER,       // 用户模块
        AUTH,       // 鉴权模块
        SERVICE,    // 服务模块
        ORDER,      // 订单模块
        PAYMENT,    //支付模块
        CHAT,       //聊天模块
        COMMUNITY,  //社区模块
        FILE,       //文件模块
        THIRD_PARTY,//第三方服务
    }
} 