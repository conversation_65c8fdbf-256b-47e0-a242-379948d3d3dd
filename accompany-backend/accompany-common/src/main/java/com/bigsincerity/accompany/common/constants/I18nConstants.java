package com.bigsincerity.accompany.common.constants;

/**
 * 多语言相关常量
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class I18nConstants {

    /**
     * 语言相关
     */
    public static class Language {
        /** 默认语言 */
        public static final String DEFAULT_LANGUAGE = "zh-CN";
        
        /** 备用语言 */
        public static final String FALLBACK_LANGUAGE = "en-US";
        
        /** HTTP头中的语言字段 */
        public static final String ACCEPT_LANGUAGE_HEADER = "Accept-Language";
        
        /** 请求参数中的语言字段 */
        public static final String LANGUAGE_PARAM = "lang";
    }

    /**
     * 消息分类
     */
    public static class Category {
        /** 错误消息 */
        public static final String ERROR = "error";
        
        /** 成功消息 */
        public static final String SUCCESS = "success";
        
        /** 警告消息 */
        public static final String WARNING = "warning";
        
        /** 信息消息 */
        public static final String INFO = "info";
        
        /** 验证消息 */
        public static final String VALIDATION = "validation";
        
        /** 业务消息 */
        public static final String BUSINESS = "business";
        
        /** 系统消息 */
        public static final String SYSTEM = "system";
    }

    /**
     * 模块名称
     */
    public static class Module {
        /** 用户模块 */
        public static final String USER = "user";
        
        /** 认证模块 */
        public static final String AUTH = "auth";
        
        /** 订单模块 */
        public static final String ORDER = "order";
        
        /** 支付模块 */
        public static final String PAYMENT = "payment";
        
        /** 通知模块 */
        public static final String NOTIFICATION = "notification";
        
        /** 系统模块 */
        public static final String SYSTEM = "system";
        
        /** 通用模块 */
        public static final String COMMON = "common";
    }

    /**
     * 缓存相关
     */
    public static class Cache {
        /** 多语言缓存前缀 */
        public static final String I18N_CACHE_PREFIX = "accompany:i18n:";
        
        /** 消息缓存键模板 */
        public static final String MESSAGE_CACHE_KEY_TEMPLATE = I18N_CACHE_PREFIX + "msg:%s:%s";
        
        /** 模块缓存键模板 */
        public static final String MODULE_CACHE_KEY_TEMPLATE = I18N_CACHE_PREFIX + "module:%s";
        
        /** 分类缓存键模板 */
        public static final String CATEGORY_CACHE_KEY_TEMPLATE = I18N_CACHE_PREFIX + "category:%s";
        
        /** 所有消息缓存键 */
        public static final String ALL_MESSAGES_CACHE_KEY = I18N_CACHE_PREFIX + "all";
        
        /** 缓存过期时间（小时） */
        public static final int CACHE_EXPIRE_HOURS = 24;
    }

    /**
     * 预定义消息键
     */
    public static class MessageKey {
        
        // 通用消息
        public static final String SUCCESS = "common.success";
        public static final String FAILED = "common.failed";
        public static final String INVALID_PARAMETER = "common.invalid_parameter";
        public static final String UNAUTHORIZED = "common.unauthorized";
        public static final String FORBIDDEN = "common.forbidden";
        public static final String NOT_FOUND = "common.not_found";
        public static final String INTERNAL_ERROR = "common.internal_error";
        
        // 用户相关
        public static final String USER_NOT_FOUND = "user.not_found";
        public static final String USER_EXISTS = "user.exists";
        public static final String USERNAME_EXISTS = "user.username_exists";
        public static final String PHONE_EXISTS = "user.phone_exists";
        public static final String EMAIL_EXISTS = "user.email_exists";
        public static final String PASSWORD_ERROR = "user.password_error";
        public static final String ACCOUNT_DISABLED = "user.account_disabled";
        public static final String ACCOUNT_LOCKED = "user.account_locked";
        
        // 认证相关
        public static final String LOGIN_SUCCESS = "auth.login_success";
        public static final String LOGIN_FAILED = "auth.login_failed";
        public static final String REGISTER_SUCCESS = "auth.register_success";
        public static final String REGISTER_FAILED = "auth.register_failed";
        public static final String LOGOUT_SUCCESS = "auth.logout_success";
        public static final String TOKEN_INVALID = "auth.token_invalid";
        public static final String TOKEN_EXPIRED = "auth.token_expired";
        
        // 验证码相关
        public static final String SMS_SEND_SUCCESS = "sms.send_success";
        public static final String SMS_SEND_FAILED = "sms.send_failed";
        public static final String SMS_CODE_INVALID = "sms.code_invalid";
        public static final String SMS_CODE_EXPIRED = "sms.code_expired";
        public static final String SMS_SEND_TOO_FREQUENT = "sms.send_too_frequent";
        
        // 验证消息
        public static final String VALIDATION_REQUIRED = "validation.required";
        public static final String VALIDATION_LENGTH = "validation.length";
        public static final String VALIDATION_EMAIL = "validation.email";
        public static final String VALIDATION_PHONE = "validation.phone";
        public static final String VALIDATION_PASSWORD = "validation.password";
    }

    /**
     * 占位符
     */
    public static class Placeholder {
        /** 参数占位符前缀 */
        public static final String PARAM_PREFIX = "{";
        
        /** 参数占位符后缀 */
        public static final String PARAM_SUFFIX = "}";
        
        /** 索引占位符模式 */
        public static final String INDEX_PLACEHOLDER_PATTERN = "\\{(\\d+)\\}";
        
        /** 命名占位符模式 */
        public static final String NAMED_PLACEHOLDER_PATTERN = "\\{([a-zA-Z_][a-zA-Z0-9_]*)\\}";
    }
} 