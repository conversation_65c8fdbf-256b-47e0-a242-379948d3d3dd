package com.bigsincerity.accompany.common.result;

/**
 * 通用响应状态码枚举
 * 定义系统中通用的响应状态码，范围：200-299, 400-499, 500-599
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public enum CommonResultCode implements IResultCode {

    // ========== 成功状态码 (200-299) ==========
    SUCCESS(200, "common.success", "操作成功", ModuleName.COMMON.name(), ErrorLevel.INFO.name()),

    // ========== 客户端错误 (400-499) ==========
    PARAM_ERROR(400, "common.param.error", "参数错误", ModuleName.COMMON.name(), ErrorLevel.WARNING.name()),
    UNAUTHORIZED(401, "common.unauthorized", "未授权，请先登录", ModuleName.COMMON.name(), ErrorLevel.WARNING.name()),
    FORBIDDEN(403, "common.forbidden", "权限不足", ModuleName.COMMON.name(), ErrorLevel.WARNING.name()),
    NOT_FOUND(404, "common.not.found", "资源不存在", ModuleName.COMMON.name(), ErrorLevel.WARNING.name()),
    METHOD_NOT_ALLOWED(405, "common.method.not.allowed", "请求方法不支持", ModuleName.COMMON.name(), ErrorLevel.WARNING.name()),
    REQUEST_TIMEOUT(408, "common.request.timeout", "请求超时", ModuleName.COMMON.name(), ErrorLevel.WARNING.name()),
    CONFLICT(409, "common.conflict", "资源冲突", ModuleName.COMMON.name(), ErrorLevel.WARNING.name()),
    UNSUPPORTED_MEDIA_TYPE(415, "common.unsupported.media.type", "不支持的媒体类型", ModuleName.COMMON.name(), ErrorLevel.WARNING.name()),
    TOO_MANY_REQUESTS(429, "common.too.many.requests", "请求过于频繁，请稍后再试", ModuleName.COMMON.name(), ErrorLevel.WARNING.name()),

    // ========== 服务器错误 (500-599) ==========
    ERROR(500, "common.error", "系统异常", ModuleName.COMMON.name(), ErrorLevel.ERROR.name()),
    NOT_IMPLEMENTED(501, "common.not.implemented", "功能未实现", ModuleName.COMMON.name(), ErrorLevel.ERROR.name()),
    SERVICE_UNAVAILABLE(503, "common.service.unavailable", "服务不可用", ModuleName.COMMON.name(), ErrorLevel.ERROR.name()),
    GATEWAY_TIMEOUT(504, "common.gateway.timeout", "网关超时", ModuleName.COMMON.name(), ErrorLevel.ERROR.name());

    private final Integer code;
    private final String messageKey;
    private final String message;
    private final String module;
    private final String errorLevel;

    CommonResultCode(Integer code, String messageKey, String message, String module, String errorLevel) {
        this.code = code;
        this.messageKey = messageKey;
        this.message = message;
        this.module = module;
        this.errorLevel = errorLevel;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getMessageKey() {
        return messageKey;
    }

    @Override
    public String getMessage() {
        return message;
    }

    @Override
    public String getModuleName() {
        return module;
    }

    @Override
    public String getErrorLevel() {
        return errorLevel;
    }

    /**
     * 根据状态码查找枚举
     */
    public static CommonResultCode findByCode(Integer code) {
        for (CommonResultCode resultCode : values()) {
            if (resultCode.getCode().equals(code)) {
                return resultCode;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return String.format("CommonResultCode{code=%d, messageKey='%s', module='%s'}", code, messageKey, module);
    }
} 