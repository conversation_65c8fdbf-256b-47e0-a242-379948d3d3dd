package com.bigsincerity.accompany.common.result;

/**
 * 认证授权响应状态码枚举
 * 定义认证授权相关的响应状态码，范围：401000-401999
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public enum AuthResultCode implements IResultCode {

    // ========== Token相关 (401000-401099) ==========
    TOKEN_EXPIRED(401001, "auth.token.expired", "Token已过期", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),
    TOKEN_INVALID(401002, "auth.token.invalid", "Token无效", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),
    TOKEN_MISSING(401003, "auth.token.missing", "Token缺失", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),
    TOKEN_FORMAT_ERROR(401004, "auth.token.format.error", "Token格式错误", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),
    TOKEN_SIGNATURE_ERROR(401005, "auth.token.signature.error", "Token签名错误", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),

    // ========== 登录相关 (401100-401199) ==========
    LOGIN_FAILED(401101, "auth.login.failed", "登录失败", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),
    LOGIN_CREDENTIALS_ERROR(401102, "auth.login.credentials.error", "用户名或密码错误", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),
    LOGIN_ACCOUNT_DISABLED(401103, "auth.login.account.disabled", "账号已被禁用", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),
    LOGIN_ACCOUNT_LOCKED(401104, "auth.login.account.locked", "账号已被锁定", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),
    LOGIN_TOO_MANY_ATTEMPTS(401105, "auth.login.too.many.attempts", "登录尝试次数过多", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),
    LOGIN_CAPTCHA_ERROR(401106, "auth.login.captcha.error", "验证码错误", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),
    LOGIN_VERIFICATION_CODE_ERROR(401107, "auth.login.verification.code.error", "验证码错误", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),
    LOGIN_VERIFICATION_CODE_EXPIRED(401108, "auth.login.verification.code.expired", "验证码已过期", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),

    // ========== 账号状态相关 (401200-401299) ==========
    ACCOUNT_LOCKED(401201, "auth.account.locked", "账号已被锁定", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),
    ACCOUNT_DISABLED(401202, "auth.account.disabled", "账号已被禁用", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),
    ACCOUNT_NOT_ACTIVATED(401203, "auth.account.not.activated", "账号未激活", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),
    ACCOUNT_SUSPENDED(401204, "auth.account.suspended", "账号已被暂停", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),
    ACCOUNT_EXPIRED(401205, "auth.account.expired", "账号已过期", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),

    // ========== 权限相关 (401300-401399) ==========
    PERMISSION_DENIED(401301, "auth.permission.denied", "权限不足", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),
    ROLE_NOT_FOUND(401302, "auth.role.not.found", "角色不存在", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),
    ROLE_DISABLED(401303, "auth.role.disabled", "角色已被禁用", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),
    PERMISSION_NOT_FOUND(401304, "auth.permission.not.found", "权限不存在", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),

    // ========== 会话相关 (401400-401499) ==========
    SESSION_EXPIRED(401401, "auth.session.expired", "会话已过期", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),
    SESSION_INVALID(401402, "auth.session.invalid", "会话无效", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),
    SESSION_CONFLICT(401403, "auth.session.conflict", "会话冲突", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),

    // ========== 安全相关 (401500-401599) ==========
    SECURITY_VIOLATION(401501, "auth.security.violation", "安全违规", ModuleName.AUTH.name(), ErrorLevel.ERROR.name()),
    IP_BLACKLISTED(401502, "auth.ip.blacklisted", "IP地址已被拉黑", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),
    DEVICE_NOT_TRUSTED(401503, "auth.device.not.trusted", "设备未受信任", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),
    SUSPICIOUS_ACTIVITY(401504, "auth.suspicious.activity", "检测到可疑活动", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),

    // ========== 第三方认证相关 (401600-401699) ==========
    THIRD_PARTY_AUTH_FAILED(401601, "auth.third.party.auth.failed", "第三方认证失败", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),
    OAUTH_TOKEN_INVALID(401602, "auth.oauth.token.invalid", "OAuth Token无效", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),
    OAUTH_TOKEN_EXPIRED(401603, "auth.oauth.token.expired", "OAuth Token已过期", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),
    OAUTH_PROVIDER_ERROR(401604, "auth.oauth.provider.error", "OAuth提供商错误", ModuleName.AUTH.name(), ErrorLevel.ERROR.name()),

    // ========== 多因素认证相关 (401700-401799) ==========
    MFA_REQUIRED(401701, "auth.mfa.required", "需要多因素认证", ModuleName.AUTH.name(), ErrorLevel.INFO.name()),
    MFA_CODE_ERROR(401702, "auth.mfa.code.error", "多因素认证码错误", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),
    MFA_CODE_EXPIRED(401703, "auth.mfa.code.expired", "多因素认证码已过期", ModuleName.AUTH.name(), ErrorLevel.WARNING.name()),
    MFA_NOT_ENABLED(401704, "auth.mfa.not.enabled", "多因素认证未启用", ModuleName.AUTH.name(), ErrorLevel.WARNING.name());

    private final Integer code;
    private final String messageKey;
    private final String message;
    private final String module;
    private final String errorLevel;

    AuthResultCode(Integer code, String messageKey, String message, String module, String errorLevel) {
        this.code = code;
        this.messageKey = messageKey;
        this.message = message;
        this.module = module;
        this.errorLevel = errorLevel;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getMessageKey() {
        return messageKey;
    }

    @Override
    public String getMessage() {
        return message;
    }

    @Override
    public String getModuleName() {
        return module;
    }

    @Override
    public String getErrorLevel() {
        return errorLevel;
    }

    /**
     * 根据状态码查找枚举
     */
    public static AuthResultCode findByCode(Integer code) {
        for (AuthResultCode resultCode : values()) {
            if (resultCode.getCode().equals(code)) {
                return resultCode;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return String.format("AuthResultCode{code=%d, messageKey='%s', module='%s'}", code, messageKey, module);
    }
} 