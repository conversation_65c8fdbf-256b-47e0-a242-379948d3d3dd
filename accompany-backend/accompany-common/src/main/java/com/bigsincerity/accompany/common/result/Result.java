package com.bigsincerity.accompany.common.result;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 统一响应结果类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Result<T> {

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 请求是否成功
     */
    private Boolean success;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 请求ID
     */
    private String traceId;

    /**
     * 成功响应
     */
    public static <T> Result<T> success() {
        return success(null);
    }

    /**
     * 成功响应
     */
    public static <T> Result<T> success(T data) {
        Result<T> result = new Result<>();
        result.setCode(CommonResultCode.SUCCESS.getCode());
        result.setMessage(CommonResultCode.SUCCESS.getMessage());
        result.setData(data);
        result.setSuccess(true);
        result.setTimestamp(System.currentTimeMillis());
        result.setTraceId(getCurrentTraceId());
        return result;
    }

    /**
     * 成功响应
     */
    public static <T> Result<T> success(T data, String message) {
        Result<T> result = new Result<>();
        result.setCode(CommonResultCode.SUCCESS.getCode());
        result.setMessage(message);
        result.setData(data);
        result.setSuccess(true);
        result.setTimestamp(System.currentTimeMillis());
        result.setTraceId(getCurrentTraceId());
        return result;
    }

    /**
     * 错误响应
     */
    public static <T> Result<T> error(String message) {
        Result<T> result = new Result<>();
        result.setCode(CommonResultCode.ERROR.getCode());
        result.setMessage(message);
        result.setSuccess(false);
        result.setTimestamp(System.currentTimeMillis());
        result.setTraceId(getCurrentTraceId());
        return result;
    }

    /**
     * 错误响应
     */
    public static <T> Result<T> error(Integer code, String message) {
        Result<T> result = new Result<>();
        result.setCode(code);
        result.setMessage(message);
        result.setSuccess(false);
        result.setTimestamp(System.currentTimeMillis());
        result.setTraceId(getCurrentTraceId());
        return result;
    }

    /**
     * 错误响应
     */
    public static <T> Result<T> error(IResultCode resultCode) {
        Result<T> result = new Result<>();
        result.setCode(resultCode.getCode());
        result.setMessage(resultCode.getMessage());
        result.setSuccess(false);
        result.setTimestamp(System.currentTimeMillis());
        result.setTraceId(getCurrentTraceId());
        return result;
    }

    /**
     * 错误响应
     */
    public static <T> Result<T> error(IResultCode resultCode, String message) {
        Result<T> result = new Result<>();
        result.setCode(resultCode.getCode());
        result.setMessage(message);
        result.setSuccess(false);
        result.setTimestamp(System.currentTimeMillis());
        result.setTraceId(getCurrentTraceId());
        return result;
    }

    /**
     * 获取当前追踪ID
     */
    private static String getCurrentTraceId() {
        try {
            // 使用反射获取TraceUtils的getTraceId方法，避免循环依赖
            Class<?> traceUtilsClass = Class.forName("com.bigsincerity.accompany.common.utils.TraceUtils");
            java.lang.reflect.Method getTraceIdMethod = traceUtilsClass.getMethod("getTraceId");
            return (String) getTraceIdMethod.invoke(null);
        } catch (Exception e) {
            // 如果获取失败，返回null
            return null;
        }
    }
} 