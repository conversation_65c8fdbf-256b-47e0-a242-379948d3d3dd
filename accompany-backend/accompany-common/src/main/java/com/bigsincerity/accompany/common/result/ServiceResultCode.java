package com.bigsincerity.accompany.common.result;

/**
 * 服务模块响应状态码枚举
 * 定义服务相关的响应状态码，范围：402000-402999
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public enum ServiceResultCode implements IResultCode {

    // ========== 服务基础信息相关 (402000-402099) ==========
    SERVICE_NOT_FOUND(402001, "service.not.found", "服务不存在", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    SERVICE_UNAVAILABLE(402002, "service.unavailable", "服务不可用", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    SERVICE_DISABLED(402003, "service.disabled", "服务已禁用", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    SERVICE_EXPIRED(402004, "service.expired", "服务已过期", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    SERVICE_ALREADY_EXISTS(402005, "service.already.exists", "服务已存在", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    SERVICE_STATUS_INVALID(402006, "service.status.invalid", "服务状态无效", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    SERVICE_CATEGORY_NOT_FOUND(402007, "service.category.not.found", "服务分类不存在", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),

    // ========== 服务时间相关 (402100-402199) ==========
    SERVICE_TIME_CONFLICT(402101, "service.time.conflict", "服务时间冲突", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    SERVICE_TIME_INVALID(402102, "service.time.invalid", "服务时间无效", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    SERVICE_TIME_EXPIRED(402103, "service.time.expired", "服务时间已过期", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    SERVICE_TIME_NOT_AVAILABLE(402104, "service.time.not.available", "服务时间不可用", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    SERVICE_TIME_OVERLAP(402105, "service.time.overlap", "服务时间重叠", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),

    // ========== 服务区域相关 (402200-402299) ==========
    SERVICE_AREA_NOT_SUPPORTED(402201, "service.area.not.supported", "服务区域不支持", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    SERVICE_AREA_INVALID(402202, "service.area.invalid", "服务区域无效", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    SERVICE_AREA_OUT_OF_RANGE(402203, "service.area.out.of.range", "服务区域超出范围", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    SERVICE_AREA_NOT_COVERED(402204, "service.area.not.covered", "服务区域未覆盖", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),

    // ========== 服务配额相关 (402300-402399) ==========
    SERVICE_QUOTA_EXCEEDED(402301, "service.quota.exceeded", "服务配额已满", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    SERVICE_QUOTA_INSUFFICIENT(402302, "service.quota.insufficient", "服务配额不足", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    SERVICE_QUOTA_LIMIT_REACHED(402303, "service.quota.limit.reached", "服务配额已达上限", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    SERVICE_QUOTA_RESET_FAILED(402304, "service.quota.reset.failed", "服务配额重置失败", ModuleName.SERVICE.name(), ErrorLevel.ERROR.name()),

    // ========== 服务提供者相关 (402400-402499) ==========
    PROVIDER_NOT_AVAILABLE(402401, "service.provider.not.available", "服务提供者不可用", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    PROVIDER_NOT_FOUND(402402, "service.provider.not.found", "服务提供者不存在", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    PROVIDER_DISABLED(402403, "service.provider.disabled", "服务提供者已禁用", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    PROVIDER_BUSY(402404, "service.provider.busy", "服务提供者忙碌", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    PROVIDER_OFFLINE(402405, "service.provider.offline", "服务提供者离线", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    PROVIDER_RATING_INSUFFICIENT(402406, "service.provider.rating.insufficient", "服务提供者评分不足", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),

    // ========== 服务评价相关 (402500-402599) ==========
    SERVICE_REVIEW_NOT_FOUND(402501, "service.review.not.found", "服务评价不存在", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    SERVICE_REVIEW_ALREADY_EXISTS(402502, "service.review.already.exists", "已经评价过了", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    SERVICE_REVIEW_PERMISSION_DENIED(402503, "service.review.permission.denied", "无权限评价", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    SERVICE_REVIEW_TIME_EXPIRED(402504, "service.review.time.expired", "评价时间已过期", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    SERVICE_REVIEW_INVALID(402505, "service.review.invalid", "评价内容无效", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),

    // ========== 服务预约相关 (402600-402699) ==========
    SERVICE_BOOKING_FAILED(402601, "service.booking.failed", "服务预约失败", ModuleName.SERVICE.name(), ErrorLevel.ERROR.name()),
    SERVICE_BOOKING_CONFLICT(402602, "service.booking.conflict", "服务预约冲突", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    SERVICE_BOOKING_EXPIRED(402603, "service.booking.expired", "服务预约已过期", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    SERVICE_BOOKING_CANCELLED(402604, "service.booking.cancelled", "服务预约已取消", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    SERVICE_BOOKING_NOT_FOUND(402605, "service.booking.not.found", "服务预约不存在", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),

    // ========== 服务价格相关 (402700-402799) ==========
    SERVICE_PRICE_INVALID(402701, "service.price.invalid", "服务价格无效", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    SERVICE_PRICE_CHANGED(402702, "service.price.changed", "服务价格已变更", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    SERVICE_PRICE_NOT_SET(402703, "service.price.not.set", "服务价格未设置", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    SERVICE_PRICE_CALCULATION_ERROR(402704, "service.price.calculation.error", "服务价格计算错误", ModuleName.SERVICE.name(), ErrorLevel.ERROR.name()),

    // ========== 服务配置相关 (402800-402899) ==========
    SERVICE_CONFIG_NOT_FOUND(402801, "service.config.not.found", "服务配置不存在", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    SERVICE_CONFIG_INVALID(402802, "service.config.invalid", "服务配置无效", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    SERVICE_CONFIG_UPDATE_FAILED(402803, "service.config.update.failed", "服务配置更新失败", ModuleName.SERVICE.name(), ErrorLevel.ERROR.name()),

    // ========== 服务统计相关 (402900-402999) ==========
    SERVICE_STATISTICS_NOT_FOUND(402901, "service.statistics.not.found", "服务统计不存在", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name()),
    SERVICE_STATISTICS_UPDATE_FAILED(402902, "service.statistics.update.failed", "服务统计更新失败", ModuleName.SERVICE.name(), ErrorLevel.ERROR.name()),
    SERVICE_STATISTICS_INVALID(402903, "service.statistics.invalid", "服务统计无效", ModuleName.SERVICE.name(), ErrorLevel.WARNING.name());

    private final Integer code;
    private final String messageKey;
    private final String message;
    private final String module;
    private final String errorLevel;

    ServiceResultCode(Integer code, String messageKey, String message, String module, String errorLevel) {
        this.code = code;
        this.messageKey = messageKey;
        this.message = message;
        this.module = module;
        this.errorLevel = errorLevel;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getMessageKey() {
        return messageKey;
    }

    @Override
    public String getMessage() {
        return message;
    }

    @Override
    public String getModuleName() {
        return module;
    }

    @Override
    public String getErrorLevel() {
        return errorLevel;
    }

    /**
     * 根据状态码查找枚举
     */
    public static ServiceResultCode findByCode(Integer code) {
        for (ServiceResultCode resultCode : values()) {
            if (resultCode.getCode().equals(code)) {
                return resultCode;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return String.format("ServiceResultCode{code=%d, messageKey='%s', module='%s'}", code, messageKey, module);
    }
} 