package com.bigsincerity.accompany.common.result;

/**
 * 用户模块响应状态码枚举
 * 定义用户相关的响应状态码，范围：400000-400999
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public enum UserResultCode implements IResultCode {

    // ========== 用户基础信息相关 (400000-400099) ==========
    USER_NOT_FOUND(400001, "user.not.found", "用户不存在", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    USER_ALREADY_EXISTS(400002, "user.already.exists", "用户已存在", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    USERNAME_TAKEN(400003, "user.username.taken", "用户名已被占用", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    EMAIL_TAKEN(400004, "user.email.taken", "邮箱已被注册", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    PHONE_TAKEN(400005, "user.phone.taken", "手机号已被注册", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    USER_INFO_INCOMPLETE(400006, "user.info.incomplete", "用户信息不完整", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    USER_PROFILE_NOT_FOUND(400007, "user.profile.not.found", "用户档案不存在", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    USER_STATUS_INVALID(400008, "user.status.invalid", "用户状态无效", ModuleName.USER.name(), ErrorLevel.WARNING.name()),

    // ========== 密码相关 (400100-400199) ==========
    PASSWORD_ERROR(400101, "user.password.error", "密码错误", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    OLD_PASSWORD_ERROR(400102, "user.old.password.error", "原密码错误", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    PASSWORD_TOO_WEAK(400103, "user.password.too.weak", "密码强度不足", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    PASSWORD_EXPIRED(400104, "user.password.expired", "密码已过期", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    PASSWORD_RESET_FAILED(400105, "user.password.reset.failed", "密码重置失败", ModuleName.USER.name(), ErrorLevel.ERROR.name()),
    PASSWORD_CHANGE_FAILED(400106, "user.password.change.failed", "密码修改失败", ModuleName.USER.name(), ErrorLevel.ERROR.name()),    

    // ========== 验证码相关 (400200-400299) ==========
    VERIFICATION_CODE_ERROR(400201, "user.verification.code.error", "验证码错误", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    VERIFICATION_CODE_EXPIRED(400202, "user.verification.code.expired", "验证码已过期", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    VERIFICATION_CODE_SEND_FAILED(400203, "user.verification.code.send.failed", "验证码发送失败", ModuleName.USER.name(), ErrorLevel.ERROR.name()),
    VERIFICATION_CODE_TOO_FREQUENT(400204, "user.verification.code.too.frequent", "验证码发送过于频繁", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    VERIFICATION_CODE_NOT_FOUND(400205, "user.verification.code.not.found", "验证码不存在", ModuleName.USER.name(), ErrorLevel.WARNING.name()),

    // ========== 实名认证相关 (400300-400399) ==========
    VERIFICATION_PENDING(400301, "user.verification.pending", "实名认证审核中", ModuleName.USER.name(), ErrorLevel.INFO.name()),
    VERIFICATION_FAILED(400302, "user.verification.failed", "实名认证失败", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    ID_CARD_INVALID(400303, "user.id.card.invalid", "身份证号码无效", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    FACE_VERIFICATION_FAILED(400304, "user.face.verification.failed", "人脸识别验证失败", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    ALREADY_VERIFIED(400305, "user.already.verified", "用户已通过实名认证", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    VERIFICATION_DOCUMENT_INVALID(400306, "user.verification.document.invalid", "认证文档无效", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    VERIFICATION_TIMEOUT(400307, "user.verification.timeout", "认证超时", ModuleName.USER.name(), ErrorLevel.WARNING.name()),

    // ========== 用户设置相关 (400400-400499) ==========
    SETTINGS_NOT_FOUND(400401, "user.settings.not.found", "用户设置不存在", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    SETTINGS_UPDATE_FAILED(400402, "user.settings.update.failed", "设置更新失败", ModuleName.USER.name(), ErrorLevel.ERROR.name()),
    PRIVACY_SETTINGS_INVALID(400403, "user.privacy.settings.invalid", "隐私设置无效", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    NOTIFICATION_SETTINGS_INVALID(400404, "user.notification.settings.invalid", "通知设置无效", ModuleName.USER.name(), ErrorLevel.WARNING.name()),

    // ========== 用户地址相关 (400500-400599) ==========
    ADDRESS_NOT_FOUND(400501, "user.address.not.found", "地址不存在", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    ADDRESS_INVALID(400502, "user.address.invalid", "地址信息无效", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    ADDRESS_LIMIT_EXCEEDED(400503, "user.address.limit.exceeded", "地址数量超出限制", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    DEFAULT_ADDRESS_NOT_FOUND(400504, "user.default.address.not.found", "默认地址不存在", ModuleName.USER.name(), ErrorLevel.WARNING.name()),

    // ========== 用户偏好相关 (400600-400699) ==========
    PREFERENCE_NOT_FOUND(400601, "user.preference.not.found", "用户偏好不存在", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    PREFERENCE_INVALID(400602, "user.preference.invalid", "用户偏好无效", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    PREFERENCE_UPDATE_FAILED(400603, "user.preference.update.failed", "偏好更新失败", ModuleName.USER.name(), ErrorLevel.ERROR.name()),

    // ========== 用户统计相关 (400700-400799) ==========
    STATISTICS_NOT_FOUND(400701, "user.statistics.not.found", "用户统计不存在", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    STATISTICS_UPDATE_FAILED(400702, "user.statistics.update.failed", "统计更新失败", ModuleName.USER.name(), ErrorLevel.ERROR.name()),

    // ========== 用户关系相关 (400800-400899) ==========
    RELATIONSHIP_NOT_FOUND(400801, "user.relationship.not.found", "用户关系不存在", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    RELATIONSHIP_ALREADY_EXISTS(400802, "user.relationship.already.exists", "用户关系已存在", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    RELATIONSHIP_INVALID(400803, "user.relationship.invalid", "用户关系无效", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    FRIEND_REQUEST_FAILED(400804, "user.friend.request.failed", "好友请求失败", ModuleName.USER.name(), ErrorLevel.ERROR.name()),
    FRIEND_REQUEST_ALREADY_SENT(400805, "user.friend.request.already.sent", "好友请求已发送", ModuleName.USER.name(), ErrorLevel.WARNING.name()),

    // ========== 用户标签相关 (400900-400999) ==========
    TAG_NOT_FOUND(400901, "user.tag.not.found", "用户标签不存在", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    TAG_ALREADY_EXISTS(400902, "user.tag.already.exists", "用户标签已存在", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    TAG_INVALID(400903, "user.tag.invalid", "用户标签无效", ModuleName.USER.name(), ErrorLevel.WARNING.name()),
    TAG_LIMIT_EXCEEDED(400904, "user.tag.limit.exceeded", "标签数量超出限制", ModuleName.USER.name(), ErrorLevel.WARNING.name());

    private final Integer code;
    private final String messageKey;
    private final String message;
    private final String moduleName;
    private final String errorLevel;

    UserResultCode(Integer code, String messageKey, String message, String moduleName, String errorLevel) {
        this.code = code;
        this.messageKey = messageKey;
        this.message = message;
        this.moduleName = moduleName;
        this.errorLevel = errorLevel;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getMessageKey() {
        return messageKey;
    }

    @Override
    public String getMessage() {
        return message;
    }

    @Override
    public String getModuleName() {
        return moduleName;
    }

    @Override
    public String getErrorLevel() {
        return errorLevel;
    }

    /**
     * 根据状态码查找枚举
     */
    public static UserResultCode findByCode(Integer code) {
        for (UserResultCode resultCode : values()) {
            if (resultCode.getCode().equals(code)) {
                return resultCode;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return String.format("UserResultCode{code=%d, messageKey='%s', module='%s'}", code, messageKey, moduleName);
    }
} 