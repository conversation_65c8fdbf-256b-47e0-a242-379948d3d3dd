package com.bigsincerity.accompany.common.id;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.Enumeration;

/**
 * 雪花算法配置类
 * 
 * 支持多种workerId和datacenterId获取方式：
 * 1. 配置文件指定
 * 2. 根据机器IP自动生成
 * 3. 根据MAC地址自动生成
 * 4. 默认值
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Configuration
public class SnowflakeIdConfig {

    @Value("${accompany.snowflake.worker-id:#{null}}")
    private Long configWorkerId;

    @Value("${accompany.snowflake.datacenter-id:#{null}}")
    private Long configDatacenterId;

    @Value("${accompany.snowflake.auto-generate:true}")
    private boolean autoGenerate;

    /**
     * 配置雪花算法ID生成器Bean
     */
    @Bean
    public SnowflakeIdGenerator snowflakeIdGenerator() {
        long workerId = getWorkerId();
        long datacenterId = getDatacenterId();
        
        log.info("初始化雪花算法ID生成器: workerId={}, datacenterId={}", workerId, datacenterId);
        return new SnowflakeIdGenerator(workerId, datacenterId);
    }

    /**
     * 获取工作机器ID
     */
    private long getWorkerId() {
        // 1. 优先使用配置文件中的值
        if (configWorkerId != null) {
            if (configWorkerId >= 0 && configWorkerId <= 31) {
                log.info("使用配置的workerId: {}", configWorkerId);
                return configWorkerId;
            } else {
                log.warn("配置的workerId超出范围(0-31): {}, 将自动生成", configWorkerId);
            }
        }

        // 2. 如果允许自动生成，则根据机器信息生成
        if (autoGenerate) {
            long autoWorkerId = generateWorkerIdFromMachine();
            log.info("自动生成workerId: {}", autoWorkerId);
            return autoWorkerId;
        }

        // 3. 使用默认值
        log.info("使用默认workerId: 1");
        return 1L;
    }

    /**
     * 获取数据中心ID
     */
    private long getDatacenterId() {
        // 1. 优先使用配置文件中的值
        if (configDatacenterId != null) {
            if (configDatacenterId >= 0 && configDatacenterId <= 31) {
                log.info("使用配置的datacenterId: {}", configDatacenterId);
                return configDatacenterId;
            } else {
                log.warn("配置的datacenterId超出范围(0-31): {}, 将自动生成", configDatacenterId);
            }
        }

        // 2. 如果允许自动生成，则根据机器信息生成
        if (autoGenerate) {
            long autoDatacenterId = generateDatacenterIdFromMachine();
            log.info("自动生成datacenterId: {}", autoDatacenterId);
            return autoDatacenterId;
        }

        // 3. 使用默认值
        log.info("使用默认datacenterId: 1");
        return 1L;
    }

    /**
     * 根据机器信息生成工作机器ID
     */
    private long generateWorkerIdFromMachine() {
        try {
            // 方法1: 使用本机IP地址的最后一段
            InetAddress ip = getLocalHostLANAddress();
            if (ip != null) {
                String ipStr = ip.getHostAddress();
                String[] segments = ipStr.split("\\.");
                if (segments.length == 4) {
                    int lastSegment = Integer.parseInt(segments[3]);
                    long workerId = lastSegment & 31; // 取最后5位
                    log.debug("根据IP地址生成workerId: IP={}, workerId={}", ipStr, workerId);
                    return workerId;
                }
            }

            // 方法2: 使用MAC地址
            NetworkInterface network = NetworkInterface.getByInetAddress(ip);
            if (network != null) {
                byte[] mac = network.getHardwareAddress();
                if (mac != null && mac.length >= 6) {
                    long workerId = ((0x000000FF & (long) mac[mac.length - 1]) |
                                   (0x0000FF00 & (((long) mac[mac.length - 2]) << 8))) >> 6;
                    workerId = workerId & 31; // 取最后5位
                    log.debug("根据MAC地址生成workerId: workerId={}", workerId);
                    return workerId;
                }
            }
        } catch (Exception e) {
            log.warn("自动生成workerId失败，使用随机值", e);
        }

        // 方法3: 使用随机值（基于当前时间）
        long workerId = (System.currentTimeMillis() >>> 2) & 31;
        log.debug("使用随机workerId: {}", workerId);
        return workerId;
    }

    /**
     * 根据机器信息生成数据中心ID
     */
    private long generateDatacenterIdFromMachine() {
        try {
            // 方法1: 使用本机IP地址的倒数第二段
            InetAddress ip = getLocalHostLANAddress();
            if (ip != null) {
                String ipStr = ip.getHostAddress();
                String[] segments = ipStr.split("\\.");
                if (segments.length == 4) {
                    int secondLastSegment = Integer.parseInt(segments[2]);
                    long datacenterId = secondLastSegment & 31; // 取最后5位
                    log.debug("根据IP地址生成datacenterId: IP={}, datacenterId={}", ipStr, datacenterId);
                    return datacenterId;
                }
            }

            // 方法2: 使用MAC地址的其他部分
            NetworkInterface network = NetworkInterface.getByInetAddress(ip);
            if (network != null) {
                byte[] mac = network.getHardwareAddress();
                if (mac != null && mac.length >= 6) {
                    long datacenterId = ((0x000000FF & (long) mac[mac.length - 3]) |
                                       (0x0000FF00 & (((long) mac[mac.length - 4]) << 8))) >> 6;
                    datacenterId = datacenterId & 31; // 取最后5位
                    log.debug("根据MAC地址生成datacenterId: datacenterId={}", datacenterId);
                    return datacenterId;
                }
            }
        } catch (Exception e) {
            log.warn("自动生成datacenterId失败，使用随机值", e);
        }

        // 方法3: 使用随机值（基于当前时间+偏移）
        long datacenterId = ((System.currentTimeMillis() >>> 3) + 7) & 31;
        log.debug("使用随机datacenterId: {}", datacenterId);
        return datacenterId;
    }

    /**
     * 获取本机局域网IP地址
     */
    private InetAddress getLocalHostLANAddress() {
        try {
            InetAddress candidateAddress = null;
            // 遍历所有的网络接口
            for (Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
                 interfaces.hasMoreElements(); ) {
                NetworkInterface networkInterface = interfaces.nextElement();
                
                // 过滤掉回环接口和非活动接口
                if (networkInterface.isLoopback() || !networkInterface.isUp()) {
                    continue;
                }
                
                // 遍历网络接口的所有IP地址
                for (Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                     addresses.hasMoreElements(); ) {
                    InetAddress address = addresses.nextElement();
                    
                    // 过滤掉回环地址和IPv6地址
                    if (address.isLoopbackAddress() || 
                        address.getHostAddress().indexOf(':') > -1) {
                        continue;
                    }
                    
                    // 优先选择site-local地址（私有IP）
                    if (address.isSiteLocalAddress()) {
                        return address;
                    }
                    
                    // 备选地址
                    if (candidateAddress == null) {
                        candidateAddress = address;
                    }
                }
            }
            
            if (candidateAddress != null) {
                return candidateAddress;
            }
            
            // 如果没有找到合适的地址，使用默认方法
            return InetAddress.getLocalHost();
            
        } catch (Exception e) {
            log.warn("获取本机IP地址失败", e);
            return null;
        }
    }

    /**
     * 验证ID参数
     */
    public static boolean isValidId(long id, String idName) {
        if (id < 0 || id > 31) {
            log.error("{}必须在0-31之间，当前值: {}", idName, id);
            return false;
        }
        return true;
    }

    /**
     * 获取当前配置信息
     */
    public String getConfigInfo() {
        return String.format("SnowflakeConfig{configWorkerId=%d, configDatacenterId=%d, autoGenerate=%s}",
                configWorkerId, configDatacenterId, autoGenerate);
    }
} 