package com.bigsincerity.accompany.common.enums;

import lombok.Getter;

/**
 * 用户类型枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum UserType {

    /**
     * 普通用户
     */
    NORMAL(1, "normal", "普通用户", "寻求陪伴服务的用户"),

    /**
     * 陪伴者/服务提供者
     */
    COMPANION(2, "companion", "陪伴者", "提供陪伴服务的用户"),

    /**
     * 管理员
     */
    ADMIN(3, "admin", "管理员", "系统管理员");

    /**
     * 类型代码
     */
    private final Integer code;

    /**
     * 类型标识
     */
    private final String type;

    /**
     * 类型名称
     */
    private final String name;

    /**
     * 类型描述
     */
    private final String description;

    UserType(Integer code, String type, String name, String description) {
        this.code = code;
        this.type = type;
        this.name = name;
        this.description = description;
    }

    /**
     * 根据代码获取用户类型
     */
    public static UserType fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (UserType userType : values()) {
            if (userType.getCode().equals(code)) {
                return userType;
            }
        }
        return null;
    }

    /**
     * 根据类型标识获取用户类型
     */
    public static UserType fromType(String type) {
        if (type == null || type.trim().isEmpty()) {
            return null;
        }
        for (UserType userType : values()) {
            if (userType.getType().equalsIgnoreCase(type)) {
                return userType;
            }
        }
        return null;
    }

    /**
     * 根据类型标识获取代码
     */
    public static Integer getCodeByType(String type) {
        UserType userType = fromType(type);
        return userType != null ? userType.getCode() : NORMAL.getCode();
    }

    /**
     * 检查是否为普通用户
     */
    public static boolean isNormal(Integer code) {
        return NORMAL.getCode().equals(code);
    }

    /**
     * 检查是否为陪伴者
     */
    public static boolean isCompanion(Integer code) {
        return COMPANION.getCode().equals(code);
    }

    /**
     * 检查是否为管理员
     */
    public static boolean isAdmin(Integer code) {
        return ADMIN.getCode().equals(code);
    }

    /**
     * 验证类型是否有效
     */
    public static boolean isValidType(String type) {
        return fromType(type) != null;
    }

    /**
     * 验证代码是否有效
     */
    public static boolean isValidCode(Integer code) {
        return fromCode(code) != null;
    }
} 