package com.bigsincerity.accompany.common.id;

import com.bigsincerity.accompany.common.result.CommonResultCode;
import com.bigsincerity.accompany.common.result.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * ID生成测试控制器
 * 用于测试和调试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/id")
@RequiredArgsConstructor
public class IdController {

    private final IdGeneratorUtil idGeneratorUtil;
    private final SnowflakeIdConfig snowflakeIdConfig;

    /**
     * 生成单个ID
     */
    @GetMapping("/generate")
    public Result<Map<String, Object>> generateId() {
        try {
            long id = idGeneratorUtil.nextId();
            SnowflakeIdGenerator.SnowflakeIdInfo idInfo = idGeneratorUtil.parseId(id);
            
            Map<String, Object> data = new HashMap<>();
            data.put("id", id);
            data.put("idStr", String.valueOf(id));
            data.put("timestamp", idInfo.getTimestamp());
            data.put("generateTime", new Date(idInfo.getTimestamp()));
            data.put("datacenterId", idInfo.getDatacenterId());
            data.put("workerId", idInfo.getWorkerId());
            data.put("sequence", idInfo.getSequence());
            
            return Result.success(data);
        } catch (Exception e) {
            log.error("生成ID失败", e);
            return Result.error(CommonResultCode.ERROR, "生成ID失败: " + e.getMessage());
        }
    }

    /**
     * 批量生成ID
     */
    @GetMapping("/generate/batch")
    public Result<Map<String, Object>> generateBatchIds(@RequestParam(defaultValue = "10") int count) {
        try {
            if (count <= 0 || count > 1000) {
                return Result.error(CommonResultCode.PARAM_ERROR, "数量必须在1-1000之间");
            }
            
            long[] ids = idGeneratorUtil.nextIds(count);
            
            Map<String, Object> data = new HashMap<>();
            data.put("count", count);
            data.put("ids", ids);
            data.put("generateTime", new Date());
            
            return Result.success(data);
        } catch (Exception e) {
            log.error("批量生成ID失败", e);
            return Result.error(CommonResultCode.ERROR, "批量生成ID失败: " + e.getMessage());
        }
    }

    /**
     * 解析ID信息
     */
    @GetMapping("/parse/{id}")
    public Result<Map<String, Object>> parseId(@PathVariable long id) {
        try {
            SnowflakeIdGenerator.SnowflakeIdInfo idInfo = idGeneratorUtil.parseId(id);
            
            Map<String, Object> data = new HashMap<>();
            data.put("id", id);
            data.put("isValid", idGeneratorUtil.isSnowflakeId(id));
            data.put("timestamp", idInfo.getTimestamp());
            data.put("generateTime", new Date(idInfo.getTimestamp()));
            data.put("datacenterId", idInfo.getDatacenterId());
            data.put("workerId", idInfo.getWorkerId());
            data.put("sequence", idInfo.getSequence());
            
            return Result.success(data);
        } catch (Exception e) {
            log.error("解析ID失败: {}", id, e);
            return Result.error(CommonResultCode.ERROR, "解析ID失败: " + e.getMessage());
        }
    }

    /**
     * 生成各种类型的ID
     */
    @GetMapping("/generate/types")
    public Result<Map<String, Object>> generateDifferentTypes() {
        try {
            Map<String, Object> data = new HashMap<>();
            
            // 基础ID
            data.put("basicId", idGeneratorUtil.nextId());
            data.put("basicIdStr", idGeneratorUtil.nextIdStr());
            data.put("shortId", idGeneratorUtil.nextShortId());
            
            // 业务ID
            data.put("userId", idGeneratorUtil.nextUserId());
            data.put("orderId", idGeneratorUtil.nextOrderId());
            data.put("sessionId", idGeneratorUtil.nextSessionId());
            data.put("deviceId", idGeneratorUtil.nextDeviceId());
            data.put("verificationCodeId", idGeneratorUtil.nextVerificationCodeId());
            
            // 特殊ID
            data.put("inviteCode", idGeneratorUtil.generateInviteCode());
            data.put("randomNumericId8", idGeneratorUtil.generateRandomNumericId(8));
            data.put("randomNumericId12", idGeneratorUtil.generateRandomNumericId(12));
            
            // 带前缀ID
            data.put("customPrefixId", idGeneratorUtil.nextIdWithPrefix("CUSTOM_"));
            
            data.put("generateTime", new Date());
            
            return Result.success(data);
        } catch (Exception e) {
            log.error("生成各种类型ID失败", e);
            return Result.error(CommonResultCode.ERROR, "生成各种类型ID失败: " + e.getMessage());
        }
    }

    /**
     * 性能测试
     */
    @GetMapping("/performance")
    public Result<Map<String, Object>> performanceTest(@RequestParam(defaultValue = "10000") int count) {
        try {
            if (count <= 0 || count > 100000) {
                return Result.error(CommonResultCode.PARAM_ERROR, "测试数量必须在1-100000之间");
            }
            
            long startTime = System.currentTimeMillis();
            
            // 生成指定数量的ID
            long[] ids = idGeneratorUtil.nextIds(count);
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            // 检查ID的唯一性
            long uniqueCount = java.util.Arrays.stream(ids).distinct().count();
            boolean isAllUnique = uniqueCount == count;
            
            Map<String, Object> data = new HashMap<>();
            data.put("count", count);
            data.put("duration", duration + "ms");
            data.put("avgTime", String.format("%.3f", (double) duration / count) + "ms");
            data.put("throughput", String.format("%.0f", (double) count * 1000 / duration) + "/s");
            data.put("isAllUnique", isAllUnique);
            data.put("uniqueCount", uniqueCount);
            data.put("duplicateCount", count - uniqueCount);
            data.put("firstId", ids[0]);
            data.put("lastId", ids[ids.length - 1]);
            
            return Result.success(data);
        } catch (Exception e) {
            log.error("性能测试失败", e);
            return Result.error(CommonResultCode.ERROR, "性能测试失败: " + e.getMessage());
        }
    }

    /**
     * 获取系统信息
     */
    @GetMapping("/info")
    public Result<Map<String, Object>> getSystemInfo() {
        try {
            Map<String, Object> data = new HashMap<>();
            
            // 雪花算法配置信息
            data.put("configInfo", snowflakeIdConfig.getConfigInfo());
            
            // 机器信息
            data.put("machineInfo", idGeneratorUtil.getMachineInfo());
            
            // 当前时间信息
            long currentTime = System.currentTimeMillis();
            data.put("currentTimestamp", currentTime);
            data.put("currentTime", new Date(currentTime));
            
            // 生成示例ID并解析
            long sampleId = idGeneratorUtil.nextId();
            SnowflakeIdGenerator.SnowflakeIdInfo sampleIdInfo = idGeneratorUtil.parseId(sampleId);
            data.put("sampleId", sampleId);
            data.put("sampleIdInfo", sampleIdInfo);
            
            return Result.success(data);
        } catch (Exception e) {
            log.error("获取系统信息失败", e);
            return Result.error(CommonResultCode.ERROR, "获取系统信息失败: " + e.getMessage());
        }
    }

    /**
     * 验证ID格式
     */
    @PostMapping("/validate")
    public Result<Map<String, Object>> validateId(@RequestParam String idStr) {
        try {
            Map<String, Object> data = new HashMap<>();
            data.put("input", idStr);
            
            try {
                long id = Long.parseLong(idStr);
                boolean isValid = idGeneratorUtil.isSnowflakeId(id);
                
                data.put("isValidFormat", true);
                data.put("isSnowflakeId", isValid);
                data.put("id", id);
                
                if (isValid) {
                    SnowflakeIdGenerator.SnowflakeIdInfo idInfo = idGeneratorUtil.parseId(id);
                    data.put("idInfo", idInfo);
                    data.put("generateTime", new Date(idInfo.getTimestamp()));
                }
                
            } catch (NumberFormatException e) {
                data.put("isValidFormat", false);
                data.put("isSnowflakeId", false);
                data.put("error", "不是有效的数字格式");
            }
            
            return Result.success(data);
        } catch (Exception e) {
            log.error("验证ID失败: {}", idStr, e);
            return Result.error(CommonResultCode.ERROR, "验证ID失败: " + e.getMessage());
        }
    }
} 