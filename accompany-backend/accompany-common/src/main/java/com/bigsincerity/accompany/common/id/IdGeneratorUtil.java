package com.bigsincerity.accompany.common.id;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.Date;
import java.util.concurrent.ThreadLocalRandom;

/**
 * ID生成工具类
 * 
 * 提供多种ID生成方式：
 * 1. 雪花算法生成全局唯一ID
 * 2. 短ID生成（基于雪花算法）
 * 3. 字符串ID生成
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class IdGeneratorUtil {

    private final SnowflakeIdGenerator snowflakeIdGenerator;

    /**
     * 自动装配雪花算法ID生成器
     */
    @Autowired
    public IdGeneratorUtil(SnowflakeIdGenerator snowflakeIdGenerator) {
        this.snowflakeIdGenerator = snowflakeIdGenerator;
        log.info("ID生成工具类初始化完成");
    }

    /**
     * 生成下一个ID（雪花算法）
     * 
     * @return 唯一ID
     */
    public long nextId() {
        return snowflakeIdGenerator.nextId();
    }

    /**
     * 生成下一个ID并转换为字符串
     * 
     * @return 唯一ID字符串
     */
    public String nextIdStr() {
        return String.valueOf(snowflakeIdGenerator.nextId());
    }

    /**
     * 生成短ID（去掉时间戳高位，保持18位长度）
     * 适用于对ID长度有要求的场景
     * 
     * @return 短ID
     */
    public long nextShortId() {
        long id = snowflakeIdGenerator.nextId();
        // 去掉高位时间戳，保持唯一性的同时缩短长度
        return Math.abs(id % 1000000000000000000L); // 18位
    }

    /**
     * 生成带前缀的字符串ID
     * 
     * @param prefix 前缀
     * @return 带前缀的ID字符串
     */
    public String nextIdWithPrefix(String prefix) {
        return prefix + snowflakeIdGenerator.nextId();
    }

    /**
     * 生成用户ID
     * 
     * @return 用户ID
     */
    public long nextUserId() {
        return nextId();
    }

    /**
     * 生成订单ID
     * 
     * @return 订单ID字符串（带前缀）
     */
    public String nextOrderId() {
        return "ORDER" + nextId();
    }

    /**
     * 生成会话ID
     * 
     * @return 会话ID字符串
     */
    public String nextSessionId() {
        return "SESSION" + nextId();
    }

    /**
     * 生成设备ID
     * 
     * @return 设备ID字符串
     */
    public String nextDeviceId() {
        return "DEVICE" + nextId();
    }

    /**
     * 生成验证码ID
     * 
     * @return 验证码ID
     */
    public long nextVerificationCodeId() {
        return nextId();
    }

    /**
     * 解析雪花算法ID
     * 
     * @param id 雪花算法生成的ID
     * @return ID信息
     */
    public SnowflakeIdGenerator.SnowflakeIdInfo parseId(long id) {
        return SnowflakeIdGenerator.parseId(id);
    }

    /**
     * 解析雪花算法ID字符串
     * 
     * @param idStr 雪花算法生成的ID字符串
     * @return ID信息
     */
    public SnowflakeIdGenerator.SnowflakeIdInfo parseId(String idStr) {
        try {
            long id = Long.parseLong(idStr);
            return SnowflakeIdGenerator.parseId(id);
        } catch (NumberFormatException e) {
            log.error("解析ID失败，无效的ID格式: {}", idStr, e);
            throw new IllegalArgumentException("无效的ID格式: " + idStr);
        }
    }

    /**
     * 获取ID生成时间
     * 
     * @param id 雪花算法生成的ID
     * @return 生成时间
     */
    public Date getIdGenerateTime(long id) {
        SnowflakeIdGenerator.SnowflakeIdInfo idInfo = parseId(id);
        return new Date(idInfo.getTimestamp());
    }

    /**
     * 检查ID是否为雪花算法生成
     * 
     * @param id ID
     * @return 是否为雪花算法生成
     */
    public boolean isSnowflakeId(long id) {
        try {
            SnowflakeIdGenerator.SnowflakeIdInfo idInfo = parseId(id);
            // 检查时间戳是否合理（不能早于起始时间，不能晚于当前时间+1小时）
            long currentTime = System.currentTimeMillis();
            long idTime = idInfo.getTimestamp();
            return idTime >= 1704067200000L && idTime <= currentTime + 3600000L;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 批量生成ID
     * 
     * @param count 生成数量
     * @return ID数组
     */
    public long[] nextIds(int count) {
        if (count <= 0) {
            throw new IllegalArgumentException("生成数量必须大于0");
        }
        if (count > 10000) {
            throw new IllegalArgumentException("单次生成数量不能超过10000");
        }

        long[] ids = new long[count];
        for (int i = 0; i < count; i++) {
            ids[i] = nextId();
        }
        return ids;
    }

    /**
     * 生成随机数字ID（非雪花算法，用于特殊场景）
     * 
     * @param length ID长度
     * @return 随机数字ID
     */
    public String generateRandomNumericId(int length) {
        if (length <= 0 || length > 18) {
            throw new IllegalArgumentException("ID长度必须在1-18之间");
        }

        StringBuilder id = new StringBuilder();
        // 第一位不能为0
        id.append(ThreadLocalRandom.current().nextInt(1, 10));
        
        // 其余位可以为0-9
        for (int i = 1; i < length; i++) {
            id.append(ThreadLocalRandom.current().nextInt(0, 10));
        }
        
        return id.toString();
    }

    /**
     * 生成邀请码（6位字母数字组合）
     * 
     * @return 邀请码
     */
    public String generateInviteCode() {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        StringBuilder code = new StringBuilder();
        ThreadLocalRandom random = ThreadLocalRandom.current();
        
        for (int i = 0; i < 6; i++) {
            code.append(chars.charAt(random.nextInt(chars.length())));
        }
        
        return code.toString();
    }

    /**
     * 获取机器信息（用于调试）
     * 
     * @return 机器信息字符串
     */
    public String getMachineInfo() {
        try {
            InetAddress ip = InetAddress.getLocalHost();
            NetworkInterface network = NetworkInterface.getByInetAddress(ip);
            byte[] mac = network.getHardwareAddress();
            
            StringBuilder macStr = new StringBuilder();
            for (int i = 0; i < mac.length; i++) {
                macStr.append(String.format("%02X%s", mac[i], (i < mac.length - 1) ? "-" : ""));
            }
            
            return String.format("IP: %s, MAC: %s", ip.getHostAddress(), macStr.toString());
        } catch (Exception e) {
            log.warn("获取机器信息失败", e);
            return "无法获取机器信息";
        }
    }
} 