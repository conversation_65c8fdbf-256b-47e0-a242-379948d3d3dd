package com.bigsincerity.accompany.common.result;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 异常码验证使用示例
 * 展示如何使用验证器来检测重复的异常码
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class ResultCodeValidationExample {

    @Autowired
    private ResultCodeValidator validator;

    @Autowired
    private ResultCodeChecker checker;

    /**
     * 示例：验证所有异常码
     */
    public void validateAllCodes() {
        System.out.println("=== 开始验证所有异常码 ===");
        
        ResultCodeValidator.ValidationResult result = validator.validateAllResultCodes();
        
        if (result.isValid()) {
            System.out.println("✅ 所有异常码验证通过！");
        } else {
            System.out.println("❌ 发现验证错误：");
            for (String error : result.getErrors()) {
                System.out.println("  - " + error);
            }
        }
        
        if (!result.getWarnings().isEmpty()) {
            System.out.println("⚠️  发现警告：");
            for (String warning : result.getWarnings()) {
                System.out.println("  - " + warning);
            }
        }
        
        System.out.println("=== 验证完成 ===\n");
    }

    /**
     * 示例：验证特定模块的异常码
     */
    public void validateModule(String moduleName) {
        System.out.println("=== 验证模块: " + moduleName + " ===");
        
        ResultCodeValidator.ValidationResult result = validator.validateModule(moduleName);
        
        if (result.isValid()) {
            System.out.println("✅ 模块 " + moduleName + " 验证通过！");
        } else {
            System.out.println("❌ 模块 " + moduleName + " 发现错误：");
            for (String error : result.getErrors()) {
                System.out.println("  - " + error);
            }
        }
        
        System.out.println("=== 模块验证完成 ===\n");
    }

    /**
     * 示例：检查单个异常码
     */
    public void checkSingleCode(Integer code, String module) {
        System.out.println("=== 检查异常码: " + code + " (模块: " + module + ") ===");
        
        ResultCodeChecker.CheckResult result = checker.checkCode(code, module);
        
        if (result.isValid()) {
            System.out.println("✅ 异常码 " + code + " 检查通过！");
        } else {
            System.out.println("❌ 异常码 " + code + " 发现问题：");
            for (String error : result.getErrors()) {
                System.out.println("  - " + error);
            }
        }
        
        if (!result.getWarnings().isEmpty()) {
            System.out.println("⚠️  发现警告：");
            for (String warning : result.getWarnings()) {
                System.out.println("  - " + warning);
            }
        }
        
        if (!result.getSuggestions().isEmpty()) {
            System.out.println("💡 建议：");
            for (String suggestion : result.getSuggestions()) {
                System.out.println("  - " + suggestion);
            }
        }
        
        System.out.println("=== 检查完成 ===\n");
    }

    /**
     * 示例：获取下一个可用的异常码
     */
    public void getNextAvailableCode(String module) {
        System.out.println("=== 获取模块 " + module + " 的下一个可用异常码 ===");
        
        Integer nextCode = checker.getNextAvailableCode(module);
        
        if (nextCode != null) {
            System.out.println("✅ 下一个可用异常码: " + nextCode);
        } else {
            System.out.println("❌ 模块 " + module + " 的异常码已用完！");
        }
        
        System.out.println("=== 获取完成 ===\n");
    }

    /**
     * 示例：模拟开发时的实时检查
     */
    public void simulateDevelopmentCheck() {
        System.out.println("=== 模拟开发时的实时检查 ===");
        
        // 模拟开发者在添加新的异常码时的检查
        System.out.println("开发者正在添加新的异常码...");
        
        // 检查一个可能重复的异常码
        checkSingleCode(401001, "AUTH"); // 这个应该重复
        
        // 检查一个超出范围的异常码
        checkSingleCode(500000, "AUTH"); // 这个应该超出范围
        
        // 检查一个正确的异常码
        checkSingleCode(401999, "AUTH"); // 这个应该在范围内
        
        // 获取下一个可用的异常码
        getNextAvailableCode("AUTH");
        
        System.out.println("=== 模拟完成 ===\n");
    }

    /**
     * 示例：完整的验证流程
     */
    public void runFullValidation() {
        System.out.println("🚀 开始完整的异常码验证流程\n");
        
        // 1. 验证所有异常码
        validateAllCodes();
        
        // 2. 验证各个模块
        validateModule("COMMON");
        validateModule("AUTH");
        validateModule("USER");
        validateModule("SERVICE");
        
        // 3. 模拟开发检查
        simulateDevelopmentCheck();
        
        // 4. 获取各模块的下一个可用异常码
        System.out.println("📋 各模块的下一个可用异常码：");
        getNextAvailableCode("COMMON");
        getNextAvailableCode("AUTH");
        getNextAvailableCode("USER");
        getNextAvailableCode("SERVICE");
        
        System.out.println("🎉 完整验证流程结束！");
    }

    /**
     * 示例：在CI/CD中使用
     */
    public boolean validateForCI() {
        System.out.println("🔍 CI/CD验证开始...");
        
        ResultCodeValidator.ValidationResult result = validator.validateAllResultCodes();
        
        if (result.isValid()) {
            System.out.println("✅ CI/CD验证通过");
            return true;
        } else {
            System.out.println("❌ CI/CD验证失败：");
            for (String error : result.getErrors()) {
                System.out.println("  - " + error);
            }
            return false;
        }
    }
} 