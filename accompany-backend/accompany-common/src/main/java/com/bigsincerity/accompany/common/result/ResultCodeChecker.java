package com.bigsincerity.accompany.common.result;

import com.bigsincerity.accompany.common.result.IResultCode.ModuleName;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 异常码检查器
 * 提供开发时的实时检查功能，可以在IDE中集成使用
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class ResultCodeChecker {

    @Autowired
    private ResultCodeValidator validator;

    // 缓存已检查的异常码，避免重复检查
    private final Map<Integer, IResultCode> checkedCodes = new ConcurrentHashMap<>();
    private final Map<String, Set<String>> moduleMessageKeys = new ConcurrentHashMap<>();

    /**
     * 检查单个异常码
     */
    public CheckResult checkCode(Integer code, String module) {
        CheckResult result = new CheckResult();
        
        // 检查是否重复
        if (checkedCodes.containsKey(code)) {
            IResultCode existing = checkedCodes.get(code);
            result.addError(String.format("异常码 %d 已存在，在 %s.%s 中", 
                code, existing.getModuleName(), existing.getClass().getSimpleName()));
        } else {
            // 检查号段范围
            if (!isInModuleRange(code, module)) {
                result.addError(String.format("异常码 %d 超出模块 %s 的范围", code, module));
            }
        }
        
        return result;
    }

    /**
     * 检查消息键
     */
    public CheckResult checkMessageKey(String messageKey, String module) {
        CheckResult result = new CheckResult();
        
        Set<String> moduleKeys = moduleMessageKeys.computeIfAbsent(module, k -> new HashSet<>());
        if (moduleKeys.contains(messageKey)) {
            result.addError(String.format("消息键 %s 在模块 %s 中已存在", messageKey, module));
        } else {
            moduleKeys.add(messageKey);
        }
        
        return result;
    }

    /**
     * 检查异常码范围
     */
    private boolean isInModuleRange(Integer code, String module) {
        ResultCodeValidator.CodeRange range = getModuleRange(module);
        return range != null && range.isInRange(code);
    }

    /**
     * 获取模块范围
     */
    private ResultCodeValidator.CodeRange getModuleRange(String module) {
        switch (module) {
            case "COMMON":
                return new ResultCodeValidator.CodeRange(200, 299, 400, 499, 500, 599);
            case "USER":
                return new ResultCodeValidator.CodeRange(400000, 400999);
            case "AUTH":
                return new ResultCodeValidator.CodeRange(401000, 401999);
            case "SERVICE":
                return new ResultCodeValidator.CodeRange(402000, 402999);
            case "ORDER":
                return new ResultCodeValidator.CodeRange(403000, 403999);
            case "PAYMENT":
                return new ResultCodeValidator.CodeRange(404000, 404999);
            case "CHAT":
                return new ResultCodeValidator.CodeRange(405000, 405999);
            case "COMMUNITY":
                return new ResultCodeValidator.CodeRange(406000, 406999);
            case "FILE":
                return new ResultCodeValidator.CodeRange(407000, 407999);
            case "THIRD_PARTY":
                return new ResultCodeValidator.CodeRange(500000, 500999);
            default:
                return null;
        }
    }

    /**
     * 获取下一个可用的异常码
     */
    public Integer getNextAvailableCode(String module) {
        Set<Integer> usedCodes = getUsedCodesInModule(module);
        ResultCodeValidator.CodeRange range = getModuleRange(module);
        
        if (range == null) {
            return null;
        }
        
        // 这里简化处理，实际应该根据具体的范围逻辑来查找
        int start = getModuleStartCode(module);
        int end = getModuleEndCode(module);
        
        for (int i = start; i <= end; i++) {
            if (!usedCodes.contains(i)) {
                return i;
            }
        }
        
        return null;
    }

    /**
     * 获取模块中已使用的异常码
     */
    private Set<Integer> getUsedCodesInModule(String module) {
        Set<Integer> usedCodes = new HashSet<>();
        
        // 从现有的枚举中获取已使用的异常码
        try {
            Set<Class<? extends IResultCode>> enumClasses = validator.findResultCodeEnums();
            for (Class<? extends IResultCode> enumClass : enumClasses) {
                IResultCode[] values = (IResultCode[]) enumClass.getMethod("values").invoke(null);
                for (IResultCode code : values) {
                    if (module.equals(code.getModuleName())) {
                        usedCodes.add(code.getCode());
                    }
                }
            }
        } catch (Exception e) {
            // 忽略异常，返回空集合
        }
        
        return usedCodes;
    }

    /**
     * 获取模块起始异常码
     */
    private int getModuleStartCode(String module) {
        if(ModuleName.COMMON.name().equals(module)) {
            return 200;
        } else if(ModuleName.USER.name().equals(module)) {
            return 400000;
        } else if(ModuleName.AUTH.name().equals(module)) {
            return 401000;
        } else if(ModuleName.SERVICE.name().equals(module)) {
            return 402000;
        } else if(ModuleName.ORDER.name().equals(module)) {
            return 403000;
        } else if(ModuleName.PAYMENT.name().equals(module)) {
            return 404000;
        } else if(ModuleName.CHAT.name().equals(module)) {
            return 405000;
        } else if(ModuleName.COMMUNITY.name().equals(module)) {
            return 406000;
        } else if(ModuleName.FILE.name().equals(module)) {
            return 407000;
        } else if(ModuleName.THIRD_PARTY.name().equals(module)) {
            return 500000;
        }
        return 0;
    }

    /**
     * 获取模块结束异常码
     */
    private int getModuleEndCode(String module) {
        if(ModuleName.COMMON.name().equals(module)) {
            return 599;
        } else if(ModuleName.USER.name().equals(module)) {
            return 400999;
        } else if(ModuleName.AUTH.name().equals(module)) {
            return 401999;
        } else if(ModuleName.SERVICE.name().equals(module)) {
            return 402999;
        } else if(ModuleName.ORDER.name().equals(module)) {
            return 403999;
        } else if(ModuleName.PAYMENT.name().equals(module)) {   
            return 404999;
        } else if(ModuleName.CHAT.name().equals(module)) {
            return 405999;
        } else if(ModuleName.COMMUNITY.name().equals(module)) {
            return 406999;
        } else if(ModuleName.FILE.name().equals(module)) {
            return 407999;
        } else if(ModuleName.THIRD_PARTY.name().equals(module)) {
            return 500999;
        }
        return 0;
    }

    /**
     * 检查结果
     */
    public static class CheckResult {
        private final List<String> errors = new ArrayList<>();
        private final List<String> warnings = new ArrayList<>();
        private final List<String> suggestions = new ArrayList<>();

        public void addError(String error) {
            errors.add(error);
        }

        public void addWarning(String warning) {
            warnings.add(warning);
        }

        public void addSuggestion(String suggestion) {
            suggestions.add(suggestion);
        }

        public boolean isValid() {
            return errors.isEmpty();
        }

        public List<String> getErrors() {
            return errors;
        }

        public List<String> getWarnings() {
            return warnings;
        }

        public List<String> getSuggestions() {
            return suggestions;
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            
            if (!errors.isEmpty()) {
                sb.append("❌ 错误:\n");
                for (String error : errors) {
                    sb.append("  - ").append(error).append("\n");
                }
            }
            
            if (!warnings.isEmpty()) {
                sb.append("⚠️  警告:\n");
                for (String warning : warnings) {
                    sb.append("  - ").append(warning).append("\n");
                }
            }
            
            if (!suggestions.isEmpty()) {
                sb.append("💡 建议:\n");
                for (String suggestion : suggestions) {
                    sb.append("  - ").append(suggestion).append("\n");
                }
            }
            
            return sb.toString();
        }
    }
} 