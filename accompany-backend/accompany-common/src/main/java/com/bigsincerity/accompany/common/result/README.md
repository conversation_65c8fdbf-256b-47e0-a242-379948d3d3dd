# 响应状态码系统设计文档

## 概述

本系统提供了一个模块化、可扩展、支持国际化的响应状态码管理系统，用于统一管理Accompany项目中的所有异常码。

## 设计理念

### 1. 模块化设计
- 每个业务模块都有独立的异常码枚举
- 异常码按功能分类，便于维护和扩展
- 支持模块级别的异常码管理

### 2. 国际化支持
- 支持多语言消息配置
- 使用Spring的MessageSource进行国际化
- 提供默认消息作为兜底

### 3. 可扩展性
- 预留了足够的异常码范围供未来扩展
- 支持动态注册新的异常码模块
- 错误级别分类便于监控和日志记录

## 异常码范围分配

| 模块 | 范围 | 说明 |
|------|------|------|
| 通用模块 | 200-299, 400-499, 500-599 | 系统级别的通用异常码 |
| 用户模块 | 400000-400999 | 用户相关的业务异常码 |
| 认证授权模块 | 401000-401999 | 认证授权相关的异常码 |
| 服务模块 | 402000-402999 | 服务相关的业务异常码 |
| 订单模块 | 403000-403999 | 订单相关的业务异常码 |
| 支付模块 | 404000-404999 | 支付相关的业务异常码 |
| 聊天模块 | 405000-405999 | 聊天相关的业务异常码 |
| 社区模块 | 406000-406999 | 社区相关的业务异常码 |
| 文件模块 | 407000-407999 | 文件上传相关的异常码 |
| 第三方服务模块 | 500000-500999 | 第三方服务相关的异常码 |

## 核心组件

### 1. IResultCode 接口
定义异常码的基本结构：
- `getCode()`: 获取状态码
- `getMessageKey()`: 获取国际化消息键
- `getDefaultMessage()`: 获取默认消息
- `getModule()`: 获取模块名称
- `getErrorLevel()`: 获取错误级别

### 2. ResultCodeManager 管理器
统一管理所有异常码：
- 支持根据状态码查找异常码
- 支持根据消息键查找异常码
- 提供国际化消息获取功能
- 支持按模块和错误级别查询

### 3. 模块化异常码枚举
每个模块都有独立的异常码枚举：
- `CommonResultCode`: 通用异常码
- `AuthResultCode`: 认证授权异常码
- `UserResultCode`: 用户模块异常码
- `ServiceResultCode`: 服务模块异常码

## 使用方法

### 1. 基本使用

```java
@Autowired
private ResultCodeManager resultCodeManager;

// 使用预定义的异常码
IResultCode errorCode = AuthResultCode.TOKEN_EXPIRED;
String message = resultCodeManager.getMessage(errorCode);
```

### 2. 在业务逻辑中使用

```java
public void login(String username, String password) {
    if (invalidCredentials) {
        IResultCode errorCode = AuthResultCode.LOGIN_CREDENTIALS_ERROR;
        String errorMessage = resultCodeManager.getMessage(errorCode);
        throw new BusinessException(errorCode.getCode(), errorMessage);
    }
}
```

### 3. 查找异常码

```java
// 根据状态码查找
IResultCode resultCode = resultCodeManager.findByCode(401001);

// 根据消息键查找
IResultCode resultCode = resultCodeManager.findByMessageKey("auth.token.expired");
```

### 4. 获取模块异常码

```java
// 获取认证模块的所有异常码
Map<Integer, IResultCode> authCodes = resultCodeManager.getResultCodesByModule("AUTH");

// 获取所有警告级别的异常码
Map<Integer, IResultCode> warningCodes = resultCodeManager.getResultCodesByErrorLevel(ErrorLevel.WARNING);
```

## 国际化配置

### 1. 消息文件
- `messages_zh_CN.properties`: 中文消息
- `messages_en_US.properties`: 英文消息

### 2. 消息键命名规范
- 格式：`{module}.{category}.{action}`
- 示例：`auth.token.expired`、`user.password.error`

### 3. 添加新语言
1. 创建新的消息文件：`messages_{locale}.properties`
2. 添加对应的消息配置
3. Spring会自动加载新的语言文件

## 扩展指南

### 1. 添加新的异常码模块

```java
public enum NewModuleResultCode implements IResultCode {
    NEW_ERROR(408001, "newmodule.error", "新模块错误", "NEW_MODULE", ErrorLevel.WARNING);
    
    // 实现接口方法...
}
```

### 2. 注册新模块

在 `ResultCodeManager` 的 `initializeResultCodes()` 方法中添加：

```java
// 注册新模块异常码
registerResultCodes(NewModuleResultCode.values());
```

### 3. 添加国际化消息

在消息文件中添加对应的消息配置：

```properties
# messages_zh_CN.properties
newmodule.error=新模块错误

# messages_en_US.properties
newmodule.error=New module error
```

## 错误级别说明

| 级别 | 说明 | 使用场景 |
|------|------|----------|
| INFO | 信息级别 | 正常的业务提示信息 |
| WARNING | 警告级别 | 业务异常，但不影响系统运行 |
| ERROR | 错误级别 | 系统错误，需要关注 |
| CRITICAL | 严重错误级别 | 系统严重错误，需要立即处理 |

## 最佳实践

### 1. 异常码设计原则
- 保持异常码的唯一性
- 使用有意义的错误消息
- 按功能模块分类
- 预留扩展空间

### 2. 国际化消息设计
- 使用简洁明了的消息
- 避免技术术语
- 提供上下文信息
- 保持消息的一致性

### 3. 性能考虑
- 异常码管理器使用缓存
- 避免频繁的国际化消息查找
- 合理使用错误级别

## 监控和日志

### 1. 错误级别监控
- INFO: 记录业务操作日志
- WARNING: 监控业务异常
- ERROR: 监控系统错误
- CRITICAL: 立即告警

### 2. 模块统计
- 按模块统计异常码使用频率
- 监控异常码的分布情况
- 分析异常码的使用趋势

## 迁移指南

### 从旧系统迁移
1. 将旧的异常码映射到新的模块化异常码
2. 更新业务代码中的异常码使用
3. 添加国际化消息配置
4. 测试验证迁移结果

### 兼容性考虑
- 保持向后兼容
- 提供迁移工具
- 分阶段进行迁移

## 总结

这个异常码系统提供了：
- 模块化的异常码管理
- 完整的国际化支持
- 灵活的扩展机制
- 清晰的错误级别分类
- 统一的管理接口

通过这个系统，可以更好地管理项目中的异常码，提高代码的可维护性和用户体验。 