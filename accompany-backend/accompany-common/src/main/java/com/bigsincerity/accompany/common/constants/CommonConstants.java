package com.bigsincerity.accompany.common.constants;

/**
 * 通用常量类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class CommonConstants {

    /**
     * 系统配置
     */
    public static class System {
        /** 系统名称 */
        public static final String SYSTEM_NAME = "Accompany";
        
        /** 系统版本 */
        public static final String SYSTEM_VERSION = "1.0.0";
        
        /** 默认编码 */
        public static final String DEFAULT_CHARSET = "UTF-8";
        
        /** 默认时区 */
        public static final String DEFAULT_TIMEZONE = "Asia/Shanghai";
    }

    /**
     * HTTP状态相关
     */
    public static class Http {
        /** 成功状态码 */
        public static final int SUCCESS_CODE = 200;
        
        /** 客户端错误状态码 */
        public static final int CLIENT_ERROR_CODE = 400;
        
        /** 服务器错误状态码 */
        public static final int SERVER_ERROR_CODE = 500;
        
        /** 未授权状态码 */
        public static final int UNAUTHORIZED_CODE = 401;
        
        /** 禁止访问状态码 */
        public static final int FORBIDDEN_CODE = 403;
    }

    /**
     * 缓存相关
     */
    public static class Cache {
        /** 默认缓存前缀 */
        public static final String DEFAULT_PREFIX = "accompany:";
        
        /** 用户缓存前缀 */
        public static final String USER_PREFIX = DEFAULT_PREFIX + "user:";
        
        /** 短信验证码缓存前缀 */
        public static final String SMS_CODE_PREFIX = DEFAULT_PREFIX + "sms:code:";
        
        /** 短信发送限制缓存前缀 */
        public static final String SMS_LIMIT_PREFIX = DEFAULT_PREFIX + "sms:limit:";
        
        /** JWT黑名单缓存前缀 */
        public static final String JWT_BLACKLIST_PREFIX = DEFAULT_PREFIX + "jwt:blacklist:";
    }

    /**
     * 时间相关
     */
    public static class Time {
        /** 短信验证码有效期（分钟） */
        public static final int SMS_CODE_EXPIRE_MINUTES = 5;
        
        /** 短信发送间隔（秒） */
        public static final int SMS_SEND_INTERVAL_SECONDS = 60;
        
        /** JWT访问令牌有效期（分钟） */
        public static final int JWT_ACCESS_TOKEN_EXPIRE_MINUTES = 120;
        
        /** JWT刷新令牌有效期（天） */
        public static final int JWT_REFRESH_TOKEN_EXPIRE_DAYS = 7;
        
        /** 会话超时时间（分钟） */
        public static final int SESSION_TIMEOUT_MINUTES = 30;
    }

    /**
     * 业务规则
     */
    public static class Business {
        /** 密码最小长度 */
        public static final int PASSWORD_MIN_LENGTH = 8;
        
        /** 密码最大长度 */
        public static final int PASSWORD_MAX_LENGTH = 20;
        
        /** 用户名最小长度 */
        public static final int USERNAME_MIN_LENGTH = 2;
        
        /** 用户名最大长度 */
        public static final int USERNAME_MAX_LENGTH = 20;
        
        /** 验证码长度 */
        public static final int VERIFICATION_CODE_LENGTH = 6;
        
        /** 推荐码长度 */
        public static final int REFERRAL_CODE_LENGTH = 8;
        
        /** 默认初始积分 */
        public static final int DEFAULT_INITIAL_POINTS = 0;
        
        /** 默认初始信用分 */
        public static final int DEFAULT_INITIAL_CREDIT_SCORE = 100;
        
        /** 推荐奖励积分（推荐人） */
        public static final int REFERRAL_REWARD_POINTS = 10;
        
        /** 推荐奖励积分（新用户） */
        public static final int NEW_USER_REWARD_POINTS = 5;
    }

    /**
     * 正则表达式
     */
    public static class Regex {
        /** 手机号正则 */
        public static final String PHONE_PATTERN = "^1[3-9]\\d{9}$";
        
        /** 邮箱正则 */
        public static final String EMAIL_PATTERN = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$";
        
        /** 用户名正则（中文、英文、数字、下划线） */
        public static final String USERNAME_PATTERN = "^[a-zA-Z0-9_\\u4e00-\\u9fa5]+$";
        
        /** 密码强度正则（大小写字母、数字） */
        public static final String STRONG_PASSWORD_PATTERN = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]+$";
        
        /** 验证码正则（6位数字） */
        public static final String VERIFICATION_CODE_PATTERN = "^\\d{6}$";
    }

    /**
     * 消息模板
     */
    public static class MessageTemplate {
        /** 注册成功消息 */
        public static final String REGISTER_SUCCESS = "注册成功";
        
        /** 登录成功消息 */
        public static final String LOGIN_SUCCESS = "登录成功";
        
        /** 验证码发送成功消息 */
        public static final String SMS_SEND_SUCCESS = "验证码发送成功";
        
        /** 欢迎消息模板 */
        public static final String WELCOME_MESSAGE_TEMPLATE = "欢迎加入%s！您的用户名是：%s，推荐码：%s";
    }

    /**
     * 错误消息
     */
    public static class ErrorMessage {
        /** 用户不存在 */
        public static final String USER_NOT_FOUND = "用户不存在";
        
        /** 密码错误 */
        public static final String PASSWORD_ERROR = "密码错误";
        
        /** 验证码错误 */
        public static final String VERIFICATION_CODE_ERROR = "验证码错误或已过期";
        
        /** 账户被禁用 */
        public static final String ACCOUNT_DISABLED = "账户已被禁用";
        
        /** 账户被锁定 */
        public static final String ACCOUNT_LOCKED = "账户已被锁定";
        
        /** 用户名已存在 */
        public static final String USERNAME_EXISTS = "用户名已存在";
        
        /** 手机号已注册 */
        public static final String PHONE_EXISTS = "手机号已注册";
        
        /** 邮箱已注册 */
        public static final String EMAIL_EXISTS = "邮箱已注册";
    }
} 