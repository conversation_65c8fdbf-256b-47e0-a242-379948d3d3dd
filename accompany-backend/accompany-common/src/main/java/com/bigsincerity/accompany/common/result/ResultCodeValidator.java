package com.bigsincerity.accompany.common.result;

import com.bigsincerity.accompany.common.result.IResultCode.ModuleName;
import org.springframework.stereotype.Component;
import org.springframework.context.ApplicationContext;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.lang.reflect.Field;

/**
 * 响应状态码验证器
 * 用于检测重复的异常码和验证号段分配
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class ResultCodeValidator {

    @Autowired
    private ApplicationContext applicationContext;

    // 异常码范围定义
    private static final Map<String, CodeRange> MODULE_RANGES = new HashMap<>();
    
    static {
        // 定义各模块的异常码范围
        MODULE_RANGES.put(ModuleName.COMMON.name(), new CodeRange(200, 299, 400, 499, 500, 599));
        MODULE_RANGES.put(ModuleName.USER.name(), new CodeRange(400000, 400999));
        MODULE_RANGES.put(ModuleName.AUTH.name(), new CodeRange(401000, 401999));
        MODULE_RANGES.put(ModuleName.SERVICE.name(), new CodeRange(402000, 402999));
        MODULE_RANGES.put(ModuleName.ORDER.name(), new CodeRange(403000, 403999));
        MODULE_RANGES.put(ModuleName.PAYMENT.name(), new CodeRange(404000, 404999));
        MODULE_RANGES.put(ModuleName.CHAT.name(), new CodeRange(405000, 405999));
        MODULE_RANGES.put(ModuleName.COMMUNITY.name(), new CodeRange(406000, 406999));
        MODULE_RANGES.put(ModuleName.FILE.name(), new CodeRange(407000, 407999));
        MODULE_RANGES.put(ModuleName.THIRD_PARTY.name(), new CodeRange(500000, 500999));
    }

    /**
     * 验证所有异常码
     */
    public ValidationResult validateAllResultCodes() {
        ValidationResult result = new ValidationResult();
        
        try {
            // 获取所有实现了IResultCode的枚举类
            Set<Class<? extends IResultCode>> enumClasses = findResultCodeEnums();
            
            // 收集所有异常码
            Map<Integer, IResultCode> allCodes = new HashMap<>();
            Map<String, List<IResultCode>> moduleCodes = new HashMap<>();
            
            for (Class<? extends IResultCode> enumClass : enumClasses) {
                IResultCode[] values = getEnumValues(enumClass);
                for (IResultCode code : values) {
                    // 检查重复
                    if (allCodes.containsKey(code.getCode())) {
                        IResultCode existing = allCodes.get(code.getCode());
                        result.addDuplicateError(code, existing);
                    } else {
                        allCodes.put(code.getCode(), code);
                    }
                    
                    // 按模块分组
                    moduleCodes.computeIfAbsent(code.getModuleName(), k -> new ArrayList<>()).add(code);
                }
            }
            
            // 验证号段分配
            for (Map.Entry<String, List<IResultCode>> entry : moduleCodes.entrySet()) {
                String module = entry.getKey();
                List<IResultCode> codes = entry.getValue();
                validateModuleRange(module, codes, result);
            }
            
            // 验证消息键重复
            validateMessageKeys(allCodes.values(), result);
            
        } catch (Exception e) {
            result.addSystemError("验证过程中发生系统错误: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 验证单个模块的异常码
     */
    public ValidationResult validateModule(String moduleName) {
        ValidationResult result = new ValidationResult();
        
        try {
            Set<Class<? extends IResultCode>> enumClasses = findResultCodeEnums();
            List<IResultCode> moduleCodes = new ArrayList<>();
            
            for (Class<? extends IResultCode> enumClass : enumClasses) {
                IResultCode[] values = getEnumValues(enumClass);
                for (IResultCode code : values) {
                    if (moduleName.equals(code.getModuleName())) {
                        moduleCodes.add(code);
                    }
                }
            }
            
            // 验证号段分配
            validateModuleRange(moduleName, moduleCodes, result);
            
            // 验证模块内重复
            validateModuleDuplicates(moduleCodes, result);
            
        } catch (Exception e) {
            result.addSystemError("验证模块 " + moduleName + " 时发生错误: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 验证号段分配
     */
    private void validateModuleRange(String module, List<IResultCode> codes, ValidationResult result) {
        CodeRange range = MODULE_RANGES.get(module);
        if (range == null) {
            result.addRangeError(module, "未定义的模块: " + module);
            return;
        }
        
        for (IResultCode code : codes) {
            if (!range.isInRange(code.getCode())) {
                result.addRangeError(module, 
                    String.format("异常码 %d 超出模块 %s 的范围 %s", 
                        code.getCode(), module, range.toString()));
            }
        }
    }

    /**
     * 验证模块内重复
     */
    private void validateModuleDuplicates(List<IResultCode> codes, ValidationResult result) {
        Set<Integer> seenCodes = new HashSet<>();
        Set<String> seenMessageKeys = new HashSet<>();
        
        for (IResultCode code : codes) {
            // 检查异常码重复
            if (!seenCodes.add(code.getCode())) {
                result.addDuplicateError(code, null);
            }
            
            // 检查消息键重复
            if (!seenMessageKeys.add(code.getMessageKey())) {
                result.addMessageKeyError(code, "消息键重复: " + code.getMessageKey());
            }
        }
    }

    /**
     * 验证消息键重复
     */
    private void validateMessageKeys(Collection<IResultCode> codes, ValidationResult result) {
        Map<String, List<IResultCode>> messageKeyMap = new HashMap<>();
        
        for (IResultCode code : codes) {
            messageKeyMap.computeIfAbsent(code.getMessageKey(), k -> new ArrayList<>()).add(code);
        }
        
        for (Map.Entry<String, List<IResultCode>> entry : messageKeyMap.entrySet()) {
            if (entry.getValue().size() > 1) {
                result.addMessageKeyError(entry.getValue().get(0), 
                    "消息键重复: " + entry.getKey() + " (共" + entry.getValue().size() + "个)");
            }
        }
    }

    /**
     * 查找所有实现了IResultCode的枚举类
     */
    @SuppressWarnings("unchecked")
    public Set<Class<? extends IResultCode>> findResultCodeEnums() {
        Set<Class<? extends IResultCode>> enumClasses = new HashSet<>();
        
        // 手动添加已知的枚举类
        enumClasses.add(CommonResultCode.class);
        enumClasses.add(AuthResultCode.class);
        enumClasses.add(UserResultCode.class);
        enumClasses.add(ServiceResultCode.class);
        
        // 可以通过反射扫描包来动态发现，但为了性能考虑，这里手动添加
        return enumClasses;
    }

    /**
     * 获取枚举值
     */
    @SuppressWarnings("unchecked")
    private IResultCode[] getEnumValues(Class<? extends IResultCode> enumClass) {
        try {
            Field valuesField = enumClass.getDeclaredField("$VALUES");
            valuesField.setAccessible(true);
            return (IResultCode[]) valuesField.get(null);
        } catch (Exception e) {
            // 如果反射失败，使用values()方法
            try {
                return (IResultCode[]) enumClass.getMethod("values").invoke(null);
            } catch (Exception ex) {
                throw new RuntimeException("无法获取枚举值: " + enumClass.getName(), ex);
            }
        }
    }

    /**
     * 异常码范围定义
     */
    public static class CodeRange {
        private final List<Range> ranges = new ArrayList<>();
        
        public CodeRange(int... ranges) {
            if (ranges.length % 2 != 0) {
                throw new IllegalArgumentException("范围参数必须是偶数个");
            }
            for (int i = 0; i < ranges.length; i += 2) {
                this.ranges.add(new Range(ranges[i], ranges[i + 1]));
            }
        }
        
        public boolean isInRange(int code) {
            return ranges.stream().anyMatch(range -> range.isInRange(code));
        }
        
        @Override
        public String toString() {
            return ranges.stream()
                .map(Range::toString)
                .reduce((a, b) -> a + ", " + b)
                .orElse("无范围");
        }
        
        private static class Range {
            private final int start;
            private final int end;
            
            public Range(int start, int end) {
                this.start = start;
                this.end = end;
            }
            
            public boolean isInRange(int code) {
                return code >= start && code <= end;
            }
            
            @Override
            public String toString() {
                return start + "-" + end;
            }
        }
    }

    /**
     * 验证结果
     */
    public static class ValidationResult {
        private final List<String> errors = new ArrayList<>();
        private final List<String> warnings = new ArrayList<>();
        private final Map<Integer, List<IResultCode>> duplicates = new HashMap<>();
        private final Map<String, List<String>> rangeErrors = new HashMap<>();
        private final Map<String, List<String>> messageKeyErrors = new HashMap<>();
        
        public void addDuplicateError(IResultCode code, IResultCode existing) {
            if (existing != null) {
                errors.add(String.format("异常码重复: %d 在 %s.%s 和 %s.%s 中", 
                    code.getCode(), 
                    code.getModuleName(), code.getClass().getSimpleName(),
                    existing.getModuleName(), existing.getClass().getSimpleName()));
            } else {
                errors.add(String.format("异常码重复: %d 在 %s.%s 中", 
                    code.getCode(), code.getModuleName(), code.getClass().getSimpleName()));
            }
            
            duplicates.computeIfAbsent(code.getCode(), k -> new ArrayList<>()).add(code);
            if (existing != null) {
                duplicates.get(code.getCode()).add(existing);
            }
        }
        
        public void addRangeError(String module, String error) {
            rangeErrors.computeIfAbsent(module, k -> new ArrayList<>()).add(error);
            errors.add("[" + module + "] " + error);
        }
        
        public void addMessageKeyError(IResultCode code, String error) {
            messageKeyErrors.computeIfAbsent(code.getMessageKey(), k -> new ArrayList<>()).add(error);
            errors.add("[" + code.getMessageKey() + "] " + error);
        }
        
        public void addSystemError(String error) {
            errors.add("[系统] " + error);
        }
        
        public void addWarning(String warning) {
            warnings.add(warning);
        }
        
        public boolean isValid() {
            return errors.isEmpty();
        }
        
        public List<String> getErrors() {
            return errors;
        }
        
        public List<String> getWarnings() {
            return warnings;
        }
        
        public Map<Integer, List<IResultCode>> getDuplicates() {
            return duplicates;
        }
        
        public Map<String, List<String>> getRangeErrors() {
            return rangeErrors;
        }
        
        public Map<String, List<String>> getMessageKeyErrors() {
            return messageKeyErrors;
        }
        
        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("验证结果:\n");
            
            if (errors.isEmpty() && warnings.isEmpty()) {
                sb.append("✓ 所有异常码验证通过\n");
            } else {
                if (!errors.isEmpty()) {
                    sb.append("❌ 错误:\n");
                    for (String error : errors) {
                        sb.append("  - ").append(error).append("\n");
                    }
                }
                
                if (!warnings.isEmpty()) {
                    sb.append("⚠️  警告:\n");
                    for (String warning : warnings) {
                        sb.append("  - ").append(warning).append("\n");
                    }
                }
            }
            
            return sb.toString();
        }
    }
} 