# 雪花算法ID生成器使用指南

## 📖 概述

雪花算法（Snowflake）是一种分布式系统中生成唯一ID的算法，由Twitter开源。本项目提供了完整的雪花算法实现，包括自动配置、工具类、测试接口等。

## 🏗️ 算法结构

雪花算法生成的是64位的long型数字，结构如下：

```
0 - 0000000000 0000000000 0000000000 0000000000 0 - 00000 - 00000 - 000000000000
|   |                                             |   |     |     |
|   |<-- 41位时间戳(毫秒级)                        |   |     |     |<--12位序列号
|   |                                             |   |     |
|   |                                             |   |<--5位机器ID
|   |                                             |
|   |                                             |<--5位数据中心ID
|   |
|<--1位符号位，始终为0
```

### 各部分说明

- **1位符号位**: 始终为0，保证生成的ID为正数
- **41位时间戳**: 毫秒级时间戳，从2024-01-01 00:00:00开始计算，可使用约69年
- **5位数据中心ID**: 支持32个数据中心
- **5位机器ID**: 每个数据中心支持32台机器
- **12位序列号**: 每毫秒内支持4096个序列号

## 🚀 快速开始

### 1. 基础使用

```java
@Autowired
private IdGeneratorUtil idGeneratorUtil;

// 生成基础ID
long id = idGeneratorUtil.nextId();
String idStr = idGeneratorUtil.nextIdStr();

// 生成业务ID
long userId = idGeneratorUtil.nextUserId();
String orderId = idGeneratorUtil.nextOrderId();
String sessionId = idGeneratorUtil.nextSessionId();
```

### 2. 配置文件

```yaml
accompany:
  snowflake:
    # 工作机器ID (0-31)，如果不配置则自动生成
    worker-id: 1
    # 数据中心ID (0-31)，如果不配置则自动生成
    datacenter-id: 1
    # 是否自动生成ID（当上面的ID未配置时）
    auto-generate: true
```

### 3. 在实体类中使用

```java
@Entity
public class User {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;  // MyBatis-Plus会自动使用雪花算法生成ID
    
    // ... 其他字段
}
```

## 📦 核心组件

### 1. SnowflakeIdGenerator - 核心生成器

```java
public class SnowflakeIdGenerator {
    public synchronized long nextId();                    // 生成下一个ID
    public static SnowflakeIdInfo parseId(long id);      // 解析ID信息
}
```

### 2. IdGeneratorUtil - 工具类

```java
@Component
public class IdGeneratorUtil {
    public long nextId();                                 // 生成基础ID
    public String nextIdStr();                           // 生成ID字符串
    public long nextShortId();                           // 生成短ID
    public String nextIdWithPrefix(String prefix);       // 生成带前缀ID
    
    // 业务ID生成
    public long nextUserId();                            // 用户ID
    public String nextOrderId();                         // 订单ID
    public String nextSessionId();                       // 会话ID
    public String nextDeviceId();                        // 设备ID
    
    // 解析功能
    public SnowflakeIdInfo parseId(long id);            // 解析ID
    public Date getIdGenerateTime(long id);             // 获取ID生成时间
    public boolean isSnowflakeId(long id);              // 检查是否为雪花算法ID
    
    // 批量生成
    public long[] nextIds(int count);                   // 批量生成ID
    
    // 特殊ID
    public String generateInviteCode();                 // 生成邀请码
    public String generateRandomNumericId(int length);  // 生成随机数字ID
}
```

### 3. SnowflakeIdConfig - 自动配置

自动根据机器信息生成workerId和datacenterId，支持：
- 基于IP地址自动生成
- 基于MAC地址自动生成
- 配置文件手动指定
- 随机生成（兜底方案）

## 🔧 API接口

### 测试接口

项目提供了完整的测试接口，用于验证雪花算法的功能：

- `GET /api/id/generate` - 生成单个ID
- `GET /api/id/generate/batch?count=10` - 批量生成ID
- `GET /api/id/generate/types` - 生成各种类型ID
- `GET /api/id/parse/{id}` - 解析ID信息
- `POST /api/id/validate` - 验证ID格式
- `GET /api/id/performance?count=10000` - 性能测试
- `GET /api/id/info` - 获取系统信息

### 响应示例

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1735123456789012345,
    "idStr": "1735123456789012345",
    "timestamp": 1735123456789,
    "generateTime": "2024-12-25T10:30:56.789",
    "datacenterId": 1,
    "workerId": 1,
    "sequence": 0
  },
  "success": true,
  "timestamp": 1735123456789
}
```

## 🎯 使用场景

### 1. 数据库主键

```java
@Entity
public class Order {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;  // 自动使用雪花算法生成
}
```

### 2. 业务编号生成

```java
// 订单号: ORDER1735123456789012345
String orderNo = idGeneratorUtil.nextOrderId();

// 会话ID: SESSION1735123456789012345
String sessionId = idGeneratorUtil.nextSessionId();

// 设备ID: DEVICE1735123456789012345
String deviceId = idGeneratorUtil.nextDeviceId();
```

### 3. 分布式锁

```java
// 使用ID作为分布式锁的key
String lockKey = "lock:" + idGeneratorUtil.nextId();
```

### 4. 消息队列

```java
// 消息ID
String messageId = idGeneratorUtil.nextIdStr();
```

## 📊 性能特性

### 1. 生成性能

- **单线程**: 约100万次/秒
- **多线程**: 支持高并发，性能随核心数增加
- **内存占用**: 极小，无额外存储开销

### 2. 唯一性保证

- **时间维度**: 41位时间戳，精确到毫秒
- **机器维度**: 10位机器标识（数据中心+机器ID）
- **序列维度**: 12位序列号，每毫秒4096个

### 3. 有序性

- **趋势递增**: 基于时间戳，整体趋势递增
- **局部有序**: 同一毫秒内的ID有序

## ⚠️ 注意事项

### 1. 时钟回拨

系统内置了时钟回拨检测和处理：
- 小于5ms的回拨：等待并重试
- 大于5ms的回拨：抛出异常

### 2. 机器标识

确保每台机器的workerId和datacenterId组合唯一：
- 手动配置：在配置文件中指定
- 自动生成：基于IP/MAC地址自动计算

### 3. 数据库字段类型

使用`BIGINT`类型存储：
```sql
CREATE TABLE example (
    id BIGINT PRIMARY KEY COMMENT 'ID'
);
```

## 🧪 测试页面

访问测试页面进行功能验证：
- 用户系统测试: http://localhost:8081/user/
- 雪花算法测试: http://localhost:8081/user/snowflake-test.html

测试页面功能：
- ✅ 单个ID生成
- ✅ 批量ID生成
- ✅ ID解析和验证
- ✅ 性能测试
- ✅ 实时生成监控

## 🔍 故障排除

### 1. ID重复

**原因**: 机器标识重复
**解决**: 确保每台机器的workerId+datacenterId唯一

### 2. 时钟回拨异常

**原因**: 系统时钟被调整
**解决**: 
- 检查系统时间同步
- 使用NTP服务保证时间准确性

### 3. 性能问题

**原因**: 高并发下的锁竞争
**解决**: 
- 考虑使用多个生成器实例
- 调整序列号位数

## 💡 最佳实践

### 1. 配置建议

```yaml
# 生产环境建议手动配置
accompany:
  snowflake:
    worker-id: 1      # 根据机器编号设置
    datacenter-id: 1  # 根据数据中心编号设置
    auto-generate: false
```

### 2. 代码使用

```java
// ✅ 推荐：使用工具类
@Autowired
private IdGeneratorUtil idGeneratorUtil;

// ❌ 不推荐：直接使用生成器
@Autowired
private SnowflakeIdGenerator generator;
```

### 3. 错误处理

```java
try {
    long id = idGeneratorUtil.nextId();
    // 业务逻辑
} catch (RuntimeException e) {
    // 处理时钟回拨等异常
    log.error("ID生成失败", e);
    // 降级处理，如使用UUID
}
```

## 📈 扩展功能

### 1. 自定义前缀

```java
// 订单号前缀
String orderNo = idGeneratorUtil.nextIdWithPrefix("ORD");
// 结果: ORD1735123456789012345
```

### 2. 短ID生成

```java
// 18位短ID
long shortId = idGeneratorUtil.nextShortId();
```

### 3. 邀请码生成

```java
// 6位字母数字组合
String inviteCode = idGeneratorUtil.generateInviteCode();
// 结果: A1B2C3
```

## 📞 技术支持

如有问题，请通过以下方式联系：
- 项目Issues: https://github.com/accompany/accompany-backend/issues
- 邮箱: <EMAIL>

---

**💡 提示**: 雪花算法适合大多数分布式系统场景，但在极端高并发或对ID长度有严格要求的场景下，可考虑其他方案如短ID生成器或分段式ID生成。 