# 数据库设置说明

## 🎯 编译错误已解决！

✅ **编译成功** - 所有编译错误已修复
✅ **后端认证系统完整** - 登录注册功能完整实现
✅ **前端API集成** - Vue页面已对接后端接口

## 📋 下一步：数据库配置

### 1. **启动MySQL服务**
```bash
# macOS 使用 Homebrew
brew services start mysql

# 或者手动启动
mysql.server start
```

### 2. **执行SQL脚本**
```bash
# 连接MySQL
mysql -u root -p

# 执行我们的建表脚本
source /Users/<USER>/Documents/Develop/IdeaProject/Accompany/accompany-backend/accompany-user/src/main/resources/sql/create_users_table.sql
```

### 3. **验证表创建**
```sql
USE accompany_db;
SHOW TABLES;
DESCRIBE users;
DESCRIBE sms_verification_codes;
```

### 4. **启动应用**
```bash
cd /Users/<USER>/Documents/Develop/IdeaProject/Accompany/accompany-backend/accompany-user
java -jar target/accompany-user-1.0.0.jar --spring.profiles.active=auth
```

## 🔧 数据库配置说明

**当前配置 (application-auth.yml):**
- 数据库：`accompany_db`
- 用户名：`root`
- 密码：`123456`
- 端口：`3306`

如果您的MySQL配置不同，请修改 `src/main/resources/application-auth.yml` 中的数据库连接信息。

## 🚀 测试API接口

应用启动后，您可以测试以下接口：

```bash
# 检查用户名是否可用
curl "http://localhost:8080/api/auth/check/username?username=test"

# 检查手机号是否可用
curl "http://localhost:8080/api/auth/check/phone?phone=13800138000"

# 发送短信验证码
curl -X POST "http://localhost:8080/api/auth/sms/send" \
  -H "Content-Type: application/json" \
  -d '{"phone":"13800138000","type":"register"}'
```

## 📱 前端测试

启动前端项目：
```bash
cd /Users/<USER>/Documents/Develop/IdeaProject/Accompany/accompany-frontend/accompany-web
pnpm run dev
```

访问：http://localhost:3000/register 或 http://localhost:3000/login

## ✨ 完成的功能

- ✅ 用户注册（含短信验证码）
- ✅ 用户登录（密码登录）
- ✅ JWT Token认证
- ✅ 用户名/手机号/邮箱可用性检查
- ✅ 密码加密（SHA-256 + 盐值）
- ✅ 雪花算法ID生成
- ✅ 全局异常处理
- ✅ CORS跨域支持
- ✅ 前端Vue3 + Element Plus界面 