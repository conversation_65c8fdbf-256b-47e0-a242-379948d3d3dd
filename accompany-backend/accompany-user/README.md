# Accompany 用户模块

## 项目概述

Accompany用户模块是一个完整的用户认证和管理系统，提供用户注册、登录、认证、会话管理等核心功能。

## 功能特性

### 🔐 核心认证功能
- ✅ 用户注册（用户名+密码+手机号验证）
- ✅ 多种登录方式（密码登录、短信验证码登录）
- ✅ JWT令牌认证（AccessToken + RefreshToken）
- ✅ 会话管理和设备管理
- ✅ 安全的密码加密存储

### 📱 短信验证
- ✅ 短信验证码发送
- ✅ 验证码验证和过期检查
- ✅ 发送频率限制
- ✅ 多种验证码类型（注册、登录、重置密码等）

### 🛡️ 安全控制
- ✅ 黑名单管理
- ✅ IP地址追踪
- ✅ 设备管理和信任设备
- ✅ API调用日志记录
- ✅ 登录日志记录

### 📊 数据管理
- ✅ 用户基础信息管理
- ✅ 实名认证信息
- ✅ 第三方登录支持
- ✅ 用户状态管理

## 技术架构

### 技术栈
- **框架**: Spring Boot 2.7+
- **数据库**: MySQL 8.0+
- **缓存**: Redis 6.0+
- **ORM**: MyBatis-Plus 3.5+
- **认证**: JWT
- **构建**: Maven 3.6+

### 架构设计
```
┌─────────────────┐
│   Controller    │ ← REST API接口层
├─────────────────┤
│    Service      │ ← 业务逻辑层  
├─────────────────┤
│   Repository    │ ← 数据访问层
├─────────────────┤
│    Entity       │ ← 实体模型层
└─────────────────┘
```

## 数据库设计

### 核心表结构
| 表名 | 说明 | 主要字段 |
|------|------|----------|
| `users` | 用户基础表 | id, username, phone, email, password_hash |
| `user_verifications` | 用户认证信息表 | user_id, verification_type, real_name, id_card |
| `social_logins` | 第三方登录表 | user_id, provider, open_id, access_token |
| `sms_verification_codes` | 短信验证码表 | phone, code, code_type, expires_time |
| `blacklists` | 拉黑名单表 | target_type, target_value, reason |
| `user_devices` | 用户设备管理表 | user_id, device_id, device_type, is_trusted |
| `api_logs` | API调用日志表 | user_id, api_path, ip_address, execution_time |
| `user_login_logs` | 用户登录日志表 | user_id, login_type, login_status, ip_address |
| `user_sessions` | 用户会话表 | user_id, session_id, access_token, refresh_token |

## API接口

### 认证相关接口

#### 1. 发送短信验证码
```http
POST /api/auth/sms/send
Content-Type: application/x-www-form-urlencoded

phone=13800138000&codeType=1
```

#### 2. 用户注册
```http
POST /api/auth/register
Content-Type: application/json

{
  "username": "testuser",
  "password": "Test123456",
  "confirmPassword": "Test123456",
  "phone": "13800138000",
  "email": "<EMAIL>",
  "verificationCode": "123456",
  "nickname": "测试用户",
  "gender": 1,
  "userType": 1,
  "agreeTerms": true,
  "deviceId": "web-1234567890",
  "deviceType": 3,
  "deviceName": "Web Browser"
}
```

#### 3. 用户登录
```http
POST /api/auth/login
Content-Type: application/json

{
  "loginType": 1,
  "username": "testuser",
  "password": "Test123456",
  "rememberMe": false,
  "deviceId": "web-1234567890",
  "deviceType": 3,
  "deviceName": "Web Browser"
}
```

#### 4. 用户登出
```http
POST /api/auth/logout
Authorization: Bearer <access_token>
```

#### 5. 刷新令牌
```http
POST /api/auth/refresh
Content-Type: application/x-www-form-urlencoded

refreshToken=<refresh_token>
```

### 检查接口

#### 1. 检查用户名可用性
```http
GET /api/auth/check-username?username=testuser
```

#### 2. 检查手机号可用性
```http
GET /api/auth/check-phone?phone=13800138000
```

#### 3. 检查邮箱可用性
```http
GET /api/auth/check-email?email=<EMAIL>
```

## 快速开始

### 1. 环境准备
```bash
# 安装MySQL 8.0+
# 安装Redis 6.0+
# 安装JDK 17+
# 安装Maven 3.6+
```

### 2. 数据库初始化
```sql
-- 创建数据库
CREATE DATABASE accompany DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 执行表结构脚本
source accompany-user/src/main/resources/sql/user_tables.sql
```

### 3. 配置文件
```yaml
# application.yml
spring:
  datasource:
    url: ****************************************************************************
    username: root
    password: your_password
  
  redis:
    host: localhost
    port: 6379
    password: your_redis_password
```

### 4. 启动应用
```bash
cd accompany-backend/accompany-user
mvn spring-boot:run
```

### 5. 访问测试页面
打开浏览器访问：http://localhost:8081/user/

## 测试指南

### 页面测试
1. **访问测试页面**: http://localhost:8081/user/
2. **注册新用户**: 
   - 填写用户名、密码、手机号等信息
   - 点击"发送验证码"（测试环境验证码为：123456）
   - 同意用户协议并提交注册
3. **用户登录**:
   - 选择密码登录或短信验证码登录
   - 输入用户名和密码/验证码
   - 点击登录
4. **可用性检查**:
   - 检查用户名、手机号、邮箱是否已被使用

### API测试
使用Postman或其他API测试工具测试各个接口功能。

## 配置说明

### 业务配置
```yaml
accompany:
  user:
    # 密码配置
    password:
      salt-length: 32              # 盐值长度
      hash-algorithm: SHA-256      # 哈希算法
    
    # 短信配置
    sms:
      expire-minutes: 5            # 验证码过期时间（分钟）
      send-limit-per-hour: 10      # 每小时发送限制
      code-length: 6               # 验证码长度
    
    # JWT配置
    jwt:
      secret: your-secret-key      # JWT密钥
      access-token-expire-hours: 24   # AccessToken过期时间（小时）
      refresh-token-expire-days: 30   # RefreshToken过期时间（天）
    
    # 设备管理
    device:
      max-devices-per-user: 5      # 每用户最大设备数
      trust-device-days: 30        # 信任设备天数
```

## 安全特性

### 密码安全
- 使用SHA-256算法加密
- 每个密码使用唯一的盐值
- 密码强度验证（至少8位，包含大小写字母和数字）

### 接口安全
- JWT令牌认证
- IP地址记录和黑名单
- 请求频率限制
- 设备管理和信任设备

### 数据安全
- 敏感信息加密存储
- 完整的操作日志记录
- 软删除机制
- 数据备份和恢复

## 监控和日志

### 日志类型
- **API调用日志**: 记录所有API请求和响应
- **登录日志**: 记录用户登录行为
- **操作日志**: 记录关键业务操作
- **错误日志**: 记录系统异常和错误

### 监控指标
- 注册成功率
- 登录成功率
- API响应时间
- 错误率统计

## 扩展功能

### 已支持
- [x] 基础用户注册登录
- [x] 短信验证码
- [x] JWT认证
- [x] 设备管理
- [x] 黑名单管理

### 规划中
- [ ] 第三方登录（微信、QQ、Apple）
- [ ] 实名认证流程
- [ ] 多因子认证（MFA）
- [ ] 用户行为分析
- [ ] 风险控制系统

## 故障排除

### 常见问题

#### 1. 数据库连接失败
```
检查数据库配置是否正确
确认数据库服务是否启动
验证用户名密码是否正确
```

#### 2. Redis连接失败
```
检查Redis配置是否正确
确认Redis服务是否启动
验证密码是否正确
```

#### 3. JWT令牌验证失败
```
检查JWT密钥配置
确认令牌是否过期
验证令牌格式是否正确
```

### 日志查看
```bash
# 查看应用日志
tail -f logs/accompany-user.log

# 查看错误日志
grep ERROR logs/accompany-user.log
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目使用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目地址: [https://github.com/accompany/accompany-backend](https://github.com/accompany/accompany-backend)
- 问题反馈: [Issues](https://github.com/accompany/accompany-backend/issues)
- 邮箱: <EMAIL>

---

**🌟 如果这个项目对您有帮助，请给我们一个Star！** 