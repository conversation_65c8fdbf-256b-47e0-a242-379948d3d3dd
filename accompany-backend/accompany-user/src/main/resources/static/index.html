<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accompany 用户系统测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 800px;
            max-width: 90%;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .tabs {
            display: flex;
            background: #f8f9fa;
        }

        .tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            border: none;
            background: transparent;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .tab.active {
            background: white;
            color: #667eea;
            font-weight: bold;
        }

        .tab:hover {
            background: #e9ecef;
        }

        .form-container {
            padding: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-row {
            display: flex;
            gap: 15px;
        }

        .form-row .form-group {
            flex: 1;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }

        input, select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        input:focus, select:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
            margin-top: 10px;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-sms {
            width: auto;
            padding: 8px 15px;
            font-size: 14px;
            margin-left: 10px;
        }

        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }

        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .sms-input {
            display: flex;
            align-items: center;
        }

        .sms-input input {
            flex: 1;
        }

        .hidden {
            display: none !important;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌟 Accompany</h1>
            <p>用户系统测试平台</p>
        </div>

        <div class="tabs">
            <button class="tab active" onclick="showTab('register')">用户注册</button>
            <button class="tab" onclick="showTab('login')">用户登录</button>
            <button class="tab" onclick="showTab('check')">可用性检查</button>
        </div>

        <!-- 注册表单 -->
        <div id="register-form" class="form-container">
            <form onsubmit="register(event)">
                <div class="form-row">
                    <div class="form-group">
                        <label for="reg-username">用户名</label>
                        <input type="text" id="reg-username" name="username" placeholder="4-20位字母数字下划线" required>
                    </div>
                    <div class="form-group">
                        <label for="reg-nickname">昵称</label>
                        <input type="text" id="reg-nickname" name="nickname" placeholder="显示名称">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="reg-password">密码</label>
                        <input type="password" id="reg-password" name="password" placeholder="8-20位包含大小写字母和数字" required>
                    </div>
                    <div class="form-group">
                        <label for="reg-confirm-password">确认密码</label>
                        <input type="password" id="reg-confirm-password" name="confirmPassword" placeholder="再次输入密码" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="reg-phone">手机号</label>
                        <input type="tel" id="reg-phone" name="phone" placeholder="11位手机号码" required>
                    </div>
                    <div class="form-group">
                        <label for="reg-email">邮箱</label>
                        <input type="email" id="reg-email" name="email" placeholder="邮箱地址(可选)">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="reg-gender">性别</label>
                        <select id="reg-gender" name="gender">
                            <option value="0">未知</option>
                            <option value="1">男</option>
                            <option value="2">女</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="reg-user-type">用户类型</label>
                        <select id="reg-user-type" name="userType">
                            <option value="1">普通用户</option>
                            <option value="2">服务提供者</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="reg-verification-code">短信验证码</label>
                    <div class="sms-input">
                        <input type="text" id="reg-verification-code" name="verificationCode" placeholder="6位数字验证码" required>
                        <button type="button" class="btn btn-secondary btn-sms" onclick="sendSms('register')">发送验证码</button>
                    </div>
                </div>

                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="reg-agree-terms" name="agreeTerms" required>
                        <label for="reg-agree-terms">我已阅读并同意《用户协议》和《隐私政策》</label>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary">立即注册</button>
            </form>
            <div id="register-result" class="result"></div>
        </div>

        <!-- 登录表单 -->
        <div id="login-form" class="form-container hidden">
            <form onsubmit="login(event)">
                <div class="form-group">
                    <label for="login-type">登录方式</label>
                    <select id="login-type" name="loginType" onchange="toggleLoginType()">
                        <option value="1">密码登录</option>
                        <option value="2">短信验证码登录</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="login-username">用户名/手机号/邮箱</label>
                    <input type="text" id="login-username" name="username" placeholder="请输入用户名、手机号或邮箱" required>
                </div>

                <div class="form-group" id="password-group">
                    <label for="login-password">密码</label>
                    <input type="password" id="login-password" name="password" placeholder="请输入密码">
                </div>

                <div class="form-group hidden" id="sms-group">
                    <label for="login-verification-code">短信验证码</label>
                    <div class="sms-input">
                        <input type="text" id="login-verification-code" name="verificationCode" placeholder="6位数字验证码">
                        <button type="button" class="btn btn-secondary btn-sms" onclick="sendSms('login')">发送验证码</button>
                    </div>
                </div>

                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="remember-me" name="rememberMe">
                        <label for="remember-me">记住登录状态</label>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary">立即登录</button>
            </form>
            <div id="login-result" class="result"></div>
        </div>

        <!-- 可用性检查表单 -->
        <div id="check-form" class="form-container hidden">
            <div class="form-group">
                <label for="check-username">检查用户名</label>
                <div class="sms-input">
                    <input type="text" id="check-username" placeholder="请输入用户名">
                    <button type="button" class="btn btn-secondary btn-sms" onclick="checkUsername()">检查</button>
                </div>
            </div>

            <div class="form-group">
                <label for="check-phone">检查手机号</label>
                <div class="sms-input">
                    <input type="tel" id="check-phone" placeholder="请输入手机号">
                    <button type="button" class="btn btn-secondary btn-sms" onclick="checkPhone()">检查</button>
                </div>
            </div>

            <div class="form-group">
                <label for="check-email">检查邮箱</label>
                <div class="sms-input">
                    <input type="email" id="check-email" placeholder="请输入邮箱">
                    <button type="button" class="btn btn-secondary btn-sms" onclick="checkEmail()">检查</button>
                </div>
            </div>

            <div id="check-result" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = '/user/api/auth';

        // 切换标签页
        function showTab(tabName) {
            // 隐藏所有表单
            document.querySelectorAll('.form-container').forEach(form => {
                form.classList.add('hidden');
            });
            
            // 移除所有标签的active类
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示目标表单
            document.getElementById(tabName + '-form').classList.remove('hidden');
            
            // 激活对应标签
            event.target.classList.add('active');
        }

        // 切换登录方式
        function toggleLoginType() {
            const loginType = document.getElementById('login-type').value;
            const passwordGroup = document.getElementById('password-group');
            const smsGroup = document.getElementById('sms-group');
            
            if (loginType === '1') {
                passwordGroup.classList.remove('hidden');
                smsGroup.classList.add('hidden');
                document.getElementById('login-password').required = true;
                document.getElementById('login-verification-code').required = false;
            } else {
                passwordGroup.classList.add('hidden');
                smsGroup.classList.remove('hidden');
                document.getElementById('login-password').required = false;
                document.getElementById('login-verification-code').required = true;
            }
        }

        // 显示结果
        function showResult(elementId, message, isSuccess) {
            const resultElement = document.getElementById(elementId);
            resultElement.textContent = message;
            resultElement.className = 'result ' + (isSuccess ? 'success' : 'error');
            resultElement.style.display = 'block';
        }

        // 发送短信验证码
        async function sendSms(type) {
            let phone;
            if (type === 'register') {
                phone = document.getElementById('reg-phone').value;
                codeType = 1;
            } else {
                phone = document.getElementById('login-username').value;
                codeType = 2;
            }

            if (!phone) {
                alert('请先输入手机号');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/sms/send?phone=${phone}&codeType=${codeType}`, {
                    method: 'POST'
                });
                const result = await response.json();
                
                if (result.success) {
                    alert('验证码发送成功');
                } else {
                    alert('发送失败: ' + result.message);
                }
            } catch (error) {
                alert('发送失败: ' + error.message);
            }
        }

        // 用户注册
        async function register(event) {
            event.preventDefault();
            
            const formData = new FormData(event.target);
            const data = {
                username: formData.get('username'),
                password: formData.get('password'),
                confirmPassword: formData.get('confirmPassword'),
                phone: formData.get('phone'),
                email: formData.get('email') || null,
                verificationCode: formData.get('verificationCode'),
                nickname: formData.get('nickname') || null,
                gender: parseInt(formData.get('gender')),
                userType: parseInt(formData.get('userType')),
                agreeTerms: formData.has('agreeTerms'),
                deviceId: 'web-' + Date.now(),
                deviceType: 3,
                deviceName: 'Web Browser'
            };

            try {
                const response = await fetch(`${API_BASE}/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showResult('register-result', '注册成功! 自动登录中...', true);
                    // 保存登录信息
                    localStorage.setItem('accessToken', result.data.accessToken);
                    localStorage.setItem('refreshToken', result.data.refreshToken);
                    localStorage.setItem('userInfo', JSON.stringify(result.data));
                } else {
                    showResult('register-result', '注册失败: ' + result.message, false);
                }
            } catch (error) {
                showResult('register-result', '注册失败: ' + error.message, false);
            }
        }

        // 用户登录
        async function login(event) {
            event.preventDefault();
            
            const formData = new FormData(event.target);
            const loginType = parseInt(formData.get('loginType'));
            
            const data = {
                loginType: loginType,
                username: formData.get('username'),
                password: loginType === 1 ? formData.get('password') : null,
                verificationCode: loginType === 2 ? formData.get('verificationCode') : null,
                rememberMe: formData.has('rememberMe'),
                deviceId: 'web-' + Date.now(),
                deviceType: 3,
                deviceName: 'Web Browser'
            };

            try {
                const response = await fetch(`${API_BASE}/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showResult('login-result', '登录成功!', true);
                    // 保存登录信息
                    localStorage.setItem('accessToken', result.data.accessToken);
                    localStorage.setItem('refreshToken', result.data.refreshToken);
                    localStorage.setItem('userInfo', JSON.stringify(result.data));
                } else {
                    showResult('login-result', '登录失败: ' + result.message, false);
                }
            } catch (error) {
                showResult('login-result', '登录失败: ' + error.message, false);
            }
        }

        // 检查用户名
        async function checkUsername() {
            const username = document.getElementById('check-username').value;
            if (!username) {
                alert('请输入用户名');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/check-username?username=${username}`);
                const result = await response.json();
                
                if (result.success) {
                    showResult('check-result', 
                        `用户名 "${username}" ${result.data ? '可用' : '已被占用'}`, 
                        result.data);
                } else {
                    showResult('check-result', '检查失败: ' + result.message, false);
                }
            } catch (error) {
                showResult('check-result', '检查失败: ' + error.message, false);
            }
        }

        // 检查手机号
        async function checkPhone() {
            const phone = document.getElementById('check-phone').value;
            if (!phone) {
                alert('请输入手机号');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/check-phone?phone=${phone}`);
                const result = await response.json();
                
                if (result.success) {
                    showResult('check-result', 
                        `手机号 "${phone}" ${result.data ? '可用' : '已被注册'}`, 
                        result.data);
                } else {
                    showResult('check-result', '检查失败: ' + result.message, false);
                }
            } catch (error) {
                showResult('check-result', '检查失败: ' + error.message, false);
            }
        }

        // 检查邮箱
        async function checkEmail() {
            const email = document.getElementById('check-email').value;
            if (!email) {
                alert('请输入邮箱');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/check-email?email=${email}`);
                const result = await response.json();
                
                if (result.success) {
                    showResult('check-result', 
                        `邮箱 "${email}" ${result.data ? '可用' : '已被注册'}`, 
                        result.data);
                } else {
                    showResult('check-result', '检查失败: ' + result.message, false);
                }
            } catch (error) {
                showResult('check-result', '检查失败: ' + error.message, false);
            }
        }
    </script>
</body>
</html> 