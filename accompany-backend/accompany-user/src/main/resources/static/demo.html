<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accompany 用户系统演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 40px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
            padding: 30px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .section h2 {
            color: #667eea;
            margin-bottom: 20px;
            font-size: 1.8em;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .feature-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }

        .feature-card h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .feature-card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .feature-list {
            list-style: none;
            padding-left: 0;
        }

        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            position: relative;
            padding-left: 25px;
        }

        .feature-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
        }

        .status-completed {
            background: #d4edda;
            color: #155724;
        }

        .status-planned {
            background: #fff3cd;
            color: #856404;
        }

        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }

        .tech-badge {
            background: #667eea;
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.9em;
        }

        .next-steps {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            margin-top: 30px;
        }

        .next-steps h3 {
            margin-bottom: 15px;
            font-size: 1.5em;
        }

        .step-list {
            list-style: none;
            padding-left: 0;
        }

        .step-list li {
            padding: 10px 0;
            position: relative;
            padding-left: 35px;
        }

        .step-list li:before {
            content: counter(step-counter);
            counter-increment: step-counter;
            position: absolute;
            left: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .step-list {
            counter-reset: step-counter;
        }

        .footer {
            text-align: center;
            padding: 30px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Accompany 用户系统</h1>
            <p>基于 Spring Boot 3 + 雪花算法的分布式用户认证系统</p>
        </div>

        <div class="content">
            <!-- 已完成功能 -->
            <div class="section">
                <h2>✅ 已完成功能模块</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>❄️ 雪花算法ID生成器</h3>
                        <span class="status-badge status-completed">已完成</span>
                        <p>高性能分布式唯一ID生成方案，支持自动配置和多种ID类型生成。</p>
                        <ul class="feature-list">
                            <li>64位长整型ID，趋势递增</li>
                            <li>支持32个数据中心，每个支持32台机器</li>
                            <li>每毫秒可生成4096个ID</li>
                            <li>自动机器标识生成</li>
                            <li>时钟回拨检测和处理</li>
                            <li>多种业务ID类型（用户ID、订单ID等）</li>
                        </ul>
                    </div>

                    <div class="feature-card">
                        <h3>📊 异常码管理系统</h3>
                        <span class="status-badge status-completed">已完成</span>
                        <p>模块化异常码管理，支持国际化和重复检测。</p>
                        <ul class="feature-list">
                            <li>模块化异常码设计</li>
                            <li>支持中英文国际化</li>
                            <li>重复码检测机制</li>
                            <li>号段分配管理</li>
                            <li>统一Result响应格式</li>
                        </ul>
                    </div>

                    <div class="feature-card">
                        <h3>🗄️ 数据库表设计</h3>
                        <span class="status-badge status-completed">已完成</span>
                        <p>完整的用户系统数据库表结构设计。</p>
                        <ul class="feature-list">
                            <li>用户基础表（支持多种用户类型）</li>
                            <li>用户认证表（实名认证、职业认证等）</li>
                            <li>第三方登录表（微信、QQ、Apple等）</li>
                            <li>短信验证码表</li>
                            <li>黑名单管理表</li>
                            <li>设备管理表</li>
                            <li>API日志表</li>
                            <li>登录日志表</li>
                            <li>用户会话表</li>
                        </ul>
                    </div>

                    <div class="feature-card">
                        <h3>🏗️ 实体类设计</h3>
                        <span class="status-badge status-completed">已完成</span>
                        <p>基于MyBatis-Plus的实体类设计，集成雪花算法ID生成。</p>
                        <ul class="feature-list">
                            <li>User（用户主表）</li>
                            <li>UserVerification（用户认证）</li>
                            <li>SocialLogin（第三方登录）</li>
                            <li>SmsVerificationCode（短信验证码）</li>
                            <li>Blacklist（黑名单）</li>
                            <li>UserDevice（用户设备）</li>
                            <li>ApiLog（API日志）</li>
                            <li>UserLoginLog（登录日志）</li>
                            <li>UserSession（用户会话）</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 技术架构 -->
            <div class="section">
                <h2>🔧 技术架构</h2>
                <div class="tech-stack">
                    <span class="tech-badge">Spring Boot 3.5.3</span>
                    <span class="tech-badge">Java 17</span>
                    <span class="tech-badge">Maven 3.9.11</span>
                    <span class="tech-badge">MyBatis-Plus</span>
                    <span class="tech-badge">MySQL 8.x</span>
                    <span class="tech-badge">Redis</span>
                    <span class="tech-badge">JWT</span>
                    <span class="tech-badge">Lombok</span>
                    <span class="tech-badge">雪花算法</span>
                    <span class="tech-badge">国际化(i18n)</span>
                </div>
            </div>

            <!-- 下一步开发计划 -->
            <div class="next-steps">
                <h3>🎯 下一步开发计划</h3>
                <ol class="step-list">
                    <li>实现UserService和SmsService业务逻辑</li>
                    <li>创建MyBatis Mapper接口和XML映射文件</li>
                    <li>完善AuthController认证控制器</li>
                    <li>集成Spring Security安全框架</li>
                    <li>实现JWT令牌管理</li>
                    <li>添加SMS短信服务集成</li>
                    <li>实现第三方登录（微信、QQ等）</li>
                    <li>添加Redis缓存支持</li>
                    <li>实现API限流和防护</li>
                    <li>添加监控和日志系统</li>
                </ol>
            </div>

            <!-- 功能亮点 -->
            <div class="section">
                <h2>🌟 系统亮点</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>🚀 高性能</h3>
                        <p>雪花算法单机每秒可生成400万+唯一ID，支持分布式部署。</p>
                    </div>
                    <div class="feature-card">
                        <h3>🛡️ 高可用</h3>
                        <p>分布式架构设计，支持多实例部署，具备故障转移能力。</p>
                    </div>
                    <div class="feature-card">
                        <h3>🔒 安全性</h3>
                        <p>JWT令牌认证、密码加盐哈希、黑名单机制、设备管理。</p>
                    </div>
                    <div class="feature-card">
                        <h3>📱 多端支持</h3>
                        <p>支持Web、H5、Android、iOS多端登录，设备管理完善。</p>
                    </div>
                    <div class="feature-card">
                        <h3>🌍 国际化</h3>
                        <p>支持中英文双语，错误信息国际化，易于扩展其他语言。</p>
                    </div>
                    <div class="feature-card">
                        <h3>📊 可监控</h3>
                        <p>完善的日志记录、API调用追踪、性能监控体系。</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>© 2024 Accompany 团队 | 基于 Spring Boot 3 构建的现代化用户认证系统</p>
        </div>
    </div>
</body>
</html> 