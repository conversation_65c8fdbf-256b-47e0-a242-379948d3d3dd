<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>雪花算法ID生成器测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            text-align: center;
            padding: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .section h2 {
            color: #2a5298;
            margin-bottom: 15px;
            font-size: 1.5em;
        }

        .btn-group {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(30, 60, 114, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .input-group {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            align-items: center;
        }

        .input-group label {
            font-weight: bold;
            min-width: 80px;
        }

        .input-group input {
            flex: 1;
            padding: 8px 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
        }

        .input-group input:focus {
            outline: none;
            border-color: #2a5298;
        }

        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 13px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            background: #2d3748;
            color: #e2e8f0;
            border: 1px solid #4a5568;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            text-align: center;
        }

        .stat-card .value {
            font-size: 1.8em;
            font-weight: bold;
            color: #2a5298;
            margin-bottom: 5px;
        }

        .stat-card .label {
            font-size: 0.9em;
            color: #6c757d;
        }

        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .info-table th,
        .info-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .info-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #2a5298;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #6c757d;
        }

        .loading.show {
            display: block;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>❄️ 雪花算法ID生成器</h1>
            <p>分布式系统唯一ID生成方案测试平台</p>
        </div>

        <div class="content">
            <!-- 基础功能测试 -->
            <div class="section">
                <h2>🎯 基础功能测试</h2>
                <div class="btn-group">
                    <button class="btn btn-primary" onclick="generateSingleId()">生成单个ID</button>
                    <button class="btn btn-primary" onclick="generateBatchIds()">批量生成ID</button>
                    <button class="btn btn-primary" onclick="generateDifferentTypes()">生成各种类型ID</button>
                    <button class="btn btn-secondary" onclick="getSystemInfo()">获取系统信息</button>
                </div>

                <div class="input-group">
                    <label>批量数量:</label>
                    <input type="number" id="batchCount" value="10" min="1" max="1000">
                </div>

                <div id="basicResult" class="result" style="display: none;"></div>
            </div>

            <!-- ID解析测试 -->
            <div class="section">
                <h2>🔍 ID解析测试</h2>
                <div class="input-group">
                    <label>ID值:</label>
                    <input type="text" id="parseId" placeholder="请输入要解析的ID">
                    <button class="btn btn-primary" onclick="parseIdInfo()">解析ID</button>
                </div>

                <div class="input-group">
                    <label>验证ID:</label>
                    <input type="text" id="validateId" placeholder="请输入要验证的ID">
                    <button class="btn btn-primary" onclick="validateIdFormat()">验证ID</button>
                </div>

                <div id="parseResult" class="result" style="display: none;"></div>
            </div>

            <!-- 性能测试 -->
            <div class="section">
                <h2>⚡ 性能测试</h2>
                <div class="btn-group">
                    <button class="btn btn-primary" onclick="performanceTest(1000)">1千次生成</button>
                    <button class="btn btn-primary" onclick="performanceTest(10000)">1万次生成</button>
                    <button class="btn btn-primary" onclick="performanceTest(50000)">5万次生成</button>
                    <button class="btn btn-secondary" onclick="performanceTest(100000)">10万次生成</button>
                </div>

                <div class="input-group">
                    <label>自定义数量:</label>
                    <input type="number" id="customCount" value="10000" min="1" max="100000">
                    <button class="btn btn-primary" onclick="performanceTest()">开始测试</button>
                </div>

                <div id="performanceResult" class="result" style="display: none;"></div>
                <div id="performanceStats" class="stats" style="display: none;"></div>
            </div>

            <!-- 实时生成 -->
            <div class="section">
                <h2>🚀 实时生成监控</h2>
                <div class="btn-group">
                    <button class="btn btn-primary" onclick="startRealTimeGeneration()">开始实时生成</button>
                    <button class="btn btn-secondary" onclick="stopRealTimeGeneration()">停止生成</button>
                    <button class="btn btn-secondary" onclick="clearRealTimeResult()">清空记录</button>
                </div>

                <div class="input-group">
                    <label>生成间隔:</label>
                    <input type="number" id="interval" value="100" min="10" max="5000">
                    <label>毫秒</label>
                </div>

                <div id="realTimeResult" class="result" style="display: none;"></div>
                <div id="realTimeStats" class="stats" style="display: none;"></div>
            </div>
        </div>
    </div>

    <div id="loading" class="loading">
        <p>⏳ 正在处理中，请稍候...</p>
    </div>

    <script>
        const API_BASE = '/user/api/id';
        let realTimeInterval = null;
        let realTimeCount = 0;
        let realTimeIds = [];

        // 显示加载状态
        function showLoading() {
            document.getElementById('loading').classList.add('show');
        }

        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loading').classList.remove('show');
        }

        // 显示结果
        function showResult(elementId, data, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.textContent = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
            element.className = 'result ' + (isSuccess ? 'success' : 'error');
            element.style.display = 'block';
        }

        // 生成单个ID
        async function generateSingleId() {
            showLoading();
            try {
                const response = await fetch(`${API_BASE}/generate`);
                const result = await response.json();
                
                if (result.success) {
                    showResult('basicResult', result.data);
                } else {
                    showResult('basicResult', `错误: ${result.message}`, false);
                }
            } catch (error) {
                showResult('basicResult', `请求失败: ${error.message}`, false);
            }
            hideLoading();
        }

        // 批量生成ID
        async function generateBatchIds() {
            const count = document.getElementById('batchCount').value;
            showLoading();
            try {
                const response = await fetch(`${API_BASE}/generate/batch?count=${count}`);
                const result = await response.json();
                
                if (result.success) {
                    showResult('basicResult', result.data);
                } else {
                    showResult('basicResult', `错误: ${result.message}`, false);
                }
            } catch (error) {
                showResult('basicResult', `请求失败: ${error.message}`, false);
            }
            hideLoading();
        }

        // 生成各种类型ID
        async function generateDifferentTypes() {
            showLoading();
            try {
                const response = await fetch(`${API_BASE}/generate/types`);
                const result = await response.json();
                
                if (result.success) {
                    showResult('basicResult', result.data);
                } else {
                    showResult('basicResult', `错误: ${result.message}`, false);
                }
            } catch (error) {
                showResult('basicResult', `请求失败: ${error.message}`, false);
            }
            hideLoading();
        }

        // 获取系统信息
        async function getSystemInfo() {
            showLoading();
            try {
                const response = await fetch(`${API_BASE}/info`);
                const result = await response.json();
                
                if (result.success) {
                    showResult('basicResult', result.data);
                } else {
                    showResult('basicResult', `错误: ${result.message}`, false);
                }
            } catch (error) {
                showResult('basicResult', `请求失败: ${error.message}`, false);
            }
            hideLoading();
        }

        // 解析ID信息
        async function parseIdInfo() {
            const id = document.getElementById('parseId').value;
            if (!id) {
                showResult('parseResult', '请输入要解析的ID', false);
                return;
            }

            showLoading();
            try {
                const response = await fetch(`${API_BASE}/parse/${id}`);
                const result = await response.json();
                
                if (result.success) {
                    showResult('parseResult', result.data);
                } else {
                    showResult('parseResult', `错误: ${result.message}`, false);
                }
            } catch (error) {
                showResult('parseResult', `请求失败: ${error.message}`, false);
            }
            hideLoading();
        }

        // 验证ID格式
        async function validateIdFormat() {
            const id = document.getElementById('validateId').value;
            if (!id) {
                showResult('parseResult', '请输入要验证的ID', false);
                return;
            }

            showLoading();
            try {
                const response = await fetch(`${API_BASE}/validate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `idStr=${encodeURIComponent(id)}`
                });
                const result = await response.json();
                
                if (result.success) {
                    showResult('parseResult', result.data);
                } else {
                    showResult('parseResult', `错误: ${result.message}`, false);
                }
            } catch (error) {
                showResult('parseResult', `请求失败: ${error.message}`, false);
            }
            hideLoading();
        }

        // 性能测试
        async function performanceTest(count) {
            if (!count) {
                count = document.getElementById('customCount').value;
            }

            showLoading();
            try {
                const response = await fetch(`${API_BASE}/performance?count=${count}`);
                const result = await response.json();
                
                if (result.success) {
                    showResult('performanceResult', result.data);
                    
                    // 显示性能统计图表
                    const data = result.data;
                    const statsHtml = `
                        <div class="stat-card">
                            <div class="value">${data.count}</div>
                            <div class="label">生成数量</div>
                        </div>
                        <div class="stat-card">
                            <div class="value">${data.duration}</div>
                            <div class="label">总耗时</div>
                        </div>
                        <div class="stat-card">
                            <div class="value">${data.avgTime}</div>
                            <div class="label">平均耗时</div>
                        </div>
                        <div class="stat-card">
                            <div class="value">${data.throughput}</div>
                            <div class="label">吞吐量</div>
                        </div>
                        <div class="stat-card">
                            <div class="value">${data.isAllUnique ? '✅' : '❌'}</div>
                            <div class="label">唯一性检查</div>
                        </div>
                        <div class="stat-card">
                            <div class="value">${data.duplicateCount}</div>
                            <div class="label">重复数量</div>
                        </div>
                    `;
                    document.getElementById('performanceStats').innerHTML = statsHtml;
                    document.getElementById('performanceStats').style.display = 'grid';
                } else {
                    showResult('performanceResult', `错误: ${result.message}`, false);
                }
            } catch (error) {
                showResult('performanceResult', `请求失败: ${error.message}`, false);
            }
            hideLoading();
        }

        // 开始实时生成
        function startRealTimeGeneration() {
            if (realTimeInterval) {
                clearInterval(realTimeInterval);
            }

            const interval = parseInt(document.getElementById('interval').value);
            realTimeCount = 0;
            realTimeIds = [];

            document.getElementById('realTimeResult').style.display = 'block';
            document.getElementById('realTimeStats').style.display = 'grid';

            realTimeInterval = setInterval(async () => {
                try {
                    const response = await fetch(`${API_BASE}/generate`);
                    const result = await response.json();
                    
                    if (result.success) {
                        realTimeCount++;
                        realTimeIds.push(result.data.id);
                        
                        const resultText = `[${realTimeCount}] ${new Date().toLocaleTimeString()} - ID: ${result.data.id}\n`;
                        const resultElement = document.getElementById('realTimeResult');
                        resultElement.textContent += resultText;
                        resultElement.scrollTop = resultElement.scrollHeight;
                        
                        // 更新统计信息
                        updateRealTimeStats();
                    }
                } catch (error) {
                    console.error('实时生成失败:', error);
                }
            }, interval);
        }

        // 停止实时生成
        function stopRealTimeGeneration() {
            if (realTimeInterval) {
                clearInterval(realTimeInterval);
                realTimeInterval = null;
            }
        }

        // 清空实时结果
        function clearRealTimeResult() {
            document.getElementById('realTimeResult').textContent = '';
            document.getElementById('realTimeResult').style.display = 'none';
            document.getElementById('realTimeStats').style.display = 'none';
            realTimeCount = 0;
            realTimeIds = [];
        }

        // 更新实时统计
        function updateRealTimeStats() {
            const uniqueCount = new Set(realTimeIds).size;
            const duplicateCount = realTimeIds.length - uniqueCount;
            
            const statsHtml = `
                <div class="stat-card">
                    <div class="value">${realTimeCount}</div>
                    <div class="label">已生成数量</div>
                </div>
                <div class="stat-card">
                    <div class="value">${uniqueCount}</div>
                    <div class="label">唯一数量</div>
                </div>
                <div class="stat-card">
                    <div class="value">${duplicateCount}</div>
                    <div class="label">重复数量</div>
                </div>
                <div class="stat-card">
                    <div class="value">${realTimeIds.length > 0 ? realTimeIds[realTimeIds.length - 1] : 0}</div>
                    <div class="label">最新ID</div>
                </div>
            `;
            document.getElementById('realTimeStats').innerHTML = statsHtml;
        }

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', () => {
            if (realTimeInterval) {
                clearInterval(realTimeInterval);
            }
        });
    </script>
</body>
</html> 