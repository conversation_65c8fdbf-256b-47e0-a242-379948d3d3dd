-- 用户系统数据库表结构（优化版）
-- 作者: Accompany团队
-- 时间: 2024
-- 说明: 结合原始设计和雪花算法ID的优化版本

-- 1. 用户基础表（优化版）
CREATE TABLE users (
    id BIGINT PRIMARY KEY COMMENT '用户ID（雪花算法生成）',
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    phone VARCHAR(20) UNIQUE NOT NULL COMMENT '手机号',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    salt VARCHAR(32) NOT NULL COMMENT '密码盐值',
    nickname VARCHAR(50) COMMENT '昵称',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    gender TINYINT DEFAULT 0 COMMENT '性别: 0-未知, 1-男, 2-女',
    birthday DATE COMMENT '生日',
    city VARCHAR(100) COMMENT '所在城市',
    bio TEXT COMMENT '个人简介',
    voice_intro_url VARCHAR(500) COMMENT '语音介绍URL',
    personal_tags TEXT COMMENT '个人标签，JSON格式',
    profile_images JSON COMMENT '个人相册图片URLs',
    background_image VARCHAR(500) COMMENT '主页背景图',
    privacy_settings JSON COMMENT '隐私设置',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-禁用, 1-正常, 2-锁定',
    user_type TINYINT DEFAULT 1 COMMENT '用户类型: 1-普通用户, 2-服务提供者, 3-管理员',
    is_verified BOOLEAN DEFAULT FALSE COMMENT '是否已认证',
    verified_time DATETIME COMMENT '认证通过时间',
    registration_ip VARCHAR(45) COMMENT '注册IP地址',
    last_login_time DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    last_active_time DATETIME COMMENT '最后活跃时间',
    login_count INT DEFAULT 0 COMMENT '登录次数',
    total_points INT DEFAULT 0 COMMENT '总积分',
    current_level_id BIGINT COMMENT '当前等级ID',
    referral_code VARCHAR(20) UNIQUE COMMENT '个人推荐码',
    referred_by BIGINT COMMENT '推荐人ID',
    credit_score INT DEFAULT 100 COMMENT '信用分（100为初始分）',
    total_orders INT DEFAULT 0 COMMENT '总订单数',
    completion_rate DECIMAL(5,2) DEFAULT 100.00 COMMENT '完成率（百分比）',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '是否删除: 0-未删除, 1-已删除',
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_status (status),
    INDEX idx_user_type (user_type),
    INDEX idx_referral_code (referral_code),
    INDEX idx_created_time (created_time),
    INDEX idx_city (city),
    INDEX idx_verified (is_verified)
) COMMENT='用户基础表';

-- 2. 用户认证信息表（优化版）
CREATE TABLE user_verifications (
    id BIGINT PRIMARY KEY COMMENT '认证ID（雪花算法生成）',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    verification_type TINYINT NOT NULL COMMENT '认证类型: 1-实名认证, 2-职业认证, 3-头像认证, 4-人脸认证, 5-背景调查',
    real_name VARCHAR(50) COMMENT '真实姓名',
    id_card VARCHAR(20) COMMENT '身份证号',
    id_card_front_url VARCHAR(500) COMMENT '身份证正面照片',
    id_card_back_url VARCHAR(500) COMMENT '身份证背面照片',
    face_photo_url VARCHAR(500) COMMENT '人脸照片',
    profession VARCHAR(100) COMMENT '职业',
    company VARCHAR(200) COMMENT '公司名称',
    work_certificate_url VARCHAR(500) COMMENT '工作证明照片',
    professional_certs JSON COMMENT '专业证书信息',
    education_info JSON COMMENT '教育信息',
    work_experience JSON COMMENT '工作经历',
    verification_status TINYINT DEFAULT 0 COMMENT '认证状态: 0-待审核, 1-通过, 2-拒绝, 3-已停用',
    reject_reason VARCHAR(500) COMMENT '拒绝原因',
    reviewer_id BIGINT COMMENT '审核员ID',
    review_time DATETIME COMMENT '审核时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_id (user_id),
    INDEX idx_type (verification_type),
    INDEX idx_status (verification_status),
    INDEX idx_id_card (id_card),
    INDEX idx_created_time (created_time)
) COMMENT='用户认证信息表';

-- 3. 第三方登录表（优化版）
CREATE TABLE social_logins (
    id BIGINT PRIMARY KEY COMMENT '第三方登录ID（雪花算法生成）',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    provider TINYINT NOT NULL COMMENT '第三方平台: 1-微信, 2-QQ, 3-Apple, 4-Google, 5-支付宝',
    open_id VARCHAR(100) NOT NULL COMMENT '第三方平台用户ID',
    union_id VARCHAR(100) COMMENT '第三方平台联合ID',
    provider_username VARCHAR(100) COMMENT '第三方用户名',
    provider_email VARCHAR(100) COMMENT '第三方邮箱',
    nickname VARCHAR(100) COMMENT '第三方平台昵称',
    avatar_url VARCHAR(500) COMMENT '第三方平台头像',
    access_token TEXT COMMENT '访问令牌',
    refresh_token TEXT COMMENT '刷新令牌',
    expires_time DATETIME COMMENT '令牌过期时间',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id),
    UNIQUE KEY uk_provider_openid (provider, open_id),
    INDEX idx_user_id (user_id),
    INDEX idx_provider (provider),
    INDEX idx_open_id (open_id),
    INDEX idx_union_id (union_id)
) COMMENT='第三方登录表';

-- 4. 短信验证码表（优化版）
CREATE TABLE sms_verification_codes (
    id BIGINT PRIMARY KEY COMMENT '验证码ID（雪花算法生成）',
    phone VARCHAR(20) NOT NULL COMMENT '手机号',
    code VARCHAR(10) NOT NULL COMMENT '验证码',
    code_type TINYINT NOT NULL COMMENT '验证码类型: 1-注册, 2-登录, 3-重置密码, 4-修改手机号',
    purpose VARCHAR(50) COMMENT '用途描述',
    used TINYINT DEFAULT 0 COMMENT '是否已使用: 0-未使用, 1-已使用',
    attempt_count INT DEFAULT 0 COMMENT '尝试次数',
    max_attempts INT DEFAULT 3 COMMENT '最大尝试次数',
    ip_address VARCHAR(45) COMMENT '发送IP',
    user_agent VARCHAR(500) COMMENT '用户代理',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    expires_time DATETIME NOT NULL COMMENT '过期时间',
    used_time DATETIME COMMENT '使用时间',
    
    INDEX idx_phone_type (phone, code_type),
    INDEX idx_created_time (created_time),
    INDEX idx_expires_time (expires_time)
) COMMENT='短信验证码表';

-- 5. 拉黑名单表（优化版）
CREATE TABLE blacklists (
    id BIGINT PRIMARY KEY COMMENT '黑名单ID（雪花算法生成）',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    target_type TINYINT NOT NULL COMMENT '目标类型: 1-用户ID, 2-手机号, 3-邮箱, 4-IP地址, 5-设备ID',
    target_value VARCHAR(100) NOT NULL COMMENT '目标值',
    black_user_id BIGINT COMMENT '被拉黑用户ID（当target_type=1时使用）',
    reason VARCHAR(500) COMMENT '拉黑原因',
    operator_id BIGINT COMMENT '操作员ID',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-失效, 1-生效',
    expires_time DATETIME COMMENT '过期时间(为空表示永久)',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (black_user_id) REFERENCES users(id),
    INDEX idx_user_id (user_id),
    INDEX idx_target (target_type, target_value),
    INDEX idx_black_user_id (black_user_id),
    INDEX idx_status (status),
    INDEX idx_created_time (created_time),
    INDEX idx_expires_time (expires_time)
) COMMENT='拉黑名单表';

-- 6. 用户设备管理表（优化版）
CREATE TABLE user_devices (
    id BIGINT PRIMARY KEY COMMENT '设备ID（雪花算法生成）',
    user_id BIGINT COMMENT '用户ID(可为空，用于未登录设备追踪)',
    device_id VARCHAR(64) NOT NULL COMMENT '设备唯一标识',
    device_token VARCHAR(255) COMMENT '设备令牌（推送用）',
    device_type TINYINT NOT NULL COMMENT '设备类型: 1-Android, 2-iOS, 3-Web, 4-H5',
    device_name VARCHAR(100) COMMENT '设备名称',
    device_model VARCHAR(100) COMMENT '设备型号',
    os_version VARCHAR(50) COMMENT '操作系统版本',
    app_version VARCHAR(20) COMMENT 'APP版本',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    location VARCHAR(200) COMMENT '地理位置',
    user_agent VARCHAR(500) COMMENT '用户代理',
    is_trusted TINYINT DEFAULT 0 COMMENT '是否可信设备: 0-否, 1-是',
    push_enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用推送',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-禁用, 1-正常',
    last_active_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '最后活跃时间',
    last_login_time DATETIME COMMENT '最后登录时间',
    login_count INT DEFAULT 0 COMMENT '登录次数',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '首次使用时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id),
    UNIQUE KEY uk_device_id (device_id),
    INDEX idx_user_id (user_id),
    INDEX idx_device_type (device_type),
    INDEX idx_device_token (device_token),
    INDEX idx_ip_address (ip_address),
    INDEX idx_last_active_time (last_active_time)
) COMMENT='用户设备管理表';

-- 7. API调用日志表（优化版）
CREATE TABLE api_logs (
    id BIGINT PRIMARY KEY COMMENT '日志ID（雪花算法生成）',
    user_id BIGINT COMMENT '用户ID',
    device_id VARCHAR(64) COMMENT '设备ID',
    api_path VARCHAR(200) NOT NULL COMMENT 'API路径',
    api_endpoint VARCHAR(255) COMMENT 'API端点',
    http_method VARCHAR(10) NOT NULL COMMENT 'HTTP方法',
    request_params TEXT COMMENT '请求参数',
    request_body TEXT COMMENT '请求体',
    response_code INT COMMENT '响应状态码',
    response_body TEXT COMMENT '响应体',
    response_time_ms INT COMMENT '响应时间(毫秒)',
    error_message TEXT COMMENT '错误信息',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理',
    execution_time INT COMMENT '执行时间(毫秒)',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_api_path (api_path),
    INDEX idx_api_endpoint (api_endpoint),
    INDEX idx_ip_address (ip_address),
    INDEX idx_created_time (created_time),
    INDEX idx_response_code (response_code),
    INDEX idx_user_time (user_id, created_time)
) COMMENT='API调用日志表';

-- 8. 用户登录日志表（保持不变）
CREATE TABLE user_login_logs (
    id BIGINT PRIMARY KEY COMMENT '登录日志ID（雪花算法生成）',
    user_id BIGINT COMMENT '用户ID',
    login_type TINYINT NOT NULL COMMENT '登录类型: 1-密码, 2-短信验证码, 3-第三方',
    login_platform TINYINT NOT NULL COMMENT '登录平台: 1-Android, 2-iOS, 3-Web, 4-H5',
    device_id VARCHAR(64) COMMENT '设备ID',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    location VARCHAR(200) COMMENT '地理位置',
    user_agent VARCHAR(500) COMMENT '用户代理',
    login_status TINYINT NOT NULL COMMENT '登录状态: 1-成功, 2-失败',
    fail_reason VARCHAR(200) COMMENT '失败原因',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_login_type (login_type),
    INDEX idx_login_status (login_status),
    INDEX idx_ip_address (ip_address),
    INDEX idx_created_time (created_time)
) COMMENT='用户登录日志表';

-- 9. 用户会话表（保持不变）
CREATE TABLE user_sessions (
    id BIGINT PRIMARY KEY COMMENT '会话ID（雪花算法生成）',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    session_id VARCHAR(64) UNIQUE NOT NULL COMMENT '会话标识',
    device_id VARCHAR(64) COMMENT '设备ID',
    access_token VARCHAR(500) NOT NULL COMMENT '访问令牌',
    refresh_token VARCHAR(500) NOT NULL COMMENT '刷新令牌',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理',
    expires_time DATETIME NOT NULL COMMENT '过期时间',
    last_active_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '最后活跃时间',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-失效, 1-有效',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_session_id (session_id),
    INDEX idx_access_token (access_token),
    INDEX idx_refresh_token (refresh_token),
    INDEX idx_expires_time (expires_time),
    INDEX idx_last_active_time (last_active_time)
) COMMENT='用户会话表'; 