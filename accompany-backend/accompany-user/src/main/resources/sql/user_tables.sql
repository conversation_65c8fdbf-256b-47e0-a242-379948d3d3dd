-- 用户系统数据库表结构
-- 作者: Accompany团队
-- 时间: 2024

-- 1. 用户基础表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    phone VARCHAR(20) UNIQUE COMMENT '手机号',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    salt VARCHAR(32) NOT NULL COMMENT '密码盐值',
    nickname VARCHAR(50) COMMENT '昵称',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    gender TINYINT DEFAULT 0 COMMENT '性别: 0-未知, 1-男, 2-女',
    birthday DATE COMMENT '生日',
    bio TEXT COMMENT '个人简介',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-禁用, 1-正常, 2-锁定',
    user_type TINYINT DEFAULT 1 COMMENT '用户类型: 1-普通用户, 2-服务提供者, 3-管理员',
    last_login_time DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    login_count INT DEFAULT 0 COMMENT '登录次数',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '是否删除: 0-未删除, 1-已删除',
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_status (status),
    INDEX idx_user_type (user_type),
    INDEX idx_created_time (created_time)
) COMMENT='用户基础表';

-- 2. 用户认证信息表
CREATE TABLE user_verifications (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '认证ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    verification_type TINYINT NOT NULL COMMENT '认证类型: 1-实名认证, 2-职业认证, 3-头像认证',
    real_name VARCHAR(50) COMMENT '真实姓名',
    id_card VARCHAR(20) COMMENT '身份证号',
    id_card_front_url VARCHAR(500) COMMENT '身份证正面照片',
    id_card_back_url VARCHAR(500) COMMENT '身份证背面照片',
    face_photo_url VARCHAR(500) COMMENT '人脸照片',
    profession VARCHAR(100) COMMENT '职业',
    company VARCHAR(200) COMMENT '公司名称',
    work_certificate_url VARCHAR(500) COMMENT '工作证明照片',
    verification_status TINYINT DEFAULT 0 COMMENT '认证状态: 0-待审核, 1-通过, 2-拒绝',
    reject_reason VARCHAR(500) COMMENT '拒绝原因',
    reviewer_id BIGINT COMMENT '审核员ID',
    review_time DATETIME COMMENT '审核时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_id (user_id),
    INDEX idx_type (verification_type),
    INDEX idx_status (verification_status),
    INDEX idx_created_time (created_time)
) COMMENT='用户认证信息表';

-- 3. 第三方登录表
CREATE TABLE social_logins (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '第三方登录ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    provider TINYINT NOT NULL COMMENT '第三方平台: 1-微信, 2-QQ, 3-Apple, 4-Google',
    open_id VARCHAR(100) NOT NULL COMMENT '第三方平台用户ID',
    union_id VARCHAR(100) COMMENT '第三方平台联合ID',
    nickname VARCHAR(100) COMMENT '第三方平台昵称',
    avatar_url VARCHAR(500) COMMENT '第三方平台头像',
    access_token VARCHAR(500) COMMENT '访问令牌',
    refresh_token VARCHAR(500) COMMENT '刷新令牌',
    expires_time DATETIME COMMENT '令牌过期时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id),
    UNIQUE KEY uk_provider_openid (provider, open_id),
    INDEX idx_user_id (user_id),
    INDEX idx_provider (provider),
    INDEX idx_open_id (open_id)
) COMMENT='第三方登录表';

-- 4. 短信验证码表
CREATE TABLE sms_verification_codes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '验证码ID',
    phone VARCHAR(20) NOT NULL COMMENT '手机号',
    code VARCHAR(10) NOT NULL COMMENT '验证码',
    code_type TINYINT NOT NULL COMMENT '验证码类型: 1-注册, 2-登录, 3-重置密码, 4-修改手机号',
    used TINYINT DEFAULT 0 COMMENT '是否已使用: 0-未使用, 1-已使用',
    ip_address VARCHAR(45) COMMENT '发送IP',
    user_agent VARCHAR(500) COMMENT '用户代理',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    expires_time DATETIME NOT NULL COMMENT '过期时间',
    used_time DATETIME COMMENT '使用时间',
    
    INDEX idx_phone (phone),
    INDEX idx_phone_type (phone, code_type),
    INDEX idx_created_time (created_time),
    INDEX idx_expires_time (expires_time)
) COMMENT='短信验证码表';

-- 5. 拉黑名单表
CREATE TABLE blacklists (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '黑名单ID',
    target_type TINYINT NOT NULL COMMENT '目标类型: 1-用户ID, 2-手机号, 3-邮箱, 4-IP地址, 5-设备ID',
    target_value VARCHAR(100) NOT NULL COMMENT '目标值',
    reason VARCHAR(500) COMMENT '拉黑原因',
    operator_id BIGINT COMMENT '操作员ID',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-失效, 1-生效',
    expires_time DATETIME COMMENT '过期时间(为空表示永久)',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_target (target_type, target_value),
    INDEX idx_status (status),
    INDEX idx_created_time (created_time),
    INDEX idx_expires_time (expires_time)
) COMMENT='拉黑名单表';

-- 6. 用户设备管理表
CREATE TABLE user_devices (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '设备ID',
    user_id BIGINT COMMENT '用户ID(可为空，用于未登录设备追踪)',
    device_id VARCHAR(64) NOT NULL COMMENT '设备唯一标识',
    device_type TINYINT NOT NULL COMMENT '设备类型: 1-Android, 2-iOS, 3-Web, 4-H5',
    device_name VARCHAR(100) COMMENT '设备名称',
    os_version VARCHAR(50) COMMENT '操作系统版本',
    app_version VARCHAR(20) COMMENT 'APP版本',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    location VARCHAR(200) COMMENT '地理位置',
    user_agent VARCHAR(500) COMMENT '用户代理',
    is_trusted TINYINT DEFAULT 0 COMMENT '是否可信设备: 0-否, 1-是',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-禁用, 1-正常',
    last_active_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '最后活跃时间',
    login_count INT DEFAULT 0 COMMENT '登录次数',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '首次使用时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id),
    UNIQUE KEY uk_device_id (device_id),
    INDEX idx_user_id (user_id),
    INDEX idx_device_type (device_type),
    INDEX idx_ip_address (ip_address),
    INDEX idx_last_active_time (last_active_time)
) COMMENT='用户设备管理表';

-- 7. API调用日志表
CREATE TABLE api_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    user_id BIGINT COMMENT '用户ID',
    device_id VARCHAR(64) COMMENT '设备ID',
    api_path VARCHAR(200) NOT NULL COMMENT 'API路径',
    http_method VARCHAR(10) NOT NULL COMMENT 'HTTP方法',
    request_params TEXT COMMENT '请求参数',
    request_body TEXT COMMENT '请求体',
    response_code INT COMMENT '响应状态码',
    response_body TEXT COMMENT '响应体',
    error_message TEXT COMMENT '错误信息',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理',
    execution_time INT COMMENT '执行时间(毫秒)',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_api_path (api_path),
    INDEX idx_ip_address (ip_address),
    INDEX idx_created_time (created_time),
    INDEX idx_response_code (response_code)
) COMMENT='API调用日志表';

-- 8. 用户登录日志表
CREATE TABLE user_login_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '登录日志ID',
    user_id BIGINT COMMENT '用户ID',
    login_type TINYINT NOT NULL COMMENT '登录类型: 1-密码, 2-短信验证码, 3-第三方',
    login_platform TINYINT NOT NULL COMMENT '登录平台: 1-Android, 2-iOS, 3-Web, 4-H5',
    device_id VARCHAR(64) COMMENT '设备ID',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    location VARCHAR(200) COMMENT '地理位置',
    user_agent VARCHAR(500) COMMENT '用户代理',
    login_status TINYINT NOT NULL COMMENT '登录状态: 1-成功, 2-失败',
    fail_reason VARCHAR(200) COMMENT '失败原因',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_login_type (login_type),
    INDEX idx_login_status (login_status),
    INDEX idx_ip_address (ip_address),
    INDEX idx_created_time (created_time)
) COMMENT='用户登录日志表';

-- 9. 用户会话表
CREATE TABLE user_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '会话ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    session_id VARCHAR(64) UNIQUE NOT NULL COMMENT '会话标识',
    device_id VARCHAR(64) COMMENT '设备ID',
    access_token VARCHAR(500) NOT NULL COMMENT '访问令牌',
    refresh_token VARCHAR(500) NOT NULL COMMENT '刷新令牌',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理',
    expires_time DATETIME NOT NULL COMMENT '过期时间',
    last_active_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '最后活跃时间',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-失效, 1-有效',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_id (user_id),
    INDEX idx_session_id (session_id),
    INDEX idx_access_token (access_token),
    INDEX idx_refresh_token (refresh_token),
    INDEX idx_expires_time (expires_time),
    INDEX idx_last_active_time (last_active_time)
) COMMENT='用户会话表'; 