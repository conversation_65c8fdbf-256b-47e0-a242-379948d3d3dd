server:
  port: 8081
  servlet:
    context-path: /user

spring:
  application:
    name: accompany-user
  
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************************
    username: testuser
    password: testpass
    # HikariCP 连接池配置
    hikari:
      pool-name: UserServiceHikariCP
      minimum-idle: 5              # 最小空闲连接数
      maximum-pool-size: 20        # 最大连接池大小
      auto-commit: true            # 自动提交
      idle-timeout: 30000          # 空闲超时时间(30秒)
      max-lifetime: 900000         # 连接最大生命周期(15分钟)
      connection-timeout: 10000    # 连接超时时间(10秒)
      connection-test-query: SELECT 1
    
  redis:
    host: localhost
    port: 6379
    database: 0
    password: 
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0

# MyBatis-Plus 配置 (只使用这个，不用 JPA)
mybatis-plus:
  configuration:
    # 驼峰命名转换
    map-underscore-to-camel-case: true
    # 打印 SQL 日志
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 延迟加载开关
    lazy-loading-enabled: true
    # 按需加载
    aggressive-lazy-loading: false
    # 缓存开关
    cache-enabled: true
  global-config:
    # 数据库相关配置
    db-config:
      # 主键类型 (雪花算法生成)
      id-type: assign_id
      # 逻辑删除字段
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      # 表名前缀
      table-prefix: 
      # 字段验证策略
      insert-strategy: not_null
      update-strategy: not_null
      select-strategy: not_null
    # 横幅
    banner: false
  # Mapper XML 文件位置
  mapper-locations: classpath*:mapper/*.xml
  # 类型别名包扫描
  type-aliases-package: com.bigsincerity.accompany.user.entity

logging:
  level:
    com.bigsincerity.accompany.user: debug
    com.bigsincerity.accompany.user.mapper: debug
    org.springframework.security: info
    # MyBatis SQL 日志
    com.baomidou.mybatisplus: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 业务配置
accompany:
  # 雪花算法配置
  snowflake:
    # 工作机器ID (0-31)，如果不配置则自动生成
    worker-id: 
    # 数据中心ID (0-31)，如果不配置则自动生成
    datacenter-id: 
    # 是否自动生成ID（当上面的ID未配置时）
    auto-generate: true
  
  user:
    # 密码配置
    password:
      salt-length: 32
      hash-algorithm: SHA-256
    
    # 短信配置
    sms:
      expire-minutes: 5
      send-limit-per-hour: 10
      code-length: 6
    
    # JWT配置
    jwt:
      secret: accompany-user-secret-key-2024
      access-token-expire-hours: 24
      refresh-token-expire-days: 30
    
    # 设备管理
    device:
      max-devices-per-user: 5
      trust-device-days: 30 