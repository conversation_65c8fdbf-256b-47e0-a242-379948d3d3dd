<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bigsincerity.accompany.user.mapper.UserMapper">

    <!-- 根据用户名查询用户数量 -->
    <select id="countByUserName" resultType="int">
        SELECT COUNT(1) FROM users 
        WHERE username = #{userName} AND deleted = 0
    </select>

    <!-- 根据手机号查询用户数量 -->
    <select id="countByPhone" resultType="int">
        SELECT COUNT(1) FROM users 
        WHERE phone = #{phone} AND deleted = 0
    </select>

    <!-- 根据邮箱查询用户数量 -->
    <select id="countByEmail" resultType="int">
        SELECT COUNT(1) FROM users 
        WHERE email = #{email} AND deleted = 0
    </select>

    <!-- 根据用户名查询用户 -->
    <select id="selectByUserName" resultType="com.bigsincerity.accompany.user.entity.User">
        SELECT * FROM users 
        WHERE username = #{userName} AND deleted = 0
    </select>

    <!-- 根据手机号查询用户 -->
    <select id="selectByPhone" resultType="com.bigsincerity.accompany.user.entity.User">
        SELECT * FROM users 
        WHERE phone = #{phone} AND deleted = 0
    </select>

    <!-- 根据邮箱查询用户 -->
    <select id="selectByEmail" resultType="com.bigsincerity.accompany.user.entity.User">
        SELECT * FROM users 
        WHERE email = #{email} AND deleted = 0
    </select>

    <!-- 根据邀请码查询用户 -->
    <select id="selectByInviteCode" resultType="com.bigsincerity.accompany.user.entity.User">
        SELECT * FROM users 
        WHERE referral_code = #{inviteCode} AND deleted = 0
    </select>

</mapper> 