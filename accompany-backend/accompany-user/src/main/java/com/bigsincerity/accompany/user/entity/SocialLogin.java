package com.bigsincerity.accompany.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 第三方登录实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("social_logins")
public class SocialLogin {

    /**
     * 第三方登录ID（使用雪花算法生成）
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 第三方平台: 1-微信, 2-QQ, 3-Apple, 4-Google, 5-支付宝
     */
    @TableField("provider")
    private Integer provider;

    /**
     * 第三方平台用户ID
     */
    @TableField("open_id")
    private String openId;

    /**
     * 第三方平台联合ID
     */
    @TableField("union_id")
    private String unionId;

    /**
     * 第三方用户名
     */
    @TableField("provider_username")
    private String providerUsername;

    /**
     * 第三方邮箱
     */
    @TableField("provider_email")
    private String providerEmail;

    /**
     * 第三方平台昵称
     */
    @TableField("nickname")
    private String nickname;

    /**
     * 第三方平台头像
     */
    @TableField("avatar_url")
    private String avatarUrl;

    /**
     * 访问令牌
     */
    @TableField("access_token")
    private String accessToken;

    /**
     * 刷新令牌
     */
    @TableField("refresh_token")
    private String refreshToken;

    /**
     * 令牌过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("expires_time")
    private LocalDateTime expiresTime;

    /**
     * 是否激活
     */
    @TableField("is_active")
    private Boolean isActive;

    /**
     * 绑定时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    // 枚举定义
    public enum Provider {
        WECHAT(1, "微信"),
        QQ(2, "QQ"),
        APPLE(3, "Apple"),
        GOOGLE(4, "Google"),
        ALIPAY(5, "支付宝");

        private final Integer code;
        private final String desc;

        Provider(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 令牌是否过期
     */
    public boolean isTokenExpired() {
        return expiresTime != null && expiresTime.isBefore(LocalDateTime.now());
    }

    /**
     * 是否为微信登录
     */
    public boolean isWechatLogin() {
        return Provider.WECHAT.getCode().equals(provider);
    }

    /**
     * 是否为QQ登录
     */
    public boolean isQQLogin() {
        return Provider.QQ.getCode().equals(provider);
    }

    /**
     * 是否为Apple登录
     */
    public boolean isAppleLogin() {
        return Provider.APPLE.getCode().equals(provider);
    }

    /**
     * 获取提供商名称
     */
    public String getProviderName() {
        for (Provider p : Provider.values()) {
            if (p.getCode().equals(provider)) {
                return p.getDesc();
            }
        }
        return "未知";
    }
} 