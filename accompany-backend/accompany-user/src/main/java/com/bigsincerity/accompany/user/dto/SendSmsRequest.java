package com.bigsincerity.accompany.user.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 发送短信验证码请求DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class SendSmsRequest {

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    /**
     * 验证码类型
     */
    @NotBlank(message = "验证码类型不能为空")
    private String type;
} 