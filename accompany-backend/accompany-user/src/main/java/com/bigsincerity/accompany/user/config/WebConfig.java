package com.bigsincerity.accompany.user.config;

import com.bigsincerity.accompany.common.interceptor.TraceInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@RequiredArgsConstructor
public class WebConfig implements WebMvcConfigurer {

    private final TraceInterceptor traceInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册追踪拦截器
        registry.addInterceptor(traceInterceptor)
                .addPathPatterns("/**") // 拦截所有请求
                .excludePathPatterns(
                        "/static/**",       // 排除静态资源
                        "/favicon.ico",     // 排除网站图标
                        "/error",           // 排除错误页面
                        "/actuator/**"      // 排除监控端点
                );
    }
} 