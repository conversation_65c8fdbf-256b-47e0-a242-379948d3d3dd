package com.bigsincerity.accompany.user.service.impl;

import com.bigsincerity.accompany.common.constants.CommonConstants;
import com.bigsincerity.accompany.common.enums.SmsCodeType;
import com.bigsincerity.accompany.user.entity.SmsVerificationCode;
import com.bigsincerity.accompany.user.service.SmsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * 短信服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SmsServiceImpl implements SmsService {

    private final StringRedisTemplate stringRedisTemplate;

    /**
     * 验证码长度
     */
    private static final int CODE_LENGTH = CommonConstants.Business.VERIFICATION_CODE_LENGTH;

    /**
     * 验证码有效期(分钟)
     */
    private static final int CODE_EXPIRE_MINUTES = CommonConstants.Time.SMS_CODE_EXPIRE_MINUTES;

    /**
     * 同一手机号发送间隔(秒)
     */
    private static final int SEND_INTERVAL_SECONDS = CommonConstants.Time.SMS_SEND_INTERVAL_SECONDS;

    /**
     * Redis键前缀
     */
    private static final String SMS_CODE_PREFIX = CommonConstants.Cache.SMS_CODE_PREFIX;
    
    /**
     * Redis发送限制键前缀
     */
    private static final String SMS_LIMIT_PREFIX = CommonConstants.Cache.SMS_LIMIT_PREFIX;

    @Override
    public boolean sendVerificationCode(String phone, Integer codeType, String ipAddress, String userAgent) {
        log.info("发送短信验证码: phone={}, codeType={}", phone, codeType);

        try {
            // 检查发送频率限制
            if (!checkSendLimit(phone, codeType)) {
                return false;
            }

            // 生成验证码
            String code = generateCode();

            // 发送短信 (这里模拟发送，实际项目中需要接入短信服务商)
            boolean sendResult = sendSms(phone, code, codeType);
            
            if (!sendResult) {
                log.error("短信发送失败: phone={}, codeType={}", phone, codeType);
                return false;
            }

            // 存储验证码到Redis，设置5分钟过期
            String key = buildCacheKey(phone, codeType);
            stringRedisTemplate.opsForValue().set(key, code, CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);

            // 设置发送限制，防止频繁发送
            String limitKey = buildLimitKey(phone, codeType);
            stringRedisTemplate.opsForValue().set(limitKey, "1", SEND_INTERVAL_SECONDS, TimeUnit.SECONDS);

            log.info("短信验证码发送成功: phone={}, code={}", phone, code);
            return true;
            
        } catch (Exception e) {
            log.error("短信验证码发送失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean verifyCode(String phone, String code, Integer codeType) {
        log.info("验证短信验证码: phone={}, code={}, codeType={}", phone, code, codeType);

        if (phone == null || code == null || codeType == null) {
            return false;
        }

        String key = buildCacheKey(phone, codeType);
        String cachedCode = stringRedisTemplate.opsForValue().get(key);

        if (cachedCode == null) {
            log.warn("验证码不存在或已过期: phone={}, codeType={}", phone, codeType);
            return false;
        }

        // 验证码校验
        boolean isValid = code.equals(cachedCode);
        
        if (isValid) {
            // 验证成功，移除验证码
            stringRedisTemplate.delete(key);
            log.info("验证码验证成功: phone={}, codeType={}", phone, codeType);
        } else {
            log.warn("验证码错误: phone={}, codeType={}, expected={}, actual={}", 
                    phone, codeType, cachedCode, code);
        }

        return isValid;
    }

    @Override
    public SmsVerificationCode getValidCode(String phone, Integer codeType) {
        String key = buildCacheKey(phone, codeType);
        String cachedCode = stringRedisTemplate.opsForValue().get(key);
        
        if (cachedCode == null) {
            return null;
        }

        // 返回一个临时的SmsVerificationCode对象
        SmsVerificationCode smsCode = new SmsVerificationCode();
        smsCode.setPhone(phone);
        smsCode.setCode(cachedCode);
        smsCode.setCodeType(codeType);
        smsCode.setCreatedTime(LocalDateTime.now()); // Redis中无法获取创建时间，使用当前时间
        return smsCode;
    }

    @Override
    public boolean checkSendLimit(String phone, Integer codeType) {
        String limitKey = buildLimitKey(phone, codeType);
        String limitValue = stringRedisTemplate.opsForValue().get(limitKey);

        if (limitValue != null) {
            // 获取TTL（剩余过期时间）
            Long ttl = stringRedisTemplate.getExpire(limitKey, TimeUnit.SECONDS);
            long waitSeconds = ttl != null ? ttl : 0;
            
            log.warn("发送过于频繁: phone={}, codeType={}, 需等待{}秒", 
                    phone, codeType, waitSeconds);
            return false;
        }

        return true;
    }

    @Override
    public String generateCode() {
        SecureRandom random = new SecureRandom();
        StringBuilder code = new StringBuilder();
        
        for (int i = 0; i < CODE_LENGTH; i++) {
            code.append(random.nextInt(10));
        }
        
        return code.toString();
    }

    @Override
    public void markCodeAsUsed(Long codeId) {
        // 由于我们使用的是内存缓存，这里不需要特别处理
        // 在实际项目中，如果使用数据库，需要更新状态
        log.info("标记验证码为已使用: codeId={}", codeId);
    }

    // 简化的验证方法，供UserService使用
    public boolean verifyCode(String phone, String code, String type) {
        Integer codeType = getCodeTypeFromString(type);
        return verifyCode(phone, code, codeType);
    }

    // 简化的发送方法，供Controller使用
    public void sendVerificationCode(String phone, String type) {
        Integer codeType = getCodeTypeFromString(type);
        boolean result = sendVerificationCode(phone, codeType, null, null);
        if (!result) {
            throw new RuntimeException("验证码发送失败");
        }
    }

    /**
     * 字符串类型转换为数字类型
     */
    private Integer getCodeTypeFromString(String type) {
        return SmsCodeType.getCodeByType(type);
    }

    /**
     * 发送短信 (模拟实现)
     */
    private boolean sendSms(String phone, String code, Integer codeType) {
        try {
            String content = buildSmsContent(code, codeType);
            log.info("模拟发送短信: phone={}, codeType={}, content={}", phone, codeType, content);
            
            // 模拟发送延迟
            Thread.sleep(100);
            
            // 模拟成功率 (95%)
            return new SecureRandom().nextInt(100) < 95;
            
        } catch (Exception e) {
            log.error("短信发送异常: phone={}, code={}", phone, code, e);
            return false;
        }
    }

    /**
     * 构建短信内容
     */
    private String buildSmsContent(String code, Integer codeType) {
        String template = SmsCodeType.getMessageTemplateByCode(codeType);
        return String.format(template, code);
    }

    /**
     * 构建验证码缓存key
     */
    private String buildCacheKey(String phone, Integer codeType) {
        return SMS_CODE_PREFIX + phone + ":" + codeType;
    }

    /**
     * 构建发送限制key
     */
    private String buildLimitKey(String phone, Integer codeType) {
        return SMS_LIMIT_PREFIX + phone + ":" + codeType;
    }
} 