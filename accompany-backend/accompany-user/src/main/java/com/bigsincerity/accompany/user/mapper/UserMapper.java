package com.bigsincerity.accompany.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bigsincerity.accompany.user.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户数据访问层
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据用户名查询用户数量
     */
    int countByUserName(@Param("userName") String userName);

    /**
     * 根据手机号查询用户数量
     */
    int countByPhone(@Param("phone") String phone);

    /**
     * 根据邮箱查询用户数量
     */
    int countByEmail(@Param("email") String email);

    /**
     * 根据用户名查询用户
     */
    User selectByUserName(@Param("userName") String userName);

    /**
     * 根据手机号查询用户
     */
    User selectByPhone(@Param("phone") String phone);

    /**
     * 根据邮箱查询用户
     */
    User selectByEmail(@Param("email") String email);

    /**
     * 根据邀请码查询用户
     */
    User selectByInviteCode(@Param("inviteCode") String inviteCode);
} 