package com.bigsincerity.accompany.user.util;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

/**
 * 密码加密工具类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class PasswordUtil {

    /**
     * 盐值长度
     */
    private static final int SALT_LENGTH = 16;

    /**
     * 分隔符
     */
    private static final String SEPARATOR = "$";

    /**
     * 加密结果类
     */
    @Data
    public static class EncodeResult {
        /**
         * 盐值
         */
        private String salt;
        
        /**
         * 哈希值
         */
        private String hash;
        
        /**
         * 完整的加密密码 (salt$hash)
         */
        private String encodedPassword;
        
        public EncodeResult(String salt, String hash) {
            this.salt = salt;
            this.hash = hash;
            this.encodedPassword = salt + SEPARATOR + hash;
        }
    }

    /**
     * 加密密码（返回详细结果）
     *
     * @param rawPassword 原始密码
     * @return 加密结果
     */
    public static EncodeResult encodeWithDetails(String rawPassword) {
        try {
            // 生成随机盐值
            String salt = generateSalt();
            
            // 加密密码
            String hashedPassword = hashPassword(rawPassword, salt);
            
            return new EncodeResult(salt, hashedPassword);
            
        } catch (Exception e) {
            log.error("密码加密失败", e);
            throw new RuntimeException("密码加密失败", e);
        }
    }

    /**
     * 加密密码
     *
     * @param rawPassword 原始密码
     * @return 加密后的密码(包含盐值)
     */
    public static String encode(String rawPassword) {
        return encodeWithDetails(rawPassword).getEncodedPassword();
    }

    /**
     * 验证密码
     *
     * @param rawPassword     原始密码
     * @param encodedPassword 加密后的密码
     * @return 是否匹配
     */
    public static boolean matches(String rawPassword, String encodedPassword) {
        try {
            if (rawPassword == null || encodedPassword == null) {
                return false;
            }

            // 分离盐值和哈希值
            String[] parts = encodedPassword.split("\\" + SEPARATOR);
            if (parts.length != 2) {
                log.warn("加密密码格式错误: {}", encodedPassword);
                return false;
            }

            String salt = parts[0];
            String storedHash = parts[1];

            // 使用相同的盐值加密输入的密码
            String inputHash = hashPassword(rawPassword, salt);

            // 比较哈希值
            return storedHash.equals(inputHash);

        } catch (Exception e) {
            log.error("密码验证失败", e);
            return false;
        }
    }

    /**
     * 生成随机盐值
     */
    private static String generateSalt() {
        SecureRandom random = new SecureRandom();
        byte[] salt = new byte[SALT_LENGTH];
        random.nextBytes(salt);
        return bytesToHex(salt);
    }

    /**
     * 使用SHA-256哈希密码
     */
    private static String hashPassword(String password, String salt) throws NoSuchAlgorithmException {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        
        // 添加盐值
        digest.update(salt.getBytes(StandardCharsets.UTF_8));
        
        // 添加密码
        byte[] hash = digest.digest(password.getBytes(StandardCharsets.UTF_8));
        
        return bytesToHex(hash);
    }

    /**
     * 字节数组转十六进制字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }

    /**
     * 验证密码强度
     *
     * @param password 密码
     * @return 是否符合强度要求
     */
    public static boolean isStrongPassword(String password) {
        if (password == null || password.length() < 8) {
            return false;
        }

        // 检查是否包含大写字母、小写字母、数字
        boolean hasUpper = false;
        boolean hasLower = false;
        boolean hasDigit = false;
        boolean hasSpecial = false;

        for (char c : password.toCharArray()) {
            if (Character.isUpperCase(c)) {
                hasUpper = true;
            } else if (Character.isLowerCase(c)) {
                hasLower = true;
            } else if (Character.isDigit(c)) {
                hasDigit = true;
            } else if (!Character.isLetterOrDigit(c)) {
                hasSpecial = true;
            }
        }

        // 至少包含三种类型的字符
        int typeCount = 0;
        if (hasUpper) typeCount++;
        if (hasLower) typeCount++;
        if (hasDigit) typeCount++;
        if (hasSpecial) typeCount++;

        return typeCount >= 3;
    }

    /**
     * 生成随机密码
     *
     * @param length 密码长度
     * @return 随机密码
     */
    public static String generateRandomPassword(int length) {
        if (length < 8) {
            length = 8;
        }

        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
        SecureRandom random = new SecureRandom();
        StringBuilder password = new StringBuilder();

        for (int i = 0; i < length; i++) {
            password.append(chars.charAt(random.nextInt(chars.length())));
        }

        return password.toString();
    }
} 