package com.bigsincerity.accompany.user.service;

import com.bigsincerity.accompany.user.dto.LoginRequest;
import com.bigsincerity.accompany.user.dto.RegisterRequest;
import com.bigsincerity.accompany.user.vo.LoginResponse;

/**
 * 用户服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface UserService {

    /**
     * 用户注册
     * 
     * @param request 注册请求
     * @param clientIp 客户端IP
     */
    void register(RegisterRequest request, String clientIp);

    /**
     * 用户登录
     * 
     * @param request 登录请求
     * @param clientIp 客户端IP
     * @param userAgent 用户代理
     * @return 登录响应信息
     */
    LoginResponse login(LoginRequest request, String clientIp, String userAgent);

    /**
     * 用户登出
     * 
     * @param token 访问令牌
     */
    void logout(String token);

    /**
     * 刷新令牌
     * 
     * @param refreshToken 刷新令牌
     * @return 新的登录响应信息
     */
    LoginResponse refreshToken(String refreshToken);

    /**
     * 检查用户名是否可用
     * 
     * @param username 用户名
     * @return 是否可用
     */
    boolean isUsernameAvailable(String username);

    /**
     * 检查手机号是否可用
     * 
     * @param phone 手机号
     * @return 是否可用
     */
    boolean isPhoneAvailable(String phone);

    /**
     * 检查邮箱是否可用
     * 
     * @param email 邮箱
     * @return 是否可用
     */
    boolean isEmailAvailable(String email);
} 