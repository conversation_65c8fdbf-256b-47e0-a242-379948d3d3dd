package com.bigsincerity.accompany.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户设备管理实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user_devices")
public class UserDevice {

    /**
     * 设备ID（使用雪花算法生成）
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户ID(可为空，用于未登录设备追踪)
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 设备唯一标识
     */
    @TableField("device_id")
    private String deviceId;

    /**
     * 设备令牌（推送用）
     */
    @TableField("device_token")
    private String deviceToken;

    /**
     * 设备类型: 1-Android, 2-iOS, 3-Web, 4-H5
     */
    @TableField("device_type")
    private Integer deviceType;

    /**
     * 设备名称
     */
    @TableField("device_name")
    private String deviceName;

    /**
     * 设备型号
     */
    @TableField("device_model")
    private String deviceModel;

    /**
     * 操作系统版本
     */
    @TableField("os_version")
    private String osVersion;

    /**
     * APP版本
     */
    @TableField("app_version")
    private String appVersion;

    /**
     * IP地址
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 地理位置
     */
    @TableField("location")
    private String location;

    /**
     * 用户代理
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 是否可信设备: 0-否, 1-是
     */
    @TableField("is_trusted")
    private Integer isTrusted;

    /**
     * 是否启用推送
     */
    @TableField("push_enabled")
    private Boolean pushEnabled;

    /**
     * 状态: 0-禁用, 1-正常
     */
    @TableField("status")
    private Integer status;

    /**
     * 最后活跃时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("last_active_time")
    private LocalDateTime lastActiveTime;

    /**
     * 最后登录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("last_login_time")
    private LocalDateTime lastLoginTime;

    /**
     * 登录次数
     */
    @TableField("login_count")
    private Integer loginCount;

    /**
     * 首次使用时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    // 枚举定义
    public enum DeviceType {
        ANDROID(1, "Android"),
        IOS(2, "iOS"),
        WEB(3, "Web"),
        H5(4, "H5");

        private final Integer code;
        private final String desc;

        DeviceType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    public enum TrustedStatus {
        NOT_TRUSTED(0, "不可信"),
        TRUSTED(1, "可信");

        private final Integer code;
        private final String desc;

        TrustedStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    public enum Status {
        DISABLED(0, "禁用"),
        NORMAL(1, "正常");

        private final Integer code;
        private final String desc;

        Status(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 是否为移动设备
     */
    public boolean isMobileDevice() {
        return DeviceType.ANDROID.getCode().equals(deviceType) || 
               DeviceType.IOS.getCode().equals(deviceType);
    }

    /**
     * 是否为Web设备
     */
    public boolean isWebDevice() {
        return DeviceType.WEB.getCode().equals(deviceType) || 
               DeviceType.H5.getCode().equals(deviceType);
    }

    /**
     * 是否为可信设备
     */
    public boolean isTrustedDevice() {
        return TrustedStatus.TRUSTED.getCode().equals(isTrusted);
    }

    /**
     * 是否状态正常
     */
    public boolean isStatusNormal() {
        return Status.NORMAL.getCode().equals(status);
    }

    /**
     * 是否长时间未活跃（超过30天）
     */
    public boolean isLongTimeInactive() {
        if (lastActiveTime == null) {
            return false;
        }
        return lastActiveTime.isBefore(LocalDateTime.now().minusDays(30));
    }

    /**
     * 获取设备类型描述
     */
    public String getDeviceTypeDesc() {
        for (DeviceType type : DeviceType.values()) {
            if (type.getCode().equals(deviceType)) {
                return type.getDesc();
            }
        }
        return "未知";
    }

    /**
     * 刷新最后活跃时间
     */
    public void refreshLastActiveTime() {
        this.lastActiveTime = LocalDateTime.now();
    }

    /**
     * 增加登录次数
     */
    public void incrementLoginCount() {
        if (this.loginCount == null) {
            this.loginCount = 0;
        }
        this.loginCount++;
        this.lastLoginTime = LocalDateTime.now();
    }
} 