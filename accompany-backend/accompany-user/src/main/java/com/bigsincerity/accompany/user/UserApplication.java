package com.bigsincerity.accompany.user;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 用户服务启动类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootApplication(
    scanBasePackages = {
        "com.bigsincerity.accompany.user",
        "com.bigsincerity.accompany.common"
    }
)
@MapperScan("com.bigsincerity.accompany.user.mapper")
public class UserApplication {

    public static void main(String[] args) {
        SpringApplication.run(UserApplication.class, args);
    }
} 