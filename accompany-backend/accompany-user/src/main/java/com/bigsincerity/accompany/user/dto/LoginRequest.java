package com.bigsincerity.accompany.user.dto;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;

/**
 * 登录请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class LoginRequest {

    /**
     * 账户(用户名/手机号/邮箱)
     */
    @NotBlank(message = "账户不能为空")
    private String account;

    /**
     * 密码
     */
    private String password;

    /**
     * 验证码(短信登录时使用)
     */
    private String verificationCode;

    /**
     * 登录类型: 1-密码登录, 2-短信验证码登录
     */
    private Integer loginType = 1;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 设备类型: 1-Android, 2-iOS, 3-Web, 4-H5
     */
    private Integer deviceType = 3;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 记住登录状态
     */
    private Boolean rememberMe = false;

    // 兼容前端的字段映射
    public String getUsername() {
        return account;
    }

    public void setUsername(String username) {
        this.account = username;
    }

    // 枚举定义
    public enum LoginType {
        PASSWORD(1, "密码登录"),
        SMS_CODE(2, "短信验证码登录");

        private final Integer code;
        private final String desc;

        LoginType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    public enum DeviceType {
        ANDROID(1, "Android"),
        IOS(2, "iOS"),
        WEB(3, "Web"),
        H5(4, "H5");

        private final Integer code;
        private final String desc;

        DeviceType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
} 