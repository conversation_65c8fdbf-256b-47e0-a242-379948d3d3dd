package com.bigsincerity.accompany.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 短信验证码实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sms_verification_codes")
public class SmsVerificationCode {

    /**
     * 验证码ID（使用雪花算法生成）
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;

    /**
     * 验证码
     */
    @TableField("code")
    private String code;

    /**
     * 验证码类型: 1-注册, 2-登录, 3-重置密码, 4-修改手机号
     */
    @TableField("code_type")
    private Integer codeType;

    /**
     * 是否已使用: 0-未使用, 1-已使用
     */
    @TableField("used")
    private Integer used;

    /**
     * 发送IP
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 用户代理
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 过期时间
     */
    @TableField("expires_time")
    private LocalDateTime expiresTime;

    /**
     * 使用时间
     */
    @TableField("used_time")
    private LocalDateTime usedTime;

    // 枚举定义
    public enum CodeType {
        REGISTER(1, "注册"),
        LOGIN(2, "登录"),
        RESET_PASSWORD(3, "重置密码"),
        CHANGE_PHONE(4, "修改手机号");

        private final Integer code;
        private final String desc;

        CodeType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    public enum UsedStatus {
        UNUSED(0, "未使用"),
        USED(1, "已使用");

        private final Integer code;
        private final String desc;

        UsedStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 检查验证码是否有效
     */
    public boolean isValid() {
        return used == UsedStatus.UNUSED.getCode() && 
               expiresTime != null && 
               expiresTime.isAfter(LocalDateTime.now());
    }

    /**
     * 标记为已使用
     */
    public void markAsUsed() {
        this.used = UsedStatus.USED.getCode();
        this.usedTime = LocalDateTime.now();
    }
} 