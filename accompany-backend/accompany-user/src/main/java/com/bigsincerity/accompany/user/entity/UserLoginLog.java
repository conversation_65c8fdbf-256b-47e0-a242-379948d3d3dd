package com.bigsincerity.accompany.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户登录日志实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user_login_logs")
public class UserLoginLog {

    /**
     * 登录日志ID（使用雪花算法生成）
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 登录类型: 1-密码, 2-短信验证码, 3-第三方
     */
    @TableField("login_type")
    private Integer loginType;

    /**
     * 登录平台: 1-Android, 2-iOS, 3-Web, 4-H5
     */
    @TableField("login_platform")
    private Integer loginPlatform;

    /**
     * 设备ID
     */
    @TableField("device_id")
    private String deviceId;

    /**
     * IP地址
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 地理位置
     */
    @TableField("location")
    private String location;

    /**
     * 用户代理
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 登录状态: 1-成功, 2-失败
     */
    @TableField("login_status")
    private Integer loginStatus;

    /**
     * 失败原因
     */
    @TableField("fail_reason")
    private String failReason;

    /**
     * 登录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    // 枚举定义
    public enum LoginType {
        PASSWORD(1, "密码登录"),
        SMS_CODE(2, "短信验证码"),
        SOCIAL(3, "第三方登录");

        private final Integer code;
        private final String desc;

        LoginType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    public enum LoginPlatform {
        ANDROID(1, "Android"),
        IOS(2, "iOS"),
        WEB(3, "Web"),
        H5(4, "H5");

        private final Integer code;
        private final String desc;

        LoginPlatform(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    public enum LoginStatus {
        SUCCESS(1, "成功"),
        FAILED(2, "失败");

        private final Integer code;
        private final String desc;

        LoginStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 是否登录成功
     */
    public boolean isLoginSuccess() {
        return LoginStatus.SUCCESS.getCode().equals(loginStatus);
    }

    /**
     * 是否登录失败
     */
    public boolean isLoginFailed() {
        return LoginStatus.FAILED.getCode().equals(loginStatus);
    }

    /**
     * 是否为移动端登录
     */
    public boolean isMobileLogin() {
        return LoginPlatform.ANDROID.getCode().equals(loginPlatform) || 
               LoginPlatform.IOS.getCode().equals(loginPlatform);
    }

    /**
     * 是否为Web端登录
     */
    public boolean isWebLogin() {
        return LoginPlatform.WEB.getCode().equals(loginPlatform) || 
               LoginPlatform.H5.getCode().equals(loginPlatform);
    }

    /**
     * 是否为密码登录
     */
    public boolean isPasswordLogin() {
        return LoginType.PASSWORD.getCode().equals(loginType);
    }

    /**
     * 是否为第三方登录
     */
    public boolean isSocialLogin() {
        return LoginType.SOCIAL.getCode().equals(loginType);
    }

    /**
     * 获取登录类型描述
     */
    public String getLoginTypeDesc() {
        for (LoginType type : LoginType.values()) {
            if (type.getCode().equals(loginType)) {
                return type.getDesc();
            }
        }
        return "未知";
    }

    /**
     * 获取登录平台描述
     */
    public String getLoginPlatformDesc() {
        for (LoginPlatform platform : LoginPlatform.values()) {
            if (platform.getCode().equals(loginPlatform)) {
                return platform.getDesc();
            }
        }
        return "未知";
    }

    /**
     * 获取登录状态描述
     */
    public String getLoginStatusDesc() {
        for (LoginStatus status : LoginStatus.values()) {
            if (status.getCode().equals(loginStatus)) {
                return status.getDesc();
            }
        }
        return "未知";
    }
} 