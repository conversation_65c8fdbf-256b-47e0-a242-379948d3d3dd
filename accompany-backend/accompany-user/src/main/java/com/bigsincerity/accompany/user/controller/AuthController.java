package com.bigsincerity.accompany.user.controller;

import com.bigsincerity.accompany.common.result.Result;
import com.bigsincerity.accompany.user.dto.LoginRequest;
import com.bigsincerity.accompany.user.dto.RegisterRequest;
import com.bigsincerity.accompany.user.dto.SendSmsRequest;
import com.bigsincerity.accompany.user.service.UserService;
import com.bigsincerity.accompany.user.service.SmsService;
import com.bigsincerity.accompany.user.vo.LoginResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

/**
 * 认证控制器
 * 处理用户登录、注册相关请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:5173"})
public class AuthController {

    private final UserService userService;
    private final SmsService smsService;

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public Result<String> register(@Valid @RequestBody RegisterRequest request, 
                                  HttpServletRequest httpRequest) {
        try {
             log.info("用户注册请求: {}", request.getUserName());
            
            // 获取客户端IP
            String clientIp = getClientIp(httpRequest);
            
            // 执行注册
            userService.register(request, clientIp);
            
            log.info("用户注册成功: {}", request.getUserName());
            return Result.success("注册成功");
            
        } catch (Exception e) {
            log.error("用户注册失败: {}", e.getMessage(), e);
            return Result.error("注册失败: " + e.getMessage());
        }
    }

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public Result<LoginResponse> login(@Valid @RequestBody LoginRequest request,
                                     HttpServletRequest httpRequest) {
        try {
            log.info("用户登录请求: {}", request.getAccount());
            
            // 获取客户端IP
            String clientIp = getClientIp(httpRequest);
            String userAgent = httpRequest.getHeader("User-Agent");
            
            // 执行登录
            LoginResponse response = userService.login(request, clientIp, userAgent);
            
            log.info("用户登录成功: {}", request.getAccount());
            return Result.success(response);
            
        } catch (Exception e) {
            log.error("用户登录失败: {}", e.getMessage(), e);
            return Result.error("登录失败: " + e.getMessage());
        }
    }

    /**
     * 发送短信验证码
     */
    @PostMapping("/sms/send")
    public Result<String> sendSms(@Valid @RequestBody SendSmsRequest request) {
        try {
            log.info("发送短信验证码请求: {}", request.getPhone());
            
            smsService.sendVerificationCode(request.getPhone(), request.getType());
            
            log.info("短信验证码发送成功: {}", request.getPhone());
            return Result.success("验证码发送成功");
            
        } catch (Exception e) {
            log.error("短信验证码发送失败: {}", e.getMessage(), e);
            return Result.error("验证码发送失败: " + e.getMessage());
        }
    }

    /**
     * 检查用户名是否可用
     */
    @GetMapping("/check/username")
    public Result<Boolean> checkUsername(@RequestParam String username) {
        try {
            boolean available = userService.isUsernameAvailable(username);
            return Result.success(available);
        } catch (Exception e) {
            log.error("检查用户名失败: {}", e.getMessage(), e);
            return Result.error("检查失败");
        }
    }

    /**
     * 检查手机号是否可用
     */
    @GetMapping("/check/phone")
    public Result<Boolean> checkPhone(@RequestParam String phone) {
        try {
            boolean available = userService.isPhoneAvailable(phone);
            return Result.success(available);
        } catch (Exception e) {
            log.error("检查手机号失败: {}", e.getMessage(), e);
            return Result.error("检查失败");
        }
    }

    /**
     * 检查邮箱是否可用
     */
    @GetMapping("/check/email")
    public Result<Boolean> checkEmail(@RequestParam String email) {
        try {
            boolean available = userService.isEmailAvailable(email);
            return Result.success(available);
        } catch (Exception e) {
            log.error("检查邮箱失败: {}", e.getMessage(), e);
            return Result.error("检查失败");
        }
    }

    /**
     * 刷新Token
     */
    @PostMapping("/refresh")
    public Result<LoginResponse> refreshToken(@RequestParam String refreshToken) {
        try {
            LoginResponse response = userService.refreshToken(refreshToken);
            return Result.success(response);
        } catch (Exception e) {
            log.error("刷新Token失败: {}", e.getMessage(), e);
            return Result.error("Token刷新失败");
        }
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public Result<String> logout(@RequestHeader("Authorization") String token) {
        try {
            userService.logout(token);
            return Result.success("登出成功");
        } catch (Exception e) {
            log.error("用户登出失败: {}", e.getMessage(), e);
            return Result.error("登出失败");
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StringUtils.isNotBlank(xForwardedFor) && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0];
        }
        String xRealIp = request.getHeader("X-Real-IP");
        if (StringUtils.isNotBlank(xRealIp) && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    /**
     *  获取客户端语言，默认中文
     */
    private String getClientLanguage(HttpServletRequest request) {
        String acceptLanguage = request.getHeader("Accept-Language");
        if (StringUtils.isNotBlank(acceptLanguage)) {
            return acceptLanguage.split(",")[0];
        }
        return "zh-CN";
    }
} 