package com.bigsincerity.accompany.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户实体类（优化版）
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("users")
public class User {

    /**
     * 用户ID（使用雪花算法生成）
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户名
     */
    @TableField("username")
    private String username;

    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;

    /**
     * 密码哈希
     */
    @JsonIgnore
    @TableField("password_hash")
    private String passwordHash;

    /**
     * 密码盐值
     */
    @JsonIgnore
    @TableField("salt")
    private String salt;

    /**
     * 昵称
     */
    @TableField("nickname")
    private String nickname;

    /**
     * 头像URL
     */
    @TableField("avatar_url")
    private String avatarUrl;

    /**
     * 性别: 0-未知, 1-男, 2-女
     */
    @TableField("gender")
    private Integer gender;

    /**
     * 生日
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @TableField("birthday")
    private LocalDate birthday;

    /**
     * 所在城市
     */
    @TableField("city")
    private String city;

    /**
     * 个人简介
     */
    @TableField("bio")
    private String bio;

    /**
     * 语音介绍URL
     */
    @TableField("voice_intro_url")
    private String voiceIntroUrl;

    /**
     * 个人标签，JSON格式
     */
    @TableField("personal_tags")
    private String personalTags;

    /**
     * 个人相册图片URLs (JSON格式存储)
     */
    @TableField("profile_images")
    private String profileImages;

    /**
     * 主页背景图
     */
    @TableField("background_image")
    private String backgroundImage;

    /**
     * 隐私设置(JSON格式)
     */
    @TableField("privacy_settings")
    private String privacySettings;

    /**
     * 状态: 0-禁用, 1-正常, 2-锁定
     */
    @TableField("status")
    private Integer status;

    /**
     * 用户类型: 1-普通用户, 2-服务提供者, 3-管理员
     */
    @TableField("user_type")
    private Integer userType;

    /**
     * 是否已认证
     */
    @TableField("is_verified")
    private Boolean isVerified;

    /**
     * 认证通过时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("verified_time")
    private LocalDateTime verifiedTime;

    /**
     * 注册IP地址
     */
    @TableField("registration_ip")
    private String registrationIp;

    /**
     * 最后登录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("last_login_time")
    private LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     */
    @TableField("last_login_ip")
    private String lastLoginIp;

    /**
     * 最后活跃时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("last_active_time")
    private LocalDateTime lastActiveTime;

    /**
     * 登录次数
     */
    @TableField("login_count")
    private Integer loginCount;

    /**
     * 总积分
     */
    @TableField("total_points")
    private Integer totalPoints;

    /**
     * 当前等级ID
     */
    @TableField("current_level_id")
    private Long currentLevelId;

    /**
     * 个人推荐码
     */
    @TableField("referral_code")
    private String referralCode;

    /**
     * 推荐人ID
     */
    @TableField("referred_by")
    private Long referredBy;

    /**
     * 信用分（100为初始分）
     */
    @TableField("credit_score")
    private Integer creditScore;

    /**
     * 总订单数
     */
    @TableField("total_orders")
    private Integer totalOrders;

    /**
     * 完成率（百分比）
     */
    @TableField("completion_rate")
    private BigDecimal completionRate;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 是否删除: 0-未删除, 1-已删除
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    // ========== 业务方法 ==========

    /**
     * 获取年龄
     */
    public Integer getAge() {
        if (birthday == null) {
            return null;
        }
        return LocalDate.now().getYear() - birthday.getYear();
    }

    /**
     * 是否为服务提供者
     */
    public Boolean isServiceProvider() {
        return UserType.SERVICE_PROVIDER.getCode().equals(userType);
    }

    /**
     * 是否为管理员
     */
    public Boolean isAdmin() {
        return UserType.ADMIN.getCode().equals(userType);
    }

    /**
     * 账号是否正常
     */
    public Boolean isAccountNormal() {
        return Status.NORMAL.getCode().equals(status);
    }

    /**
     * 获取个人标签列表
     */
    public List<String> getPersonalTagList() {
        if (personalTags == null || personalTags.trim().isEmpty()) {
            return List.of();
        }
        // 简单的逗号分隔解析，后续可改为JSON解析
        return List.of(personalTags.split(","));
    }

    /**
     * 设置个人标签列表
     */
    public void setPersonalTagList(List<String> tags) {
        if (tags == null || tags.isEmpty()) {
            this.personalTags = null;
        } else {
            this.personalTags = String.join(",", tags);
        }
    }

    /**
     * 获取用户显示名称
     */
    public String getDisplayName() {
        if (nickname != null && !nickname.trim().isEmpty()) {
            return nickname;
        }
        return username != null ? username : "用户" + id;
    }

    // 枚举定义
    public enum Status {
        DISABLED(0, "禁用"),
        NORMAL(1, "正常"),
        LOCKED(2, "锁定");

        private final Integer code;
        private final String desc;

        Status(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    public enum UserType {
        NORMAL_USER(1, "普通用户"),
        SERVICE_PROVIDER(2, "服务提供者"),
        ADMIN(3, "管理员");

        private final Integer code;
        private final String desc;

        UserType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    public enum Gender {
        UNKNOWN(0, "未知"),
        MALE(1, "男"),
        FEMALE(2, "女");

        private final Integer code;
        private final String desc;

        Gender(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
} 