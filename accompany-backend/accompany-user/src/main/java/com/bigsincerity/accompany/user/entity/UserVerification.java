package com.bigsincerity.accompany.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户认证信息实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user_verifications")
public class UserVerification {

    /**
     * 认证ID（使用雪花算法生成）
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 认证类型: 1-实名认证, 2-职业认证, 3-头像认证, 4-人脸认证, 5-背景调查
     */
    @TableField("verification_type")
    private Integer verificationType;

    /**
     * 真实姓名
     */
    @TableField("real_name")
    private String realName;

    /**
     * 身份证号
     */
    @TableField("id_card")
    private String idCard;

    /**
     * 身份证正面照片
     */
    @TableField("id_card_front_url")
    private String idCardFrontUrl;

    /**
     * 身份证背面照片
     */
    @TableField("id_card_back_url")
    private String idCardBackUrl;

    /**
     * 人脸照片
     */
    @TableField("face_photo_url")
    private String facePhotoUrl;

    /**
     * 职业
     */
    @TableField("profession")
    private String profession;

    /**
     * 公司名称
     */
    @TableField("company")
    private String company;

    /**
     * 工作证明照片
     */
    @TableField("work_certificate_url")
    private String workCertificateUrl;

    /**
     * 专业证书信息(JSON格式)
     */
    @TableField("professional_certs")
    private String professionalCerts;

    /**
     * 教育信息(JSON格式)
     */
    @TableField("education_info")
    private String educationInfo;

    /**
     * 工作经历(JSON格式)
     */
    @TableField("work_experience")
    private String workExperience;

    /**
     * 认证状态: 0-待审核, 1-通过, 2-拒绝, 3-已停用
     */
    @TableField("verification_status")
    private Integer verificationStatus;

    /**
     * 拒绝原因
     */
    @TableField("reject_reason")
    private String rejectReason;

    /**
     * 审核员ID
     */
    @TableField("reviewer_id")
    private Long reviewerId;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("review_time")
    private LocalDateTime reviewTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    // 枚举定义
    public enum VerificationType {
        REAL_NAME(1, "实名认证"),
        PROFESSIONAL(2, "职业认证"),
        AVATAR(3, "头像认证"),
        FACE(4, "人脸认证"),
        BACKGROUND(5, "背景调查");

        private final Integer code;
        private final String desc;

        VerificationType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    public enum VerificationStatus {
        PENDING(0, "待审核"),
        APPROVED(1, "通过"),
        REJECTED(2, "拒绝"),
        SUSPENDED(3, "已停用");

        private final Integer code;
        private final String desc;

        VerificationStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 是否已通过认证
     */
    public boolean isApproved() {
        return VerificationStatus.APPROVED.getCode().equals(verificationStatus);
    }

    /**
     * 是否待审核
     */
    public boolean isPending() {
        return VerificationStatus.PENDING.getCode().equals(verificationStatus);
    }

    /**
     * 是否被拒绝
     */
    public boolean isRejected() {
        return VerificationStatus.REJECTED.getCode().equals(verificationStatus);
    }
} 