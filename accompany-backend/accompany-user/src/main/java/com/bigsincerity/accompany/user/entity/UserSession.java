package com.bigsincerity.accompany.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户会话实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user_sessions")
public class UserSession {

    /**
     * 会话ID（使用雪花算法生成）
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 会话标识
     */
    @TableField("session_id")
    private String sessionId;

    /**
     * 设备ID
     */
    @TableField("device_id")
    private String deviceId;

    /**
     * 访问令牌
     */
    @TableField("access_token")
    private String accessToken;

    /**
     * 刷新令牌
     */
    @TableField("refresh_token")
    private String refreshToken;

    /**
     * IP地址
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 用户代理
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 过期时间
     */
    @TableField("expires_time")
    private LocalDateTime expiresTime;

    /**
     * 最后活跃时间
     */
    @TableField("last_active_time")
    private LocalDateTime lastActiveTime;

    /**
     * 状态: 0-失效, 1-有效
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    // 枚举定义
    public enum Status {
        INVALID(0, "失效"),
        VALID(1, "有效");

        private final Integer code;
        private final String desc;

        Status(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 检查会话是否有效
     */
    public boolean isValid() {
        return status == Status.VALID.getCode() && 
               expiresTime != null && 
               expiresTime.isAfter(LocalDateTime.now());
    }

    /**
     * 刷新最后活跃时间
     */
    public void refreshLastActiveTime() {
        this.lastActiveTime = LocalDateTime.now();
    }

    /**
     * 设置为失效状态
     */
    public void invalidate() {
        this.status = Status.INVALID.getCode();
    }
} 