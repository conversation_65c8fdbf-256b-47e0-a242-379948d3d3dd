package com.bigsincerity.accompany.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * API调用日志实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("api_logs")
public class ApiLog {

    /**
     * 日志ID（使用雪花算法生成）
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 设备ID
     */
    @TableField("device_id")
    private String deviceId;

    /**
     * API路径
     */
    @TableField("api_path")
    private String apiPath;

    /**
     * API端点
     */
    @TableField("api_endpoint")
    private String apiEndpoint;

    /**
     * HTTP方法
     */
    @TableField("http_method")
    private String httpMethod;

    /**
     * 请求参数
     */
    @TableField("request_params")
    private String requestParams;

    /**
     * 请求体
     */
    @TableField("request_body")
    private String requestBody;

    /**
     * 响应状态码
     */
    @TableField("response_code")
    private Integer responseCode;

    /**
     * 响应体
     */
    @TableField("response_body")
    private String responseBody;

    /**
     * 响应时间(毫秒)
     */
    @TableField("response_time_ms")
    private Integer responseTimeMs;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * IP地址
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 用户代理
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 执行时间(毫秒)
     */
    @TableField("execution_time")
    private Integer executionTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    // 枚举定义
    public enum HttpMethod {
        GET("GET", "查询"),
        POST("POST", "新增"),
        PUT("PUT", "修改"),
        DELETE("DELETE", "删除"),
        PATCH("PATCH", "部分修改"),
        HEAD("HEAD", "头部信息"),
        OPTIONS("OPTIONS", "选项");

        private final String code;
        private final String desc;

        HttpMethod(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 是否请求成功（2xx状态码）
     */
    public boolean isSuccessResponse() {
        return responseCode != null && responseCode >= 200 && responseCode < 300;
    }

    /**
     * 是否客户端错误（4xx状态码）
     */
    public boolean isClientError() {
        return responseCode != null && responseCode >= 400 && responseCode < 500;
    }

    /**
     * 是否服务器错误（5xx状态码）
     */
    public boolean isServerError() {
        return responseCode != null && responseCode >= 500 && responseCode < 600;
    }

    /**
     * 是否慢请求（超过1秒）
     */
    public boolean isSlowRequest() {
        return responseTimeMs != null && responseTimeMs > 1000;
    }

    /**
     * 是否超慢请求（超过5秒）
     */
    public boolean isVerySlowRequest() {
        return responseTimeMs != null && responseTimeMs > 5000;
    }

    /**
     * 获取HTTP方法描述
     */
    public String getHttpMethodDesc() {
        for (HttpMethod method : HttpMethod.values()) {
            if (method.getCode().equals(httpMethod)) {
                return method.getDesc();
            }
        }
        return "未知";
    }

    /**
     * 获取响应状态描述
     */
    public String getResponseStatusDesc() {
        if (responseCode == null) {
            return "无响应";
        }
        
        if (responseCode >= 200 && responseCode < 300) {
            return "成功";
        } else if (responseCode >= 300 && responseCode < 400) {
            return "重定向";
        } else if (responseCode >= 400 && responseCode < 500) {
            return "客户端错误";
        } else if (responseCode >= 500 && responseCode < 600) {
            return "服务器错误";
        } else {
            return "未知状态";
        }
    }

    /**
     * 获取性能等级
     */
    public String getPerformanceLevel() {
        if (responseTimeMs == null) {
            return "未知";
        }
        
        if (responseTimeMs < 100) {
            return "极快";
        } else if (responseTimeMs < 500) {
            return "快";
        } else if (responseTimeMs < 1000) {
            return "正常";
        } else if (responseTimeMs < 3000) {
            return "慢";
        } else {
            return "极慢";
        }
    }
} 