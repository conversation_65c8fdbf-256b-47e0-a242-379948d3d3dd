package com.bigsincerity.accompany.user.service;

import com.bigsincerity.accompany.user.entity.SmsVerificationCode;

/**
 * 短信服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SmsService {

    /**
     * 发送短信验证码
     * 
     * @param phone 手机号
     * @param codeType 验证码类型
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     * @return 是否发送成功
     */
    boolean sendVerificationCode(String phone, Integer codeType, String ipAddress, String userAgent);

    /**
     * 验证短信验证码
     * 
     * @param phone 手机号
     * @param code 验证码
     * @param codeType 验证码类型
     * @return 是否验证成功
     */
    boolean verifyCode(String phone, String code, Integer codeType);

    /**
     * 获取有效的验证码
     * 
     * @param phone 手机号
     * @param codeType 验证码类型
     * @return 验证码信息
     */
    SmsVerificationCode getValidCode(String phone, Integer codeType);

    /**
     * 检查发送频率限制
     * 
     * @param phone 手机号
     * @param codeType 验证码类型
     * @return 是否允许发送
     */
    boolean checkSendLimit(String phone, Integer codeType);

    /**
     * 生成验证码
     * 
     * @return 6位数字验证码
     */
    String generateCode();

    /**
     * 标记验证码为已使用
     * 
     * @param codeId 验证码ID
     */
    void markCodeAsUsed(Long codeId);

    // 简化的验证方法，供UserService使用
    boolean verifyCode(String phone, String code, String type);

    // 简化的发送方法，供Controller使用
    void sendVerificationCode(String phone, String type);
} 