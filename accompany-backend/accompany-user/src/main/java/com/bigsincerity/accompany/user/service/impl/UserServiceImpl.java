package com.bigsincerity.accompany.user.service.impl;

import com.bigsincerity.accompany.common.constants.CommonConstants;
import com.bigsincerity.accompany.common.enums.SmsCodeType;
import com.bigsincerity.accompany.common.enums.UserStatus;
import com.bigsincerity.accompany.common.enums.UserType;
import com.bigsincerity.accompany.common.id.IdGeneratorUtil;
import com.bigsincerity.accompany.user.dto.LoginRequest;
import com.bigsincerity.accompany.user.dto.RegisterRequest;
import com.bigsincerity.accompany.user.entity.User;
import com.bigsincerity.accompany.user.mapper.UserMapper;
import com.bigsincerity.accompany.user.service.UserService;
import com.bigsincerity.accompany.user.service.SmsService;
import com.bigsincerity.accompany.user.util.JwtUtil;
import com.bigsincerity.accompany.user.util.PasswordUtil;
import com.bigsincerity.accompany.user.vo.LoginResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.security.SecureRandom;
import java.time.LocalDateTime;

/**
 * 用户服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserMapper userMapper;
    private final SmsService smsService;
    private final IdGeneratorUtil idGeneratorUtil;
    private final JwtUtil jwtUtil;

    @Override
    @Transactional
    public void register(RegisterRequest request, String clientIp) {
        log.info("开始用户注册流程: {}", request.getUserName());

        // 1. 验证用户名、手机号、邮箱是否已存在
        validateUserInfo(request);

        // 2. 验证密码强度（额外验证）
        validatePasswordStrength(request.getPassword());

        // 3. 验证短信验证码
        if (!smsService.verifyCode(request.getPhone(), request.getVerificationCode(), SmsCodeType.REGISTER.getCode())) {
            throw new RuntimeException(CommonConstants.ErrorMessage.VERIFICATION_CODE_ERROR);
        }

        // 4. 创建用户对象
        User user = createUser(request, clientIp);

        // 5. 保存用户信息
        int result = userMapper.insert(user);
        if (result <= 0) {
            throw new RuntimeException("用户注册失败");
        }

        // 6. 处理推荐奖励（如果有推荐人）
        handleReferralReward(user);

        // 7. 记录注册日志
        recordRegistrationLog(user, clientIp, request.getRegisterSource());

        // 8. 发送欢迎信息（异步）
        sendWelcomeMessage(user);

        log.info("用户注册成功: userId={}, username={}", user.getId(), user.getUsername());
    }

    @Override
    public LoginResponse login(LoginRequest request, String clientIp, String userAgent) {
        log.info("开始用户登录流程: {}", request.getAccount());

        // 1. 根据账户查找用户
        User user = findUserByAccount(request.getAccount());
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 2. 验证用户状态
        validateUserStatus(user);

        // 3. 验证登录凭证
        validateLoginCredentials(request, user);

        // 4. 生成token
        String accessToken = jwtUtil.generateAccessToken(user.getId(), user.getUsername());
        String refreshToken = jwtUtil.generateRefreshToken(user.getId());

        // 5. 更新最后登录信息
        updateLastLoginInfo(user.getId(), clientIp, userAgent);

        // 6. 记录登录日志
        recordLoginLog(user.getId(), clientIp, userAgent, true);

        log.info("用户登录成功: userId={}, username={}", user.getId(), user.getUsername());

        return LoginResponse.builder()
                .userId(user.getId())
                .username(user.getUsername())
                .nickname(user.getNickname())
                .avatarUrl(user.getAvatarUrl())
                .phone(user.getPhone())
                .email(user.getEmail())
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .expiresIn(jwtUtil.getAccessTokenExpiration())
                .build();
    }

    @Override
    public boolean isUsernameAvailable(String username) {
        return userMapper.countByUserName(username) == 0;
    }

    @Override
    public boolean isPhoneAvailable(String phone) {
        return userMapper.countByPhone(phone) == 0;
    }

    @Override
    public boolean isEmailAvailable(String email) {
        return userMapper.countByEmail(email) == 0;
    }

    @Override
    public LoginResponse refreshToken(String refreshToken) {
        // 验证refreshToken
        if (!jwtUtil.validateRefreshToken(refreshToken)) {
            throw new RuntimeException("刷新令牌无效或已过期");
        }

        // 从token中获取用户ID
        Long userId = jwtUtil.getUserIdFromRefreshToken(refreshToken);
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 生成新的token
        String newAccessToken = jwtUtil.generateAccessToken(user.getId(), user.getUsername());
        String newRefreshToken = jwtUtil.generateRefreshToken(user.getId());

        return LoginResponse.builder()
                .userId(user.getId())
                .username(user.getUsername())
                .nickname(user.getNickname())
                .avatarUrl(user.getAvatarUrl())
                .phone(user.getPhone())
                .email(user.getEmail())
                .accessToken(newAccessToken)
                .refreshToken(newRefreshToken)
                .expiresIn(jwtUtil.getAccessTokenExpiration())
                .build();
    }

    @Override
    public void logout(String token) {
        // 提取实际的token（去掉Bearer前缀）
        if (token.startsWith("Bearer ")) {
            token = token.substring(7);
        }

        // 将token加入黑名单（这里可以使用Redis存储）
        // 简单实现：验证token是否有效即可
        if (!jwtUtil.validateAccessToken(token)) {
            throw new RuntimeException("无效的访问令牌");
        }

        log.info("用户登出成功");
    }

    /**
     * 验证用户信息是否已存在
     */
    private void validateUserInfo(RegisterRequest request) {
        if (!isUsernameAvailable(request.getUserName())) {
            throw new RuntimeException(CommonConstants.ErrorMessage.USERNAME_EXISTS);
        }
        if (!isPhoneAvailable(request.getPhone())) {
            throw new RuntimeException(CommonConstants.ErrorMessage.PHONE_EXISTS);
        }
        if (!isEmailAvailable(request.getEmail())) {
            throw new RuntimeException(CommonConstants.ErrorMessage.EMAIL_EXISTS);
        }
    }

    /**
     * 验证密码强度
     */
    private void validatePasswordStrength(String password) {
        if (!PasswordUtil.isStrongPassword(password)) {
            throw new RuntimeException("密码强度不够，请包含大小写字母、数字");
        }
    }

    /**
     * 处理推荐奖励
     */
    private void handleReferralReward(User user) {
        if (user.getReferredBy() != null) {
            try {
                // 给推荐人增加积分
                User referrer = userMapper.selectById(user.getReferredBy());
                if (referrer != null) {
                    // 推荐奖励：推荐人+10积分，新用户+5积分
                    updateUserPoints(referrer.getId(), CommonConstants.Business.REFERRAL_REWARD_POINTS, "推荐新用户");
                    updateUserPoints(user.getId(), CommonConstants.Business.NEW_USER_REWARD_POINTS, "通过推荐注册");
                    log.info("推荐奖励发放成功: 推荐人={}, 新用户={}", referrer.getUsername(), user.getUsername());
                }
            } catch (Exception e) {
                log.error("推荐奖励发放失败: userId={}", user.getId(), e);
                // 不影响注册流程，继续执行
            }
        }
    }

    /**
     * 更新用户积分
     */
    private void updateUserPoints(Long userId, Integer points, String reason) {
        User updateUser = new User();
        updateUser.setId(userId);
        updateUser.setTotalPoints(points); // 这里应该是增加，实际项目中需要先查询当前积分
        updateUser.setUpdatedTime(LocalDateTime.now());
        userMapper.updateById(updateUser);
        log.info("用户积分更新: userId={}, points=+{}, reason={}", userId, points, reason);
    }

    /**
     * 记录注册日志
     */
    private void recordRegistrationLog(User user, String clientIp, String registerSource) {
        try {
            // 这里可以记录到专门的注册日志表
            log.info("记录注册日志: userId={}, username={}, ip={}, source={}", 
                    user.getId(), user.getUsername(), clientIp, registerSource);
            
            // 实际项目中可以创建 RegistrationLog 实体和对应的 Mapper
            // registrationLogMapper.insert(registrationLog);
        } catch (Exception e) {
            log.error("记录注册日志失败: userId={}", user.getId(), e);
        }
    }

    /**
     * 发送欢迎信息
     */
    private void sendWelcomeMessage(User user) {
        try {
            // 异步发送欢迎短信
            String welcomeMessage = String.format(CommonConstants.MessageTemplate.WELCOME_MESSAGE_TEMPLATE,
                    CommonConstants.System.SYSTEM_NAME, user.getUsername(), user.getReferralCode());
            log.info("发送欢迎信息: userId={}, message={}", user.getId(), welcomeMessage);
            
            // 实际项目中可以集成短信服务
            // smsService.sendWelcomeMessage(user.getPhone(), welcomeMessage);
        } catch (Exception e) {
            log.error("发送欢迎信息失败: userId={}", user.getId(), e);
        }
    }

    /**
     * 创建用户对象
     */
    private User createUser(RegisterRequest request, String clientIp) {
        User user = new User();
        
        // 基本信息
        user.setId(idGeneratorUtil.nextUserId());
        user.setUsername(request.getUserName());
        user.setNickname(request.getUserName()); // 默认昵称为用户名
        user.setPhone(request.getPhone());
        user.setEmail(request.getEmail());
        
        // 密码加密
        PasswordUtil.EncodeResult encodeResult = PasswordUtil.encodeWithDetails(request.getPassword());
        user.setPasswordHash(encodeResult.getEncodedPassword());
        user.setSalt(encodeResult.getSalt()); // 设置盐值
        
        // 注册信息
        user.setRegistrationIp(clientIp);
        user.setStatus(UserStatus.ACTIVE.getCode()); // 正常状态
        
        // 用户类型处理
        Integer userType = UserType.getCodeByType(request.getUserType());
        user.setUserType(userType);
        
        user.setIsVerified(false); // 未认证
        
        // 积分和等级初始化
        user.setTotalPoints(CommonConstants.Business.DEFAULT_INITIAL_POINTS);
        user.setCreditScore(CommonConstants.Business.DEFAULT_INITIAL_CREDIT_SCORE); // 初始信用分100
        user.setTotalOrders(0);
        user.setCompletionRate(java.math.BigDecimal.ZERO);
        user.setLoginCount(0);
        
        // 时间信息
        LocalDateTime now = LocalDateTime.now();
        user.setCreatedTime(now);
        user.setUpdatedTime(now);
        
        // 处理邀请码
        if (StringUtils.hasText(request.getInviteCode())) {
            // 查找推荐人
            User referrer = userMapper.selectByInviteCode(request.getInviteCode());
            if (referrer != null) {
                user.setReferredBy(referrer.getId());
                log.info("用户 {} 被 {} 推荐注册", request.getUserName(), referrer.getUsername());
            } else {
                log.warn("邀请码无效: {}", request.getInviteCode());
            }
        }
        
        // 生成个人推荐码（使用用户ID的后6位 + 随机字符）
        user.setReferralCode(generateReferralCode(user.getId()));

        return user;
    }
    

    
    /**
     * 生成推荐码
     */
    private String generateReferralCode(Long userId) {
        // 使用用户ID的后6位 + 2位随机字符
        String userIdSuffix = String.valueOf(userId).substring(Math.max(0, String.valueOf(userId).length() - 6));
        String randomSuffix = generateRandomString(2);
        return "AC" + userIdSuffix + randomSuffix;
    }
    
    /**
     * 生成随机字符串
     */
    private String generateRandomString(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        StringBuilder result = new StringBuilder();
        SecureRandom random = new SecureRandom();
        for (int i = 0; i < length; i++) {
            result.append(chars.charAt(random.nextInt(chars.length())));
        }
        return result.toString();
    }

    /**
     * 根据账户查找用户
     */
    private User findUserByAccount(String account) {
        // 判断账户类型并查询
        if (account.contains("@")) {
            return userMapper.selectByEmail(account);
        } else if (account.matches("^1[3-9]\\d{9}$")) {
            return userMapper.selectByPhone(account);
        } else {
            return userMapper.selectByUserName(account);
        }
    }

    /**
     * 验证用户状态
     */
    private void validateUserStatus(User user) {
        if (UserStatus.isDisabled(user.getStatus())) {
            throw new RuntimeException(CommonConstants.ErrorMessage.ACCOUNT_DISABLED);
        }
        if (UserStatus.isLocked(user.getStatus())) {
            throw new RuntimeException(CommonConstants.ErrorMessage.ACCOUNT_LOCKED);
        }
    }

    /**
     * 验证登录凭证
     */
    private void validateLoginCredentials(LoginRequest request, User user) {
        if (request.getLoginType() == 1) {
            // 密码登录
            if (!StringUtils.hasText(request.getPassword())) {
                throw new RuntimeException("密码不能为空");
            }
            if (!PasswordUtil.matches(request.getPassword(), user.getPasswordHash())) {
                throw new RuntimeException("密码错误");
            }
        } else if (request.getLoginType() == 2) {
            // 短信验证码登录
            if (!StringUtils.hasText(request.getVerificationCode())) {
                throw new RuntimeException("验证码不能为空");
            }
            if (!smsService.verifyCode(user.getPhone(), request.getVerificationCode(), SmsCodeType.LOGIN.getCode())) {
                throw new RuntimeException("验证码错误或已过期");
            }
        } else {
            throw new RuntimeException("不支持的登录类型");
        }
    }

    /**
     * 更新最后登录信息
     */
    private void updateLastLoginInfo(Long userId, String clientIp, String userAgent) {
        User updateUser = new User();
        updateUser.setId(userId);
        updateUser.setLastLoginTime(LocalDateTime.now());
        updateUser.setLastLoginIp(clientIp);
        updateUser.setUpdatedTime(LocalDateTime.now());
        userMapper.updateById(updateUser);
    }

    /**
     * 记录登录日志
     */
    private void recordLoginLog(Long userId, String clientIp, String userAgent, boolean success) {
        // 这里可以记录到登录日志表
        log.info("记录登录日志: userId={}, ip={}, userAgent={}, success={}", 
                userId, clientIp, userAgent, success);
    }
} 