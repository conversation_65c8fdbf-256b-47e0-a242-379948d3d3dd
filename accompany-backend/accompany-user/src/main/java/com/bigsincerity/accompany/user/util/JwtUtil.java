package com.bigsincerity.accompany.user.util;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * JWT工具类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class JwtUtil {

    /**
     * JWT密钥
     */
    @Value("${accompany.jwt.secret:accompany-secret-key-2024}")
    private String secret;

    /**
     * AccessToken过期时间(分钟)
     */
    @Value("${accompany.jwt.access-token-expiration:120}")
    private Integer accessTokenExpiration;

    /**
     * RefreshToken过期时间(天)
     */
    @Value("${accompany.jwt.refresh-token-expiration:7}")
    private Integer refreshTokenExpiration;

    /**
     * Token发行者
     */
    @Value("${accompany.jwt.issuer:accompany}")
    private String issuer;

    /**
     * 生成AccessToken
     */
    public String generateAccessToken(Long userId, String userName) {
        Date now = new Date();
        Date expiration = new Date(now.getTime() + accessTokenExpiration * 60 * 1000L);

        return JWT.create()
                .withIssuer(issuer)
                .withSubject(userId.toString())
                .withClaim("userId", userId)
                .withClaim("userName", userName)
                .withClaim("type", "access")
                .withIssuedAt(now)
                .withExpiresAt(expiration)
                .sign(Algorithm.HMAC256(secret));
    }

    /**
     * 生成RefreshToken
     */
    public String generateRefreshToken(Long userId) {
        Date now = new Date();
        Date expiration = new Date(now.getTime() + refreshTokenExpiration * 24 * 60 * 60 * 1000L);

        return JWT.create()
                .withIssuer(issuer)
                .withSubject(userId.toString())
                .withClaim("userId", userId)
                .withClaim("type", "refresh")
                .withIssuedAt(now)
                .withExpiresAt(expiration)
                .sign(Algorithm.HMAC256(secret));
    }

    /**
     * 验证AccessToken
     */
    public boolean validateAccessToken(String token) {
        try {
            JWTVerifier verifier = JWT.require(Algorithm.HMAC256(secret))
                    .withIssuer(issuer)
                    .withClaim("type", "access")
                    .build();
            verifier.verify(token);
            return true;
        } catch (JWTVerificationException e) {
            log.warn("AccessToken验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 验证RefreshToken
     */
    public boolean validateRefreshToken(String token) {
        try {
            JWTVerifier verifier = JWT.require(Algorithm.HMAC256(secret))
                    .withIssuer(issuer)
                    .withClaim("type", "refresh")
                    .build();
            verifier.verify(token);
            return true;
        } catch (JWTVerificationException e) {
            log.warn("RefreshToken验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 从AccessToken中获取用户ID
     */
    public Long getUserIdFromAccessToken(String token) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim("userId").asLong();
        } catch (Exception e) {
            log.error("解析AccessToken失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从RefreshToken中获取用户ID
     */
    public Long getUserIdFromRefreshToken(String token) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim("userId").asLong();
        } catch (Exception e) {
            log.error("解析RefreshToken失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从Token中获取用户名
     */
    public String getUserNameFromToken(String token) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim("userName").asString();
        } catch (Exception e) {
            log.error("解析Token中的用户名失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 检查Token是否过期
     */
    public boolean isTokenExpired(String token) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getExpiresAt().before(new Date());
        } catch (Exception e) {
            log.error("检查Token过期时间失败: {}", e.getMessage());
            return true;
        }
    }

    /**
     * 获取AccessToken过期时间(毫秒)
     */
    public Long getAccessTokenExpiration() {
        return accessTokenExpiration * 60 * 1000L;
    }

    /**
     * 获取RefreshToken过期时间(毫秒)
     */
    public Long getRefreshTokenExpiration() {
        return refreshTokenExpiration * 24 * 60 * 60 * 1000L;
    }
} 