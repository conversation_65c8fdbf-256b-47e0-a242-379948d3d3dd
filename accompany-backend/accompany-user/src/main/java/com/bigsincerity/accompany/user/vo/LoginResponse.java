package com.bigsincerity.accompany.user.vo;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 登录响应VO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LoginResponse {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像URL
     */
    private String avatarUrl;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 用户类型: 1-普通用户, 2-服务提供者, 3-管理员
     */
    private Integer userType;

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 刷新令牌
     */
    private String refreshToken;

    /**
     * 令牌过期时间(毫秒)
     */
    private Long expiresIn;

    /**
     * 令牌过期时间
     */
    private LocalDateTime expiresTime;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 是否首次登录
     */
    private Boolean firstLogin;

    /**
     * 登录时间
     */
    private LocalDateTime loginTime;

    /**
     * 登录IP
     */
    private String loginIp;

    /**
     * 用户状态信息
     */
    private UserStatusInfo statusInfo;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class UserStatusInfo {
        /**
         * 是否需要完善资料
         */
        private Boolean needCompleteProfile;

        /**
         * 是否需要实名认证
         */
        private Boolean needRealNameVerification;

        /**
         * 登录次数
         */
        private Integer loginCount;

        /**
         * 最后登录时间
         */
        private LocalDateTime lastLoginTime;
    }
} 