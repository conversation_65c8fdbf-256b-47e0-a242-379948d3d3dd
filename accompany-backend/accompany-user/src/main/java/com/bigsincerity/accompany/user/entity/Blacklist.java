package com.bigsincerity.accompany.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 黑名单实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("blacklists")
public class Blacklist {

    /**
     * 黑名单ID（使用雪花算法生成）
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 目标类型: 1-用户ID, 2-手机号, 3-邮箱, 4-IP地址, 5-设备ID
     */
    @TableField("target_type")
    private Integer targetType;

    /**
     * 目标值
     */
    @TableField("target_value")
    private String targetValue;

    /**
     * 被拉黑用户ID（当target_type=1时使用）
     */
    @TableField("black_user_id")
    private Long blackUserId;

    /**
     * 拉黑原因
     */
    @TableField("reason")
    private String reason;

    /**
     * 操作员ID
     */
    @TableField("operator_id")
    private Long operatorId;

    /**
     * 状态: 0-失效, 1-生效
     */
    @TableField("status")
    private Integer status;

    /**
     * 过期时间(为空表示永久)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("expires_time")
    private LocalDateTime expiresTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    // 枚举定义
    public enum TargetType {
        USER_ID(1, "用户ID"),
        PHONE(2, "手机号"),
        EMAIL(3, "邮箱"),
        IP_ADDRESS(4, "IP地址"),
        DEVICE_ID(5, "设备ID");

        private final Integer code;
        private final String desc;

        TargetType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    public enum Status {
        INVALID(0, "失效"),
        VALID(1, "生效");

        private final Integer code;
        private final String desc;

        Status(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 是否生效
     */
    public boolean isValid() {
        // 检查状态
        if (!Status.VALID.getCode().equals(status)) {
            return false;
        }
        
        // 检查过期时间
        if (expiresTime != null && expiresTime.isBefore(LocalDateTime.now())) {
            return false;
        }
        
        return true;
    }

    /**
     * 是否永久有效
     */
    public boolean isPermanent() {
        return expiresTime == null;
    }

    /**
     * 是否已过期
     */
    public boolean isExpired() {
        return expiresTime != null && expiresTime.isBefore(LocalDateTime.now());
    }

    /**
     * 是否为用户黑名单
     */
    public boolean isUserBlacklist() {
        return TargetType.USER_ID.getCode().equals(targetType);
    }

    /**
     * 获取目标类型描述
     */
    public String getTargetTypeDesc() {
        for (TargetType type : TargetType.values()) {
            if (type.getCode().equals(targetType)) {
                return type.getDesc();
            }
        }
        return "未知";
    }
} 