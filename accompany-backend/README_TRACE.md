# 追踪系统使用指南

## 📖 概述

本项目实现了完整的请求追踪系统，为每个HTTP请求自动生成唯一的追踪ID和请求ID，并将追踪信息记录在日志中，便于问题排查和系统监控。

## 🏗️ 系统架构

### 核心组件

1. **TraceUtils** - 追踪ID工具类
2. **TraceInterceptor** - 追踪拦截器
3. **Result统一响应** - 自动包含追踪ID
4. **MDC日志配置** - 日志中显示追踪信息

### 追踪ID格式

- **TraceId**: 32位UUID（去掉连字符）`a1b2c3d4e5f6789012345678901234567`
- **RequestId**: 8位UUID前缀 `a1b2c3d4`

## 🚀 自动功能

### 1. 请求追踪

每个HTTP请求会自动：
- 生成或继承追踪ID
- 生成新的请求ID
- 在响应头中返回追踪信息
- 在日志中记录完整的请求信息

### 2. 响应追踪

API响应会自动包含：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": { ... },
  "success": true,
  "timestamp": 1735123456789,
  "traceId": "a1b2c3d4e5f6789012345678901234567"
}
```

### 3. 日志追踪

所有日志都会包含追踪信息：
```
2024-12-25 10:30:56.789 [http-nio-8081-exec-1] INFO [a1b2c3d4e5f6789012345678901234567] [a1b2c3d4] c.b.a.user.controller.AuthController - 用户注册请求: testuser
```

## 📝 使用方法

### 1. 获取当前追踪信息

```java
// 获取追踪ID
String traceId = TraceUtils.getTraceId();

// 获取请求ID
String requestId = TraceUtils.getRequestId();

// 获取完整追踪信息
TraceUtils.TraceInfo traceInfo = TraceUtils.getTraceInfo();
```

### 2. 手动设置追踪ID

```java
// 设置自定义追踪ID（通常在微服务调用时使用）
TraceUtils.setTraceId("custom-trace-id");

// 初始化新的追踪信息
TraceUtils.initTrace();

// 使用指定追踪ID初始化
TraceUtils.initTrace("custom-trace-id");
```

### 3. 清理追踪信息

```java
// 清除MDC中的追踪信息（拦截器会自动处理）
TraceUtils.clearTrace();
```

## 🔧 配置说明

### 1. HTTP头配置

- **请求头**: `X-Trace-Id` (可选，如果提供则继承)
- **响应头**: `X-Trace-Id`, `X-Request-Id`

### 2. 日志配置

logback-spring.xml中的追踪配置：
```xml
<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-}] [%X{requestId:-}] %logger{36} - %msg%n</pattern>
```

### 3. 拦截器配置

自动拦截所有请求，排除：
- 静态资源 (`/static/**`)
- 网站图标 (`/favicon.ico`)
- 错误页面 (`/error`)
- 监控端点 (`/actuator/**`)

## 🎯 使用场景

### 1. 问题排查

当用户报告问题时，可以通过追踪ID快速定位相关日志：
```bash
grep "a1b2c3d4e5f6789012345678901234567" logs/accompany-user.log
```

### 2. 微服务调用链

在微服务之间传递追踪ID，实现分布式追踪：
```java
// 调用其他服务时传递追踪ID
HttpHeaders headers = new HttpHeaders();
headers.set("X-Trace-Id", TraceUtils.getTraceId());
```

### 3. 性能监控

通过追踪ID关联请求的开始和结束日志：
```
2024-12-25 10:30:56.789 [http-nio-8081-exec-1] INFO [a1b2c3d4e5f6789012345678901234567] [a1b2c3d4] c.b.a.common.interceptor.TraceInterceptor - 开始处理请求: POST /api/auth/register
2024-12-25 10:30:57.123 [http-nio-8081-exec-1] INFO [a1b2c3d4e5f6789012345678901234567] [a1b2c3d4] c.b.a.common.interceptor.TraceInterceptor - 完成处理请求: POST /api/auth/register, status=200
```

### 4. 错误关联

所有错误日志都包含追踪信息，便于定位问题：
```java
try {
    // 业务逻辑
} catch (Exception e) {
    log.error("业务处理失败", e); // 自动包含traceId和requestId
}
```

## 📊 监控和分析

### 1. 日志聚合

使用ELK Stack或其他日志聚合工具，可以：
- 按追踪ID搜索完整的请求链路
- 统计请求响应时间
- 分析错误率和异常模式

### 2. APM集成

可以与APM工具（如SkyWalking、Zipkin）集成：
- 将追踪ID传递给APM系统
- 实现更完整的分布式追踪

### 3. 业务监控

在业务代码中记录关键操作：
```java
log.info("用户注册成功: userId={}, traceId={}", userId, TraceUtils.getTraceId());
```

## ⚠️ 注意事项

### 1. 内存管理

- 拦截器会自动清理MDC，避免内存泄漏
- 在异步处理中需要手动传递追踪信息

### 2. 异步处理

在异步任务中需要手动传递追踪信息：
```java
String traceId = TraceUtils.getTraceId();
CompletableFuture.runAsync(() -> {
    TraceUtils.setTraceId(traceId);
    // 异步业务逻辑
    TraceUtils.clearTrace();
});
```

### 3. 性能影响

- 追踪系统对性能影响很小
- UUID生成和MDC操作都是轻量级的

## 🔍 故障排除

### 1. 追踪ID丢失

**问题**: 日志中没有追踪ID
**解决**: 检查拦截器是否正确配置，确保请求经过拦截器

### 2. 追踪ID不一致

**问题**: 同一请求中不同日志的追踪ID不同
**解决**: 避免在业务代码中手动修改追踪ID

### 3. 响应中没有追踪ID

**问题**: API响应中缺少traceId字段
**解决**: 检查Result类的getCurrentTraceId方法是否正常工作

## 💡 最佳实践

### 1. 日志记录

```java
// ✅ 推荐：使用统一的日志格式
log.info("用户操作: action={}, userId={}, result={}", action, userId, result);

// ❌ 不推荐：日志信息不明确
log.info("操作完成");
```

### 2. 错误处理

```java
// ✅ 推荐：记录详细的错误信息
try {
    // 业务逻辑
} catch (Exception e) {
    log.error("用户注册失败: username={}, reason={}", username, e.getMessage(), e);
    return Result.error("注册失败: " + e.getMessage());
}
```

### 3. 关键节点记录

```java
// 在关键业务节点记录日志
log.info("开始处理用户注册: username={}", username);
// ... 业务逻辑
log.info("用户注册成功: userId={}, username={}", userId, username);
```

## 📈 扩展功能

### 1. 自定义追踪信息

可以扩展TraceUtils添加更多追踪信息：
- 用户ID
- 客户端IP
- 设备信息

### 2. 链路追踪

集成分布式追踪系统：
- OpenTracing
- Spring Cloud Sleuth
- SkyWalking

### 3. 监控告警

基于追踪信息设置监控告警：
- 错误率告警
- 响应时间告警
- 异常模式告警

---

**💡 提示**: 追踪系统已经自动配置，无需额外设置即可使用。所有HTTP请求都会自动包含追踪信息，日志中也会显示追踪ID。 