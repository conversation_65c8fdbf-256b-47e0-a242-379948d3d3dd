# 🌍 多语言架构设计方案

## 📋 **设计概述**

基于 **MongoDB + Redis缓存** 的多语言解决方案，支持动态语言切换、消息管理和高性能访问。

---

## 🏗️ **系统架构**

```mermaid
graph TB
    Client[客户端请求] --> Controller[控制器层]
    Controller --> I18nService[多语言服务]
    I18nService --> Cache{Redis缓存}
    Cache -->|缓存未命中| MongoDB[(MongoDB数据库)]
    Cache -->|缓存命中| Response[返回消息]
    MongoDB --> Cache
    Cache --> Response
    Response --> Controller
    Controller --> Client
    
    Admin[管理后台] --> I18nAdmin[多语言管理]
    I18nAdmin --> MongoDB
    I18nAdmin -->|刷新缓存| Cache
```

---

## 📊 **数据结构设计**

### 🗃️ **MongoDB 集合：i18n_messages**

```json
{
  "_id": "ObjectId",
  "messageKey": "user.not_found",           // 消息唯一键
  "category": "error",                      // 消息分类
  "module": "user",                         // 所属模块
  "translations": {                         // 多语言翻译
    "zh-CN": "用户不存在",
    "en-US": "User not found"
  },
  "defaultLanguage": "zh-CN",               // 默认语言
  "description": "用户不存在错误消息",        // 消息描述
  "enabled": true,                          // 是否启用
  "createdTime": "ISODate",                 // 创建时间
  "updatedTime": "ISODate",                 // 更新时间
  "createdBy": "admin",                     // 创建人
  "updatedBy": "admin"                      // 更新人
}
```

### 🏷️ **MongoDB 索引设计**

```javascript
// 唯一索引 - 消息键
db.i18n_messages.createIndex({ "messageKey": 1 }, { unique: true })

// 复合索引 - 模块查询
db.i18n_messages.createIndex({ "module": 1, "enabled": 1 })

// 复合索引 - 分类查询
db.i18n_messages.createIndex({ "category": 1, "enabled": 1 })

// 复合索引 - 更新时间查询
db.i18n_messages.createIndex({ "updatedTime": -1 })
```

---

## 🎯 **核心组件**

### 📱 **1. 实体类 (I18nMessage)**
- **完整字段定义** - 包含所有必要字段
- **便捷方法** - getText(), setChineseText(), setEnglishText()
- **降级策略** - 自动降级到默认语言或中文

### 🔧 **2. 服务接口 (I18nService)**
- **基础方法** - getMessage(), saveMessage(), deleteMessage()
- **便捷方法** - getChineseMessage(), getEnglishMessage()
- **缓存管理** - refreshCache(), warmUpCache()
- **批量操作** - saveMessages(), getMessagesByModule()

### 🌐 **3. 语言枚举 (Language)**
- **标准语言代码** - 支持 ISO 639-1 + ISO 3166-1
- **智能解析** - 自动处理 zh-CN, zh, ZH 等变体
- **启用控制** - 可配置启用/禁用语言

### 📋 **4. 常量定义 (I18nConstants)**
- **消息键管理** - 预定义常用消息键
- **分类管理** - error, success, validation 等
- **缓存配置** - 缓存键模板和过期时间

---

## 🚀 **使用方式**

### 📝 **1. 基础用法**

```java
@Service
public class UserService {
    
    @Autowired
    private I18nService i18nService;
    
    public void validateUser(String username, String language) {
        if (StringUtils.isEmpty(username)) {
            String message = i18nService.getMessage(
                I18nConstants.MessageKey.VALIDATION_REQUIRED, 
                language, 
                "用户名"
            );
            throw new ValidationException(message);
        }
    }
}
```

### 🎮 **2. 控制器集成**

```java
@RestController
public class AuthController {
    
    @Autowired
    private I18nService i18nService;
    
    @PostMapping("/login")
    public Result<String> login(@RequestHeader("Accept-Language") String language) {
        try {
            // 业务逻辑...
            return Result.success(
                i18nService.getMessage(I18nConstants.MessageKey.LOGIN_SUCCESS, language)
            );
        } catch (Exception e) {
            return Result.error(
                i18nService.getMessage(I18nConstants.MessageKey.LOGIN_FAILED, language)
            );
        }
    }
}
```

### 🔄 **3. 语言检测**

```java
// 从 HTTP 头检测语言
private String detectLanguage(HttpServletRequest request) {
    String acceptLanguage = request.getHeader("Accept-Language");
    return Language.fromCode(acceptLanguage).getCode();
}

// 从请求参数检测语言
private String getLanguageFromParam(String lang) {
    return Language.fromCode(lang).getCode();
}
```

---

## ⚡ **性能优化**

### 🏃‍♂️ **1. 多级缓存策略**

```java
// L1: 应用内存缓存 (ConcurrentHashMap)
// L2: Redis 缓存 (24小时)
// L3: MongoDB 数据库

public String getMessage(String messageKey, String language) {
    // 1. 检查内存缓存
    String cached = memoryCache.get(buildCacheKey(messageKey, language));
    if (cached != null) return cached;
    
    // 2. 检查 Redis 缓存
    cached = redisTemplate.opsForValue().get(buildCacheKey(messageKey, language));
    if (cached != null) {
        memoryCache.put(buildCacheKey(messageKey, language), cached);
        return cached;
    }
    
    // 3. 查询数据库
    I18nMessage message = mongoTemplate.findOne(
        Query.query(Criteria.where("messageKey").is(messageKey)), 
        I18nMessage.class
    );
    
    if (message != null) {
        String text = message.getText(language);
        // 更新缓存
        redisTemplate.opsForValue().set(buildCacheKey(messageKey, language), text, 24, TimeUnit.HOURS);
        memoryCache.put(buildCacheKey(messageKey, language), text);
        return text;
    }
    
    return messageKey; // 降级返回
}
```

### 📈 **2. 缓存预热**

```java
@PostConstruct
public void warmUpCache() {
    // 预加载常用消息
    List<I18nMessage> commonMessages = mongoTemplate.find(
        Query.query(Criteria.where("category").in("error", "success", "validation")), 
        I18nMessage.class
    );
    
    // 批量加载到缓存
    for (I18nMessage message : commonMessages) {
        for (String language : Language.getEnabledLanguageCodes()) {
            String text = message.getText(language);
            String cacheKey = buildCacheKey(message.getMessageKey(), language);
            redisTemplate.opsForValue().set(cacheKey, text, 24, TimeUnit.HOURS);
        }
    }
}
```

---

## 🛠️ **管理工具**

### 📊 **1. 多语言管理后台**

```java
@RestController
@RequestMapping("/admin/i18n")
public class I18nAdminController {
    
    // 获取所有消息
    @GetMapping("/messages")
    public PageResult<I18nMessage> getMessages(@RequestParam int page, @RequestParam int size) {
        // 分页查询逻辑
    }
    
    // 创建/更新消息
    @PostMapping("/messages")
    public Result<I18nMessage> saveMessage(@RequestBody I18nMessage message) {
        // 保存逻辑 + 缓存刷新
    }
    
    // 批量导入
    @PostMapping("/messages/import")
    public Result<String> importMessages(@RequestParam MultipartFile file) {
        // Excel/JSON 导入逻辑
    }
    
    // 刷新缓存
    @PostMapping("/cache/refresh")
    public Result<String> refreshCache() {
        i18nService.refreshCache();
        return Result.success("缓存刷新成功");
    }
}
```

### 📁 **2. 数据初始化**

```java
@Component
public class I18nDataInitializer {
    
    @PostConstruct
    public void initializeData() {
        // 检查是否已初始化
        if (mongoTemplate.count(new Query(), I18nMessage.class) > 0) {
            return;
        }
        
        // 从 JSON 文件加载初始数据
        Resource resource = new ClassPathResource("data/i18n-init-data.json");
        List<I18nMessage> messages = objectMapper.readValue(
            resource.getInputStream(), 
            new TypeReference<List<I18nMessage>>() {}
        );
        
        // 批量插入
        mongoTemplate.insertAll(messages);
        
        // 预热缓存
        i18nService.warmUpCache();
    }
}
```

---

## 🌟 **高级特性**

### 🎨 **1. 参数化消息**

```java
// 消息模板："{0}长度应在{1}-{2}个字符之间"
// 使用示例：
String message = i18nService.getMessage(
    "validation.length", 
    "zh-CN", 
    "用户名", 2, 20
);
// 结果："用户名长度应在2-20个字符之间"
```

### 🔄 **2. 降级策略**

```java
public String getText(String language) {
    // 1. 优先返回请求的语言
    String text = translations.get(language);
    if (text != null) return text;
    
    // 2. 其次返回默认语言
    if (defaultLanguage != null) {
        text = translations.get(defaultLanguage);
        if (text != null) return text;
    }
    
    // 3. 最后返回中文
    text = translations.get("zh-CN");
    if (text != null) return text;
    
    // 4. 都没有则返回 messageKey
    return messageKey;
}
```

### 📱 **3. 前端集成**

```javascript
// 前端 API 调用示例
const fetchWithLanguage = (url, options = {}) => {
    const language = localStorage.getItem('language') || 'zh-CN';
    return fetch(url, {
        ...options,
        headers: {
            'Accept-Language': language,
            ...options.headers
        }
    });
};

// 使用示例
fetchWithLanguage('/api/auth/login', {
    method: 'POST',
    body: JSON.stringify(loginData)
}).then(response => {
    // 响应中的错误消息已经是对应语言
});
```

---

## 📈 **性能指标**

### 🎯 **目标性能**
- **内存缓存命中率** > 95%
- **Redis缓存命中率** > 90%
- **消息获取延迟** < 1ms (缓存命中)
- **数据库查询延迟** < 10ms (缓存未命中)

### 📊 **监控指标**
- 缓存命中率统计
- 响应时间分布
- 热点消息统计
- 语言使用分布

---

## ✅ **优势总结**

### 🚀 **技术优势**
- ✅ **高性能** - 多级缓存，毫秒级响应
- ✅ **可扩展** - MongoDB 水平扩展，支持海量数据
- ✅ **灵活性** - 动态增删语言，实时生效
- ✅ **易维护** - 统一管理，批量操作

### 💼 **业务优势**
- ✅ **国际化** - 支持全球多语言
- ✅ **用户体验** - 自动语言检测，降级策略
- ✅ **运营效率** - 后台管理，批量导入/导出
- ✅ **数据安全** - 版本控制，操作审计

**多语言架构设计完成！** 🎉 基于 MongoDB 的方案为您提供了高性能、可扩展的多语言解决方案。 