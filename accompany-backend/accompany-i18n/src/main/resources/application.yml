server:
  port: 8081
  servlet:
    context-path: /i18n

spring:
  application:
    name: accompany-i18n
  
  # MongoDB 配置
  data:
    mongodb:
      uri: mongodb://localhost:27017/accompany_i18n
      auto-index-creation: true
  
  # Redis 配置
  redis:
    host: localhost
    port: 6379
    password: 
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms
  
  # Jackson 配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    default-property-inclusion: non_null

# 多语言配置
i18n:
  # 默认语言
  default-language: zh-CN
  # 备用语言
  fallback-language: en-US
  # 缓存配置
  cache:
    # 是否启用缓存
    enabled: true
    # Redis缓存过期时间（小时）
    redis-expire-hours: 24
    # 内存缓存大小
    memory-cache-size: 1000
    # 内存缓存过期时间（分钟）
    memory-cache-expire-minutes: 30
  # 数据初始化
  data:
    # 是否启用数据初始化
    init-enabled: true
    # 初始化数据文件路径
    init-data-path: classpath:data/i18n-init-data.json

# 日志配置
logging:
  level:
    com.bigsincerity.accompany.i18n: DEBUG
    org.springframework.data.mongodb: DEBUG
    org.springframework.data.redis: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 管理端点
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always 