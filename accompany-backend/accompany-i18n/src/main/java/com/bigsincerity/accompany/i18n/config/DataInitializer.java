package com.bigsincerity.accompany.i18n.config;

import com.bigsincerity.accompany.i18n.entity.I18nMessage;
import com.bigsincerity.accompany.i18n.repository.I18nMessageRepository;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 数据初始化器
 * 启动时自动导入基础多语言数据
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DataInitializer implements CommandLineRunner {

    private final I18nMessageRepository i18nMessageRepository;
    private final ObjectMapper objectMapper;

    @Value("${i18n.data.init-enabled:true}")
    private boolean initEnabled;

    @Value("${i18n.data.init-data-path:classpath:data/i18n-init-data.json}")
    private String initDataPath;

    @Value("${i18n.default-language:zh-CN}")
    private String defaultLanguage;

    @Override
    public void run(String... args) throws Exception {
        if (!initEnabled) {
            log.info("Data initialization is disabled");
            return;
        }

        log.info("Starting i18n data initialization...");

        try {
            // 检查是否已有数据
            long existingCount = i18nMessageRepository.count();
            if (existingCount > 0) {
                log.info("Found {} existing i18n messages, skipping initialization", existingCount);
                return;
            }

            // 加载初始化数据
            List<Map<String, Object>> initData = loadInitData();
            if (initData == null || initData.isEmpty()) {
                log.warn("No initialization data found");
                return;
            }

            // 导入数据
            int importedCount = importData(initData);
            log.info("Successfully imported {} i18n messages", importedCount);

        } catch (Exception e) {
            log.error("Failed to initialize i18n data", e);
        }
    }

    /**
     * 加载初始化数据
     */
    private List<Map<String, Object>> loadInitData() {
        try {
            ClassPathResource resource = new ClassPathResource(initDataPath.replace("classpath:", ""));
            if (!resource.exists()) {
                log.warn("Init data file not found: {}", initDataPath);
                return null;
            }

            try (InputStream inputStream = resource.getInputStream()) {
                return objectMapper.readValue(inputStream, new TypeReference<List<Map<String, Object>>>() {});
            }

        } catch (Exception e) {
            log.error("Failed to load init data from: {}", initDataPath, e);
            return null;
        }
    }

    /**
     * 导入数据
     */
    private int importData(List<Map<String, Object>> initData) {
        int importedCount = 0;

        for (Map<String, Object> data : initData) {
            try {
                I18nMessage message = createMessageFromData(data);
                if (message != null) {
                    i18nMessageRepository.save(message);
                    importedCount++;
                }
            } catch (Exception e) {
                log.error("Failed to import message: {}", data.get("messageKey"), e);
            }
        }

        return importedCount;
    }

    /**
     * 从数据创建消息实体
     */
    private I18nMessage createMessageFromData(Map<String, Object> data) {
        try {
            String messageKey = (String) data.get("messageKey");
            String category = (String) data.get("category");
            String module = (String) data.get("module");
            String description = (String) data.get("description");

            @SuppressWarnings("unchecked")
            Map<String, String> translations = (Map<String, String>) data.get("translations");

            if (messageKey == null || translations == null) {
                log.warn("Invalid message data: missing messageKey or translations");
                return null;
            }

            return I18nMessage.builder()
                    .messageKey(messageKey)
                    .category(category != null ? category : "common")
                    .module(module != null ? module : "system")
                    .translations(translations)
                    .defaultLanguage(defaultLanguage)
                    .description(description)
                    .enabled(true)
                    .createdTime(LocalDateTime.now())
                    .createdBy("system")
                    .build();

        } catch (Exception e) {
            log.error("Failed to create message from data: {}", data, e);
            return null;
        }
    }
} 