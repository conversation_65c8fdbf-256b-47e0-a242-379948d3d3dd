package com.bigsincerity.accompany.i18n.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.index.Indexed;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 多语言消息实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "i18n_messages")
public class I18nMessage {

    /**
     * 主键ID
     */
    @Id
    private String id;

    /**
     * 消息键（唯一标识）
     */
    @Indexed(unique = true)
    private String messageKey;

    /**
     * 消息分类（如：error, success, validation等）
     */
    @Indexed
    private String category;

    /**
     * 模块名称（如：user, order, payment等）
     */
    @Indexed
    private String module;

    /**
     * 多语言内容
     * key: 语言代码（zh-CN, en-US等）
     * value: 对应语言的文本内容
     */
    private Map<String, String> translations;

    /**
     * 默认语言（当请求的语言不存在时使用）
     */
    private String defaultLanguage;

    /**
     * 消息描述（便于管理员理解）
     */
    private String description;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 创建时间
     */
    @CreatedDate
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @LastModifiedDate
    private LocalDateTime updatedTime;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 获取指定语言的文本
     */
    public String getText(String language) {
        if (translations == null) {
            return messageKey;
        }
        
        // 优先返回请求的语言
        String text = translations.get(language);
        if (text != null) {
            return text;
        }
        
        // 其次返回默认语言
        if (defaultLanguage != null) {
            text = translations.get(defaultLanguage);
            if (text != null) {
                return text;
            }
        }
        
        // 最后返回中文
        text = translations.get("zh-CN");
        if (text != null) {
            return text;
        }
        
        // 都没有则返回key
        return messageKey;
    }

    /**
     * 设置中文文本
     */
    public void setChineseText(String text) {
        if (translations == null) {
            translations = new java.util.HashMap<>();
        }
        translations.put("zh-CN", text);
    }

    /**
     * 设置英文文本
     */
    public void setEnglishText(String text) {
        if (translations == null) {
            translations = new java.util.HashMap<>();
        }
        translations.put("en-US", text);
    }

    /**
     * 获取中文文本
     */
    public String getChineseText() {
        return getText("zh-CN");
    }

    /**
     * 获取英文文本
     */
    public String getEnglishText() {
        return getText("en-US");
    }
} 