package com.bigsincerity.accompany.i18n;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.mongodb.config.EnableMongoAuditing;

/**
 * 多语言服务启动类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootApplication(
    scanBasePackages = {
        "com.bigsincerity.accompany.i18n",
        "com.bigsincerity.accompany.common"
    }
)
@EnableMongoAuditing
public class I18nApplication {

    public static void main(String[] args) {
        SpringApplication.run(I18nApplication.class, args);
    }
} 