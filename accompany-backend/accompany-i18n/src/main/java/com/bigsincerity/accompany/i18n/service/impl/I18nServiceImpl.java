package com.bigsincerity.accompany.i18n.service.impl;

import com.bigsincerity.accompany.common.service.I18nService;
import com.bigsincerity.accompany.i18n.entity.I18nMessage;
import com.bigsincerity.accompany.i18n.repository.I18nMessageRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 多语言服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class I18nServiceImpl implements I18nService {

    private final I18nMessageRepository i18nMessageRepository;
    private final RedisTemplate<String, String> redisTemplate;

    @Value("${i18n.default-language:zh-CN}")
    private String defaultLanguage;

    @Value("${i18n.fallback-language:en-US}")
    private String fallbackLanguage;

    @Value("${i18n.cache.enabled:true}")
    private boolean cacheEnabled;

    @Value("${i18n.cache.redis-expire-hours:24}")
    private int redisExpireHours;

    @Value("${i18n.cache.memory-cache-size:1000}")
    private int memoryCacheSize;

    @Value("${i18n.cache.memory-cache-expire-minutes:30}")
    private int memoryCacheExpireMinutes;

    // 内存缓存：key=messageKey:language, value=message
    private final Map<String, CacheEntry> memoryCache = new ConcurrentHashMap<>();

    /**
     * 缓存条目
     */
    private static class CacheEntry {
        private final String message;
        private final long expireTime;

        public CacheEntry(String message, long expireTime) {
            this.message = message;
            this.expireTime = expireTime;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() > expireTime;
        }
    }

    @Override
    public String getMessage(String messageKey, String language) {
        if (!StringUtils.hasText(messageKey)) {
            log.warn("Message key is empty");
            return messageKey;
        }

        if (!StringUtils.hasText(language)) {
            language = defaultLanguage;
        }

        // 1. 尝试内存缓存
        String cachedMessage = getFromMemoryCache(messageKey, language);
        if (cachedMessage != null) {
            return cachedMessage;
        }

        // 2. 尝试Redis缓存
        if (cacheEnabled) {
            cachedMessage = getFromRedisCache(messageKey, language);
            if (cachedMessage != null) {
                putToMemoryCache(messageKey, language, cachedMessage);
                return cachedMessage;
            }
        }

        // 3. 从数据库查询
        String message = getFromDatabase(messageKey, language);
        if (message != null) {
            // 更新缓存
            if (cacheEnabled) {
                putToRedisCache(messageKey, language, message);
            }
            putToMemoryCache(messageKey, language, message);
            return message;
        }

        // 4. 返回默认值
        log.warn("Message not found for key: {} and language: {}", messageKey, language);
        return messageKey;
    }

    @Override
    public String getMessage(String messageKey, String language, Object... args) {
        String message = getMessage(messageKey, language);
        if (args != null && args.length > 0) {
            try {
                return String.format(message, args);
            } catch (Exception e) {
                log.error("Failed to format message: {} with args: {}", message, Arrays.toString(args), e);
                return message;
            }
        }
        return message;
    }

    @Override
    public Map<String, String> getMessagesByModule(String module, String language) {
        if (!StringUtils.hasText(module)) {
            return Collections.emptyMap();
        }

        List<I18nMessage> messages = i18nMessageRepository.findByModuleAndEnabled(module, true);
        Map<String, String> result = new HashMap<>();
        
        for (I18nMessage message : messages) {
            String text = message.getText(language);
            if (text != null) {
                result.put(message.getMessageKey(), text);
            }
        }
        
        return result;
    }

    @Override
    public Map<String, String> getMessagesByCategory(String category, String language) {
        if (!StringUtils.hasText(category)) {
            return Collections.emptyMap();
        }

        List<I18nMessage> messages = i18nMessageRepository.findByCategoryAndEnabled(category, true);
        Map<String, String> result = new HashMap<>();
        
        for (I18nMessage message : messages) {
            String text = message.getText(language);
            if (text != null) {
                result.put(message.getMessageKey(), text);
            }
        }
        
        return result;
    }

    @Override
    public I18nMessage saveMessage(I18nMessage message) {
        if (message == null || !StringUtils.hasText(message.getMessageKey())) {
            throw new IllegalArgumentException("Message key cannot be empty");
        }

        // 检查是否已存在
        Optional<I18nMessage> existing = i18nMessageRepository.findByMessageKey(message.getMessageKey());
        if (existing.isPresent()) {
            I18nMessage existingMessage = existing.get();
            existingMessage.setTranslations(message.getTranslations());
            existingMessage.setDescription(message.getDescription());
            existingMessage.setEnabled(message.getEnabled());
            existingMessage.setUpdatedTime(LocalDateTime.now());
            existingMessage.setUpdatedBy(message.getUpdatedBy());
            
            I18nMessage saved = i18nMessageRepository.save(existingMessage);
            evictCache(message.getMessageKey());
            return saved;
        }

        // 设置默认值
        if (message.getEnabled() == null) {
            message.setEnabled(true);
        }
        if (message.getDefaultLanguage() == null) {
            message.setDefaultLanguage(defaultLanguage);
        }
        if (message.getCreatedTime() == null) {
            message.setCreatedTime(LocalDateTime.now());
        }

        I18nMessage saved = i18nMessageRepository.save(message);
        evictCache(message.getMessageKey());
        return saved;
    }

    @Override
    public void deleteMessage(String messageKey) {
        if (!StringUtils.hasText(messageKey)) {
            return;
        }

        Optional<I18nMessage> message = i18nMessageRepository.findByMessageKey(messageKey);
        if (message.isPresent()) {
            i18nMessageRepository.delete(message.get());
            evictCache(messageKey);
        }
    }

    @Override
    public void deleteMessage(String messageKey, String language) {
        if (!StringUtils.hasText(messageKey) || !StringUtils.hasText(language)) {
            return;
        }

        Optional<I18nMessage> messageOpt = i18nMessageRepository.findByMessageKey(messageKey);
        if (messageOpt.isPresent()) {
            I18nMessage message = messageOpt.get();
            Map<String, String> translations = message.getTranslations();
            if (translations != null) {
                translations.remove(language);
                if (translations.isEmpty()) {
                    // 如果没有翻译了，删除整个消息
                    i18nMessageRepository.delete(message);
                } else {
                    // 更新消息
                    message.setTranslations(translations);
                    i18nMessageRepository.save(message);
                }
            }
            evictCache(messageKey);
        }
    }

    @Override
    public Page<I18nMessage> findMessages(String keyword, String module, String category, Pageable pageable) {
        // 这里简化实现，实际可以根据需要添加更复杂的查询逻辑
        if (StringUtils.hasText(keyword)) {
            // 可以根据关键词搜索
            return i18nMessageRepository.findAll(pageable);
        } else if (StringUtils.hasText(module)) {
            return i18nMessageRepository.findByModule(module, pageable);
        } else if (StringUtils.hasText(category)) {
            return i18nMessageRepository.findByCategory(category, pageable);
        } else {
            return i18nMessageRepository.findAll(pageable);
        }
    }

    @Override
    public void clearCache() {
        // 清空内存缓存
        memoryCache.clear();
        
        // 清空Redis缓存
        if (cacheEnabled) {
            Set<String> keys = redisTemplate.keys("i18n:*");
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
            }
        }
        
        log.info("I18n cache cleared");
    }

    @Override
    public void evictCache(String messageKey) {
        // 清空内存缓存中的相关条目
        memoryCache.entrySet().removeIf(entry -> entry.getKey().startsWith(messageKey + ":"));
        
        // 清空Redis缓存中的相关条目
        if (cacheEnabled) {
            Set<String> keys = redisTemplate.keys("i18n:" + messageKey + ":*");
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
            }
        }
    }

    @Override
    public List<String> getSupportedLanguages() {
        return Arrays.asList("zh-CN", "en-US");
    }

    @Override
    public Map<String, Long> getStatistics() {
        Map<String, Long> stats = new HashMap<>();
        stats.put("totalMessages", i18nMessageRepository.count());
        stats.put("enabledMessages", i18nMessageRepository.countByEnabled(true));
        stats.put("memoryCacheSize", (long) memoryCache.size());
        return stats;
    }

    // ==================== 私有方法 ====================

    /**
     * 从内存缓存获取消息
     */
    private String getFromMemoryCache(String messageKey, String language) {
        String cacheKey = messageKey + ":" + language;
        CacheEntry entry = memoryCache.get(cacheKey);
        
        if (entry != null && !entry.isExpired()) {
            return entry.message;
        }
        
        if (entry != null && entry.isExpired()) {
            memoryCache.remove(cacheKey);
        }
        
        return null;
    }

    /**
     * 从Redis缓存获取消息
     */
    private String getFromRedisCache(String messageKey, String language) {
        String cacheKey = "i18n:" + messageKey + ":" + language;
        return redisTemplate.opsForValue().get(cacheKey);
    }

    /**
     * 从数据库获取消息
     */
    private String getFromDatabase(String messageKey, String language) {
        Optional<I18nMessage> messageOpt = i18nMessageRepository.findByMessageKeyAndEnabled(messageKey, true);
        if (messageOpt.isPresent()) {
            I18nMessage message = messageOpt.get();
            return message.getText(language);
        }
        return null;
    }

    /**
     * 放入内存缓存
     */
    private void putToMemoryCache(String messageKey, String language, String message) {
        // 控制内存缓存大小
        if (memoryCache.size() >= memoryCacheSize) {
            // 简单的LRU策略：删除过期的条目
            memoryCache.entrySet().removeIf(entry -> entry.getValue().isExpired());
            
            // 如果还是太大，删除最旧的条目
            if (memoryCache.size() >= memoryCacheSize) {
                memoryCache.clear();
            }
        }
        
        String cacheKey = messageKey + ":" + language;
        long expireTime = System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(memoryCacheExpireMinutes);
        memoryCache.put(cacheKey, new CacheEntry(message, expireTime));
    }

    /**
     * 放入Redis缓存
     */
    private void putToRedisCache(String messageKey, String language, String message) {
        String cacheKey = "i18n:" + messageKey + ":" + language;
        redisTemplate.opsForValue().set(cacheKey, message, redisExpireHours, TimeUnit.HOURS);
    }
} 