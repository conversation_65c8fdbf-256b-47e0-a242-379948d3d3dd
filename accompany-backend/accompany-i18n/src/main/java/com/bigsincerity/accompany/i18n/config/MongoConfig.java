package com.bigsincerity.accompany.i18n.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.config.AbstractMongoClientConfiguration;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

/**
 * MongoDB 配置类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Configuration
@EnableMongoRepositories(basePackages = "com.bigsincerity.accompany.i18n.repository")
public class MongoConfig extends AbstractMongoClientConfiguration {

    @Value("${spring.data.mongodb.uri}")
    private String mongoUri;

    @Value("${spring.data.mongodb.database}")
    private String databaseName;

    @Override
    protected String getDatabaseName() {
        return databaseName;
    }

    @Override
    protected String getMappingBasePackage() {
        return "com.bigsincerity.accompany.i18n.entity";
    }

    @Bean
    @Override
    public MongoTemplate mongoTemplate() throws Exception {
        MongoTemplate mongoTemplate = super.mongoTemplate();
        
        // 配置索引
        createIndexes(mongoTemplate);
        
        log.info("MongoDB template initialized for database: {}", getDatabaseName());
        return mongoTemplate;
    }

    /**
     * 创建必要的索引
     */
    private void createIndexes(MongoTemplate mongoTemplate) {
        try {
            // 创建复合索引
            mongoTemplate.indexOps("i18n_messages")
                .ensureIndex(
                    org.springframework.data.mongodb.core.index.Index
                        .on("messageKey", org.springframework.data.domain.Sort.Direction.ASC)
                        .unique()
                );

            mongoTemplate.indexOps("i18n_messages")
                .ensureIndex(
                    org.springframework.data.mongodb.core.index.Index
                        .on("module", org.springframework.data.domain.Sort.Direction.ASC)
                        .on("category", org.springframework.data.domain.Sort.Direction.ASC)
                );

            mongoTemplate.indexOps("i18n_messages")
                .ensureIndex(
                    org.springframework.data.mongodb.core.index.Index
                        .on("enabled", org.springframework.data.domain.Sort.Direction.ASC)
                        .on("updatedTime", org.springframework.data.domain.Sort.Direction.DESC)
                );

            log.info("MongoDB indexes created successfully");
        } catch (Exception e) {
            log.warn("Failed to create MongoDB indexes: {}", e.getMessage());
        }
    }
} 