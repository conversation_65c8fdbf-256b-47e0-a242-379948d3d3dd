package com.bigsincerity.accompany.i18n.controller;

import com.bigsincerity.accompany.common.result.Result;
import com.bigsincerity.accompany.i18n.entity.I18nMessage;
import com.bigsincerity.accompany.i18n.service.impl.I18nServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 多语言API控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class I18nController {

    private final I18nServiceImpl i18nService;

    /**
     * 获取单个消息
     */
    @GetMapping("/messages/{messageKey}")
    public Result<String> getMessage(
            @PathVariable String messageKey,
            @RequestParam(defaultValue = "zh-CN") String lang) {
        
        log.debug("Getting message: {} for language: {}", messageKey, lang);
        String message = i18nService.getMessage(messageKey, lang);
        return Result.success(message);
    }

    /**
     * 获取带参数的消息
     */
    @GetMapping("/messages/{messageKey}/format")
    public Result<String> getFormattedMessage(
            @PathVariable String messageKey,
            @RequestParam(defaultValue = "zh-CN") String lang,
            @RequestParam(required = false) List<String> args) {
        
        log.debug("Getting formatted message: {} for language: {} with args: {}", messageKey, lang, args);
        String message = i18nService.getMessage(messageKey, lang, args != null ? args.toArray() : null);
        return Result.success(message);
    }

    /**
     * 获取模块的所有消息
     */
    @GetMapping("/messages/module/{module}")
    public Result<Map<String, String>> getMessagesByModule(
            @PathVariable String module,
            @RequestParam(defaultValue = "zh-CN") String lang) {
        
        log.debug("Getting messages for module: {} and language: {}", module, lang);
        Map<String, String> messages = i18nService.getMessagesByModule(module, lang);
        return Result.success(messages);
    }

    /**
     * 获取分类的所有消息
     */
    @GetMapping("/messages/category/{category}")
    public Result<Map<String, String>> getMessagesByCategory(
            @PathVariable String category,
            @RequestParam(defaultValue = "zh-CN") String lang) {
        
        log.debug("Getting messages for category: {} and language: {}", category, lang);
        Map<String, String> messages = i18nService.getMessagesByCategory(category, lang);
        return Result.success(messages);
    }

    /**
     * 分页查询消息
     */
    @GetMapping("/messages")
    public Result<Page<I18nMessage>> findMessages(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String module,
            @RequestParam(required = false) String category,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        log.debug("Finding messages with keyword: {}, module: {}, category: {}, page: {}, size: {}", 
                keyword, module, category, page, size);
        
        Pageable pageable = PageRequest.of(page, size);
        Page<I18nMessage> messages = i18nService.findMessages(keyword, module, category, pageable);
        return Result.success(messages);
    }

    /**
     * 保存消息
     */
    @PostMapping("/messages")
    public Result<I18nMessage> saveMessage(@RequestBody I18nMessage message) {
        log.info("Saving message: {}", message.getMessageKey());
        I18nMessage saved = i18nService.saveMessage(message);
        return Result.success(saved);
    }

    /**
     * 更新消息
     */
    @PutMapping("/messages/{messageKey}")
    public Result<I18nMessage> updateMessage(
            @PathVariable String messageKey,
            @RequestBody I18nMessage message) {
        
        log.info("Updating message: {}", messageKey);
        message.setMessageKey(messageKey);
        I18nMessage updated = i18nService.saveMessage(message);
        return Result.success(updated);
    }

    /**
     * 删除消息
     */
    @DeleteMapping("/messages/{messageKey}")
    public Result<Void> deleteMessage(@PathVariable String messageKey) {
        log.info("Deleting message: {}", messageKey);
        i18nService.deleteMessage(messageKey);
        return Result.success();
    }

    /**
     * 删除指定语言的消息
     */
    @DeleteMapping("/messages/{messageKey}/languages/{language}")
    public Result<Void> deleteMessageLanguage(
            @PathVariable String messageKey,
            @PathVariable String language) {
        
        log.info("Deleting message language: {} for key: {}", language, messageKey);
        i18nService.deleteMessage(messageKey, language);
        return Result.success();
    }

    /**
     * 清空缓存
     */
    @PostMapping("/cache/clear")
    public Result<Void> clearCache() {
        log.info("Clearing i18n cache");
        i18nService.clearCache();
        return Result.success();
    }

    /**
     * 获取缓存统计
     */
    @GetMapping("/cache/stats")
    public Result<Map<String, Long>> getCacheStats() {
        log.debug("Getting cache statistics");
        Map<String, Long> stats = i18nService.getStatistics();
        return Result.success(stats);
    }

    /**
     * 获取支持的语言列表
     */
    @GetMapping("/languages")
    public Result<List<String>> getSupportedLanguages() {
        log.debug("Getting supported languages");
        List<String> languages = i18nService.getSupportedLanguages();
        return Result.success(languages);
    }

    /**
     * 批量保存消息
     */
    @PostMapping("/messages/batch")
    public Result<List<I18nMessage>> saveMessages(@RequestBody List<I18nMessage> messages) {
        log.info("Saving {} messages in batch", messages.size());
        List<I18nMessage> savedMessages = new java.util.ArrayList<>();
        
        for (I18nMessage message : messages) {
            try {
                I18nMessage saved = i18nService.saveMessage(message);
                savedMessages.add(saved);
            } catch (Exception e) {
                log.error("Failed to save message: {}", message.getMessageKey(), e);
            }
        }
        
        return Result.success(savedMessages);
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Result<Map<String, Object>> health() {
        Map<String, Object> health = new java.util.HashMap<>();
        health.put("status", "UP");
        health.put("service", "i18n-service");
        health.put("timestamp", System.currentTimeMillis());
        health.put("cacheEnabled", true);
        health.put("supportedLanguages", i18nService.getSupportedLanguages());
        health.put("statistics", i18nService.getStatistics());
        
        return Result.success(health);
    }
} 