package com.bigsincerity.accompany.i18n.repository;

import com.bigsincerity.accompany.i18n.entity.I18nMessage;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 多语言消息数据访问层
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface I18nMessageRepository extends MongoRepository<I18nMessage, String> {

    /**
     * 根据消息键查找
     */
    Optional<I18nMessage> findByMessageKey(String messageKey);

    /**
     * 根据消息键和启用状态查找
     */
    Optional<I18nMessage> findByMessageKeyAndEnabled(String messageKey, Boolean enabled);

    /**
     * 根据模块查找所有消息
     */
    List<I18nMessage> findByModule(String module);

    /**
     * 根据模块和启用状态查找
     */
    List<I18nMessage> findByModuleAndEnabled(String module, Boolean enabled);

    /**
     * 根据分类查找所有消息
     */
    List<I18nMessage> findByCategory(String category);

    /**
     * 根据分类和启用状态查找
     */
    List<I18nMessage> findByCategoryAndEnabled(String category, Boolean enabled);

    /**
     * 根据启用状态查找所有消息
     */
    List<I18nMessage> findByEnabled(Boolean enabled);

    /**
     * 检查消息键是否存在
     */
    boolean existsByMessageKey(String messageKey);

    /**
     * 根据模块和分类查找
     */
    List<I18nMessage> findByModuleAndCategory(String module, String category);

    /**
     * 根据模块、分类和启用状态查找
     */
    List<I18nMessage> findByModuleAndCategoryAndEnabled(String module, String category, Boolean enabled);

    /**
     * 查找包含特定语言的消息
     */
    @Query("{ 'translations.?0': { $exists: true }, 'enabled': true }")
    List<I18nMessage> findByLanguageExists(String language);

    /**
     * 查找缺少特定语言的消息
     */
    @Query("{ 'translations.?0': { $exists: false }, 'enabled': true }")
    List<I18nMessage> findByLanguageMissing(String language);

    /**
     * 根据创建人查找
     */
    List<I18nMessage> findByCreatedBy(String createdBy);

    /**
     * 根据更新人查找
     */
    List<I18nMessage> findByUpdatedBy(String updatedBy);

    /**
     * 统计各模块的消息数量
     */
    @Query(value = "{}", count = true)
    long countByModule(String module);

    /**
     * 统计各分类的消息数量
     */
    @Query(value = "{}", count = true)
    long countByCategory(String category);
} 