-- ========== 大数据量主键设计方案 ==========

-- 方案一：雪花算法ID生成器配置表
CREATE TABLE snowflake_config (
node_id INT PRIMARY KEY COMMENT '节点ID (0-1023)',
datacenter_id INT NOT NULL COMMENT '数据中心ID (0-31)',
is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
last_timestamp BIGINT COMMENT '最后生成时间戳',
created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '雪花算法节点配置表';

-- 雪花算法配置示例数据
INSERT INTO snowflake_config (node_id, datacenter_id, is_active) VALUES
(0, 0, TRUE),   -- 数据中心0，节点0 (主节点)
(1, 0, TRUE),   -- 数据中心0，节点1 (备用节点)
(0, 1, TRUE),   -- 数据中心1，节点0 (异地备份)
(1, 1, FALSE);  -- 数据中心1，节点1 (预留节点)

-- 雪花算法序列号管理表
CREATE TABLE snowflake_sequence (
node_id INT PRIMARY KEY COMMENT '节点ID',
current_sequence INT DEFAULT 0 COMMENT '当前序列号 (0-4095)',
last_timestamp BIGINT NOT NULL COMMENT '最后生成时间戳',
updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
FOREIGN KEY (node_id) REFERENCES snowflake_config(node_id)
) COMMENT '雪花算法序列号管理表';

/*
=== 雪花算法详细说明 ===

1. 雪花算法ID结构（64位）：
   ┌─────────────────────────────────────────────────────────────────┐
   │ 1位符号 │ 41位时间戳 │ 5位数据中心ID │ 5位节点ID │ 12位序列号 │
   └─────────────────────────────────────────────────────────────────┘
   │    0    │ 41 bits   │   5 bits     │  5 bits  │  12 bits  │

    - 符号位：固定为0
    - 时间戳：41位，精确到毫秒，可用69年 (2^41 / (365*24*3600*1000) ≈ 69)
    - 数据中心ID：5位，支持32个数据中心 (2^5 = 32)
    - 节点ID：5位，每个数据中心支持32个节点 (2^5 = 32)
    - 序列号：12位，每毫秒可生成4096个ID (2^12 = 4096)

2. 配置表使用方法：

   a) 节点注册：
   INSERT INTO snowflake_config (node_id, datacenter_id, is_active)
   VALUES (节点编号, 数据中心编号, TRUE);

   b) 节点查询：
   SELECT node_id, datacenter_id FROM snowflake_config
   WHERE is_active = TRUE AND node_id = ?;

   c) 时间戳更新：
   UPDATE snowflake_config
   SET last_timestamp = ?
   WHERE node_id = ?;

3. Java后端实现示例：
   */

-- ID生成日志表（用于监控和调试）
CREATE TABLE snowflake_generate_log (
log_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
generated_id BIGINT NOT NULL COMMENT '生成的雪花ID',
node_id INT NOT NULL COMMENT '生成节点',
datacenter_id INT NOT NULL COMMENT '数据中心',
timestamp_part BIGINT NOT NULL COMMENT '时间戳部分',
sequence_part INT NOT NULL COMMENT '序列号部分',
generate_time DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '生成时间(毫秒精度)',
INDEX idx_generated_id (generated_id) COMMENT '生成ID索引',
INDEX idx_node_time (node_id, generate_time) COMMENT '节点时间索引'
) COMMENT '雪花算法ID生成日志表';

-- ID解析函数存储过程
DELIMITER //
CREATE FUNCTION parse_snowflake_timestamp(snowflake_id BIGINT)
RETURNS BIGINT
READS SQL DATA
COMMENT '解析雪花ID中的时间戳部分'
BEGIN
DECLARE timestamp_part BIGINT;
-- 右移22位（5+5+12）获取时间戳，再加上起始时间戳
SET timestamp_part = (snowflake_id >> 22) + 1288834974657; -- 2010-11-04 起始时间
RETURN timestamp_part;
END //

CREATE FUNCTION parse_snowflake_datacenter(snowflake_id BIGINT)
RETURNS INT
READS SQL DATA
COMMENT '解析雪花ID中的数据中心ID'
BEGIN
DECLARE datacenter_id INT;
-- 右移17位，然后与31(0x1F)进行AND操作获取5位数据中心ID
SET datacenter_id = (snowflake_id >> 17) & 31;
RETURN datacenter_id;
END //

CREATE FUNCTION parse_snowflake_node(snowflake_id BIGINT)
RETURNS INT
READS SQL DATA
COMMENT '解析雪花ID中的节点ID'
BEGIN
DECLARE node_id INT;
-- 右移12位，然后与31(0x1F)进行AND操作获取5位节点ID
SET node_id = (snowflake_id >> 12) & 31;
RETURN node_id;
END //

CREATE FUNCTION parse_snowflake_sequence(snowflake_id BIGINT)
RETURNS INT
READS SQL DATA
COMMENT '解析雪花ID中的序列号'
BEGIN
DECLARE sequence_num INT;
-- 与4095(0xFFF)进行AND操作获取12位序列号
SET sequence_num = snowflake_id & 4095;
RETURN sequence_num;
END //
DELIMITER ;



--------------其他方案 -------------------------------------------------
-- 方案二：分表路由配置
CREATE TABLE table_sharding_config (
    config_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    table_name VARCHAR(50) NOT NULL COMMENT '表名',
    shard_count INT NOT NULL COMMENT '分片数量',
    shard_key VARCHAR(50) NOT NULL COMMENT '分片键字段',
    shard_algorithm VARCHAR(20) NOT NULL COMMENT '分片算法 (mod|range|hash)',
    max_id_per_shard BIGINT DEFAULT 1000000000 COMMENT '每个分片最大ID数',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '分表配置表';

-- 方案三：全局ID序列表（用于分布式环境）
CREATE TABLE global_id_sequences (
    sequence_name VARCHAR(50) PRIMARY KEY COMMENT '序列名称',
    current_value BIGINT NOT NULL DEFAULT 0 COMMENT '当前值',
    increment_by INT NOT NULL DEFAULT 1 COMMENT '递增步长',
    max_value BIGINT COMMENT '最大值',
    cycle_flag BOOLEAN DEFAULT FALSE COMMENT '是否循环',
    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间'
) COMMENT '全局ID序列管理表';

-- 性能测试记录表
CREATE TABLE performance_test_log (
    test_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '测试ID',
    test_type VARCHAR(20) NOT NULL COMMENT '测试类型 (bigint|decimal|uuid|snowflake)',
    operation_type VARCHAR(20) NOT NULL COMMENT '操作类型 (insert|select|update|delete)',
    record_count BIGINT NOT NULL COMMENT '记录数量',
    execution_time_ms INT NOT NULL COMMENT '执行时间(毫秒)',
    cpu_usage DECIMAL(5,2) COMMENT 'CPU使用率',
    memory_usage_mb DECIMAL(10,2) COMMENT '内存使用量(MB)',
    test_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '测试时间'
) COMMENT '主键性能测试记录表';

-- 示例：用户表分片版本
CREATE TABLE users_shard_template (
    user_id BIGINT PRIMARY KEY COMMENT '用户ID（雪花算法生成）',
    shard_id TINYINT NOT NULL COMMENT '分片ID',
    user_name VARCHAR(50) NOT NULL COMMENT '用户名',
    email VARCHAR(100) NOT NULL COMMENT '邮箱',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_shard_email (shard_id, email) COMMENT '分片邮箱索引'
) COMMENT '用户表分片模板（支持超大数据量）';

/*
大数据量主键设计说明：

1. BIGINT容量：
   - BIGINT UNSIGNED: 18,446,744,073,709,551,615 (约1844万亿)
   - 即使每秒100万条记录，也需要约30年才能耗尽

2. 超量解决方案：
   - 雪花算法：分布式唯一ID，64位长整型
   - 分库分表：水平切分，每个分片独立计数
   - 复合主键：分片ID + 序列ID组合
   - UUID + BIGINT：UUID对外，BIGINT内部主键

3. 性能考虑：
   - BIGINT > DECIMAL > VARCHAR(UUID)
   - 分表查询需要路由逻辑
   - 跨分片操作复杂度增加

4. 实际建议：
   - 99.9%的应用不会超过BIGINT限制
   - 预设计分表策略，按需实施
   - 监控ID使用情况，提前规划
*/
