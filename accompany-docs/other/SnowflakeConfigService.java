/**
 * 雪花算法使用示例
 * 
 * 1. 配置数据库表
 * 2. 在Service中使用ID生成器
 * 3. ID解析和查询示例
 */

// ===== 1. 配置管理Service =====
@Service
public class SnowflakeConfigService {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    /**
     * 注册新节点
     */
    public void registerNode(int nodeId, int datacenterId) {
        String sql = "INSERT INTO snowflake_config (node_id, datacenter_id, is_active) VALUES (?, ?, TRUE)";
        jdbcTemplate.update(sql, nodeId, datacenterId);
    }
    
    /**
     * 激活/停用节点
     */
    public void toggleNode(int nodeId, boolean active) {
        String sql = "UPDATE snowflake_config SET is_active = ? WHERE node_id = ?";
        jdbcTemplate.update(sql, active, nodeId);
    }
    
    /**
     * 获取活跃节点列表
     */
    public List<SnowflakeNode> getActiveNodes() {
        String sql = "SELECT node_id, datacenter_id, last_timestamp FROM snowflake_config WHERE is_active = TRUE";
        return jdbcTemplate.query(sql, (rs, rowNum) -> 
            new SnowflakeNode(rs.getInt("node_id"), rs.getInt("datacenter_id"), rs.getLong("last_timestamp"))
        );
    }
}

// ===== 2. 用户Service使用示例 =====
@Service
public class UserService {
    
    @Autowired
    private SnowflakeIdGenerator snowflakeIdGenerator;
    
    @Autowired
    private UserRepository userRepository;
    
    /**
     * 创建新用户
     */
    public User createUser(UserCreateRequest request) {
        // 生成雪花ID
        long userId = snowflakeIdGenerator.nextId();
        
        User user = new User();
        user.setUserId(userId);  // 使用雪花ID
        user.setUserName(request.getUserName());
        user.setEmail(request.getEmail());
        user.setCreatedAt(LocalDateTime.now());
        
        return userRepository.save(user);
    }
    
    /**
     * 批量生成ID
     */
    public List<Long> generateBatchIds(int count) {
        List<Long> ids = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            ids.add(snowflakeIdGenerator.nextId());
        }
        return ids;
    }
    
    /**
     * 解析用户ID信息
     */
    public SnowflakeInfo getUserIdInfo(long userId) {
        return snowflakeIdGenerator.parseId(userId);
    }
}

// ===== 3. 订单Service使用示例 =====
@Service
public class OrderService {
    
    @Autowired
    private SnowflakeIdGenerator snowflakeIdGenerator;
    
    /**
     * 创建订单
     */
    public Order createOrder(Long userId, Long serviceId, OrderCreateRequest request) {
        // 生成订单ID
        long orderId = snowflakeIdGenerator.nextId();
        
        Order order = new Order();
        order.setOrderId(orderId);
        order.setUserId(userId);
        order.setServiceId(serviceId);
        order.setAmount(request.getAmount());
        order.setStartAt(request.getStartAt());
        order.setEndAt(request.getEndAt());
        order.setStatus("pending");
        order.setCreatedAt(LocalDateTime.now());
        
        return orderRepository.save(order);
    }
}

// ===== 4. 监控和统计Service =====
@Service
public class SnowflakeMonitorService {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    /**
     * 获取ID生成统计
     */
    public Map<String, Object> getGenerationStats() {
        String sql = """
            SELECT 
                node_id,
                datacenter_id,
                COUNT(*) as total_generated,
                MIN(generate_time) as first_generate_time,
                MAX(generate_time) as last_generate_time,
                COUNT(DISTINCT DATE(generate_time)) as active_days
            FROM snowflake_generate_log 
            WHERE generate_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
            GROUP BY node_id, datacenter_id
            """;
        
        List<Map<String, Object>> stats = jdbcTemplate.queryForList(sql);
        
        Map<String, Object> result = new HashMap<>();
        result.put("node_stats", stats);
        result.put("total_generated_7days", getTotalGenerated7Days());
        result.put("avg_generation_per_second", getAvgGenerationPerSecond());
        
        return result;
    }
    
    /**
     * 检查ID冲突
     */
    public List<Long> checkIdConflicts() {
        String sql = """
            SELECT generated_id 
            FROM snowflake_generate_log 
            GROUP BY generated_id 
            HAVING COUNT(*) > 1
            """;
        
        return jdbcTemplate.queryForList(sql, Long.class);
    }
    
    /**
     * 获取节点性能
     */
    public Map<String, Object> getNodePerformance(int nodeId) {
        String sql = """
            SELECT 
                COUNT(*) as total_ids,
                COUNT(*) / (TIMESTAMPDIFF(SECOND, MIN(generate_time), MAX(generate_time)) + 1) as ids_per_second,
                MAX(sequence_part) as max_sequence_used
            FROM snowflake_generate_log 
            WHERE node_id = ? AND generate_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
            """;
        
        return jdbcTemplate.queryForMap(sql, nodeId);
    }
}

// ===== 5. 实体类 =====
public class SnowflakeNode {
    private int nodeId;
    private int datacenterId;
    private long lastTimestamp;
    
    // 构造函数、getter、setter...
}

// ===== 6. 使用示例和最佳实践 =====

/**
 * 使用步骤：
 * 
 * 1. 数据库初始化
 * -- 执行 dbdata/mysql.sql 中的雪花算法相关表创建语句
 * -- 插入节点配置数据
 * 
 * 2. 应用配置
 * -- 在 application.yml 中配置数据源
 * -- 确保 SnowflakeIdGenerator 能正确注入
 * 
 * 3. 在业务代码中使用
 * @Autowired
 * private SnowflakeIdGenerator idGenerator;
 * 
 * public void createSomething() {
 *     long id = idGenerator.nextId();
 *     // 使用生成的ID...
 * }
 * 
 * 4. 监控和维护
 * -- 定期检查ID生成日志
 * -- 监控节点性能
 * -- 处理时钟回拨问题
 */

/**
 * 性能特点：
 * 
 * 1. 生成能力
 * - 单节点：每毫秒最多4096个ID
 * - 多节点：32个数据中心 × 32个节点 × 4096个/毫秒 = 每毫秒419万个ID
 * 
 * 2. 存储空间
 * - BIGINT：8字节
 * - 比UUID（36字节）节省78%空间
 * 
 * 3. 查询性能
 * - 数值类型，索引效率高
 * - 支持范围查询和排序
 * 
 * 4. 时间有序性
 * - ID包含时间戳，天然有序
 * - 方便按时间分页和查询
 */

/**
 * 部署注意事项：
 * 
 * 1. 集群部署
 * - 每个节点配置不同的 node_id
 * - 确保同一数据中心内 node_id 唯一
 * - 不同数据中心可以使用相同 node_id
 * 
 * 2. 时钟同步
 * - 使用 NTP 确保服务器时钟同步
 * - 监控时钟回拨情况
 * - 设置时钟回拨容忍度（建议5ms内）
 * 
 * 3. 故障恢复
 * - 节点重启后自动从数据库读取配置
 * - 可以动态切换活跃节点
 * - 支持节点热备份
 * 
 * 4. 容量规划
 * - 41位时间戳可用69年
 * - 监控序列号使用情况
 * - 预估ID生成速度和存储需求
 */ 