<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accompany - 您的专属陪伴服务</title>
    <style>
        :root {
            --primary-color: #FF7E45;
            --secondary-color: #3B82F6;
            --light-color: #F9FAFB;
            --dark-color: #1F2937;
            --gray-color: #9CA3AF;
            --success-color: #10B981;
            --danger-color: #EF4444;
            --border-radius: 12px;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
        }
        
        body {
            background-color: #F5F5F5;
            color: var(--dark-color);
            line-height: 1.6;
        }
        
        .container {
            max-width: 390px;
            margin: 0 auto;
            background-color: white;
            height: 100vh;
            overflow: hidden;
            position: relative;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        /* 通用样式 */
        .btn {
            padding: 12px 24px;
            border-radius: var(--border-radius);
            border: none;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            display: inline-block;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .btn-outline {
            background-color: transparent;
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
        }
        
        .btn-block {
            display: block;
            width: 100%;
        }
        
        .card {
            background-color: white;
            border-radius: var(--border-radius);
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        
        .avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        .avatar-lg {
            width: 80px;
            height: 80px;
        }
        
        .badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            margin-right: 6px;
            margin-bottom: 6px;
        }
        
        .badge-primary {
            background-color: rgba(255, 126, 69, 0.1);
            color: var(--primary-color);
        }
        
        .badge-secondary {
            background-color: rgba(59, 130, 246, 0.1);
            color: var(--secondary-color);
        }
        
        .icon {
            width: 24px;
            height: 24px;
        }
        
        .text-primary {
            color: var(--primary-color);
        }
        
        .text-secondary {
            color: var(--secondary-color);
        }
        
        .text-gray {
            color: var(--gray-color);
        }
        
        .rating {
            color: #FBBF24;
        }
        
        .divider {
            height: 1px;
            background-color: #EFEFEF;
            margin: 16px 0;
        }
        
        /* 页面特定样式 */
        .splash-screen {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            text-align: center;
            padding: 32px;
        }
        
        .splash-logo {
            width: 120px;
            height: 120px;
            margin-bottom: 32px;
        }
        
        .login-form {
            padding: 32px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .login-form h1 {
            margin-bottom: 32px;
            font-size: 28px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-control {
            width: 100%;
            padding: 14px 16px;
            border: 1px solid #E5E7EB;
            border-radius: var(--border-radius);
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(255, 126, 69, 0.2);
        }
        
        .social-login {
            display: flex;
            justify-content: space-around;
            margin: 32px 0;
        }
        
        .social-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #F9FAFB;
            border: 1px solid #E5E7EB;
        }
        
        .header {
            padding: 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: white;
            border-bottom: 1px solid #EFEFEF;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .page-title {
            font-size: 18px;
            font-weight: 600;
        }
        
        .search-bar {
            display: flex;
            align-items: center;
            padding: 10px 16px;
            background-color: #F9FAFB;
            border-radius: var(--border-radius);
        }
        
        .search-bar input {
            border: none;
            background: transparent;
            flex: 1;
            margin-left: 10px;
            font-size: 15px;
        }
        
        .search-bar input:focus {
            outline: none;
        }
        
        .category-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
            padding: 16px;
        }
        
        .category-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }
        
        .category-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--border-radius);
            background-color: rgba(255, 126, 69, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
        }
        
        .category-name {
            font-size: 12px;
            color: var(--dark-color);
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-title .more {
            font-size: 14px;
            color: var(--gray-color);
            font-weight: normal;
        }
        
        .companion-card {
            display: flex;
            padding: 16px;
            border-bottom: 1px solid #EFEFEF;
        }
        
        .companion-info {
            flex: 1;
            margin-left: 16px;
        }
        
        .companion-name {
            font-weight: 600;
            margin-bottom: 4px;
            display: flex;
            align-items: center;
        }
        
        .companion-name .verified {
            width: 16px;
            height: 16px;
            margin-left: 6px;
        }
        
        .tags {
            margin: 8px 0;
        }
        
        .price {
            color: var(--primary-color);
            font-weight: 600;
        }
        
        .price .unit {
            font-size: 12px;
            font-weight: normal;
        }
        
        .bottom-nav {
            display: flex;
            position: fixed;
            bottom: 0;
            width: 100%;
            max-width: 390px;
            background-color: white;
            border-top: 1px solid #EFEFEF;
            padding: 8px 0;
            z-index: 100;
        }
        
        .nav-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 0;
            color: var(--gray-color);
            text-decoration: none;
            font-size: 12px;
        }
        
        .nav-item.active {
            color: var(--primary-color);
        }
        
        .nav-publish {
            background-color: var(--primary-color);
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: -20px;
            box-shadow: 0 4px 8px rgba(255, 126, 69, 0.3);
        }
        
        .profile-header {
            background-color: var(--primary-color);
            padding: 32px 16px;
            color: white;
            text-align: center;
        }
        
        .profile-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            border: 3px solid white;
            margin: 0 auto 16px;
            display: block;
        }
        
        .profile-stats {
            display: flex;
            justify-content: space-around;
            margin-top: 16px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-value {
            font-weight: 600;
            font-size: 18px;
        }
        
        .stat-label {
            font-size: 12px;
            opacity: 0.8;
        }
        
        .menu-list {
            padding: 16px;
        }
        
        .menu-item {
            display: flex;
            align-items: center;
            padding: 16px;
            background-color: white;
            border-radius: var(--border-radius);
            margin-bottom: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        
        .menu-item .icon {
            margin-right: 16px;
        }
        
        .menu-item .arrow {
            margin-left: auto;
        }
        
        .chat-container {
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        
        .chat-header {
            padding: 16px;
            display: flex;
            align-items: center;
            background-color: white;
            border-bottom: 1px solid #EFEFEF;
        }
        
        .chat-header .avatar {
            margin-right: 16px;
        }
        
        .chat-header .name {
            font-weight: 600;
            margin-bottom: 2px;
        }
        
        .chat-header .status {
            font-size: 12px;
            color: var(--gray-color);
        }
        
        .chat-header .actions {
            margin-left: auto;
            display: flex;
        }
        
        .chat-header .actions .icon {
            margin-left: 16px;
        }
        
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
            background-color: #F5F5F5;
        }
        
        .message {
            margin-bottom: 16px;
            max-width: 70%;
        }
        
        .message.received {
            align-self: flex-start;
        }
        
        .message.sent {
            align-self: flex-end;
            margin-left: auto;
        }
        
        .message-content {
            padding: 12px 16px;
            border-radius: 18px;
        }
        
        .received .message-content {
            background-color: white;
            border-bottom-left-radius: 4px;
        }
        
        .sent .message-content {
            background-color: var(--primary-color);
            color: white;
            border-bottom-right-radius: 4px;
        }
        
        .message-time {
            font-size: 12px;
            color: var(--gray-color);
            margin-top: 4px;
            text-align: right;
        }
        
        .chat-input {
            padding: 16px;
            display: flex;
            align-items: center;
            background-color: white;
            border-top: 1px solid #EFEFEF;
        }
        
        .chat-input .input-field {
            flex: 1;
            padding: 12px 16px;
            border-radius: 24px;
            border: 1px solid #E5E7EB;
            margin: 0 10px;
        }
        
        .chat-input .input-field:focus {
            outline: none;
            border-color: var(--primary-color);
        }
        
        .service-detail {
            padding-bottom: 80px;
        }
        
        .service-header {
            position: relative;
        }
        
        .service-header img {
            width: 100%;
            height: 250px;
            object-fit: cover;
        }
        
        .service-header .overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 20px 16px;
            background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
            color: white;
        }
        
        .service-header .overlay h2 {
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .service-content {
            padding: 16px;
        }
        
        .service-provider {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .provider-info {
            margin-left: 12px;
        }
        
        .service-description {
            margin-bottom: 20px;
        }
        
        .service-details {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        
        .detail-item {
            display: flex;
            align-items: center;
            width: 50%;
            margin-bottom: 12px;
        }
        
        .detail-item .icon {
            margin-right: 10px;
            color: var(--primary-color);
        }
        
        .service-reviews {
            margin-bottom: 20px;
        }
        
        .review-item {
            padding: 12px 0;
            border-bottom: 1px solid #EFEFEF;
        }
        
        .review-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .review-header .avatar {
            width: 36px;
            height: 36px;
            margin-right: 12px;
        }
        
        .reviewer-info .name {
            font-weight: 500;
        }
        
        .reviewer-info .date {
            font-size: 12px;
            color: var(--gray-color);
        }
        
        .fixed-bottom {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            max-width: 390px;
            margin: 0 auto;
            background-color: white;
            border-top: 1px solid #EFEFEF;
            padding: 16px;
            display: flex;
            justify-content: space-between;
            z-index: 50;
        }
        
        /* 动画 */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        /* 屏幕视图管理 */
        .screen {
            height: 100%;
            display: none;
        }
        
        .screen.active {
            display: block;
        }
        
        /* 内容区域，确保底部导航不会遮挡内容 */
        .content {
            padding-bottom: 70px;
            height: 100%;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 启动屏幕 -->
        <div class="screen splash-screen active" id="splash-screen">
            <img src="https://via.placeholder.com/120" alt="Accompany Logo" class="splash-logo">
            <h1 style="color: var(--primary-color); font-size: 32px;">Accompany</h1>
            <p style="margin-bottom: 40px; color: var(--gray-color);">找到你的专属陪伴，共享美好时光</p>
            <button class="btn btn-primary btn-block" onclick="switchScreen('login-screen')">开始体验</button>
        </div>
        
        <!-- 登录屏幕 -->
        <div class="screen login-form" id="login-screen">
            <h1>欢迎使用<span class="text-primary">Accompany</span></h1>
            
            <div class="form-group">
                <input type="tel" class="form-control" placeholder="请输入手机号码">
            </div>
            
            <div class="form-group" style="display: flex;">
                <input type="text" class="form-control" placeholder="请输入验证码" style="flex: 1; margin-right: 10px;">
                <button class="btn btn-outline">获取验证码</button>
            </div>
            
            <button class="btn btn-primary btn-block" onclick="switchScreen('home-screen')">登录 / 注册</button>
            
            <div style="text-align: center; margin: 20px 0; color: var(--gray-color);">
                其他登录方式
            </div>
            
            <div class="social-login">
                <div class="social-btn">
                    <img src="https://via.placeholder.com/24" alt="WeChat" class="icon">
                </div>
                <div class="social-btn">
                    <img src="https://via.placeholder.com/24" alt="QQ" class="icon">
                </div>
                <div class="social-btn">
                    <img src="https://via.placeholder.com/24" alt="Apple" class="icon">
                </div>
            </div>
            
            <p style="text-align: center; font-size: 12px; color: var(--gray-color); margin-top: auto;">
                登录即表示您同意<a href="#" class="text-primary">用户协议</a>和<a href="#" class="text-primary">隐私政策</a>
            </p>
        </div>
        
        <!-- 首页 -->
        <div class="screen" id="home-screen">
            <div class="header">
                <div style="display: flex; align-items: center;">
                    <img src="https://via.placeholder.com/24" alt="Location" class="icon" style="margin-right: 8px;">
                    <span>北京市</span>
                </div>
                <img src="https://via.placeholder.com/24" alt="Notification" class="icon">
            </div>
            
            <div class="content">
                <div class="search-bar">
                    <img src="https://via.placeholder.com/18" alt="Search" class="icon" style="width: 18px; height: 18px;">
                    <input type="text" placeholder="搜索陪伴服务">
                </div>
                
                <div style="padding: 16px;">
                    <img src="https://via.placeholder.com/358x120" alt="Banner" style="width: 100%; border-radius: var(--border-radius);">
                </div>
                
                <div class="category-grid">
                    <div class="category-item">
                        <div class="category-icon">
                            <img src="https://via.placeholder.com/24" alt="Movie" class="icon">
                        </div>
                        <span class="category-name">陪看电影</span>
                    </div>
                    <div class="category-item">
                        <div class="category-icon">
                            <img src="https://via.placeholder.com/24" alt="Food" class="icon">
                        </div>
                        <span class="category-name">陪吃饭</span>
                    </div>
                    <div class="category-item">
                        <div class="category-icon">
                            <img src="https://via.placeholder.com/24" alt="Game" class="icon">
                        </div>
                        <span class="category-name">陪打游戏</span>
                    </div>
                    <div class="category-item">
                        <div class="category-icon">
                            <img src="https://via.placeholder.com/24" alt="Chat" class="icon">
                        </div>
                        <span class="category-name">陪聊天</span>
                    </div>
                    <div class="category-item">
                        <div class="category-icon">
                            <img src="https://via.placeholder.com/24" alt="Shopping" class="icon">
                        </div>
                        <span class="category-name">陪购物</span>
                    </div>
                    <div class="category-item">
                        <div class="category-icon">
                            <img src="https://via.placeholder.com/24" alt="Travel" class="icon">
                        </div>
                        <span class="category-name">陪旅行</span>
                    </div>
                    <div class="category-item">
                        <div class="category-icon">
                            <img src="https://via.placeholder.com/24" alt="Study" class="icon">
                        </div>
                        <span class="category-name">陪学习</span>
                    </div>
                    <div class="category-item">
                        <div class="category-icon">
                            <img src="https://via.placeholder.com/24" alt="More" class="icon">
                        </div>
                        <span class="category-name">更多</span>
                    </div>
                </div>
                
                <div class="section-title">
                    <span>推荐陪伴</span>
                    <span class="more">查看更多 ></span>
                </div>
                
                <div onclick="switchScreen('service-detail-screen')">
                    <div class="companion-card">
                        <img src="https://via.placeholder.com/50" alt="Avatar" class="avatar">
                        <div class="companion-info">
                            <div class="companion-name">
                                小林
                                <img src="https://via.placeholder.com/16" alt="Verified" class="verified">
                            </div>
                            <div style="display: flex; align-items: center;">
                                <div class="rating">★★★★★</div>
                                <span style="font-size: 12px; color: var(--gray-color); margin-left: 8px;">5.0 (324条评价)</span>
                            </div>
                            <div class="tags">
                                <span class="badge badge-primary">电影</span>
                                <span class="badge badge-primary">美食</span>
                                <span class="badge badge-primary">聊天</span>
                            </div>
                            <div class="price">
                                ¥188 <span class="unit">/ 小时</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="companion-card">
                        <img src="https://via.placeholder.com/50" alt="Avatar" class="avatar">
                        <div class="companion-info">
                            <div class="companion-name">
                                王小美
                                <img src="https://via.placeholder.com/16" alt="Verified" class="verified">
                            </div>
                            <div style="display: flex; align-items: center;">
                                <div class="rating">★★★★☆</div>
                                <span style="font-size: 12px; color: var(--gray-color); margin-left: 8px;">4.8 (156条评价)</span>
                            </div>
                            <div class="tags">
                                <span class="badge badge-primary">游戏</span>
                                <span class="badge badge-primary">购物</span>
                                <span class="badge badge-primary">旅行</span>
                            </div>
                            <div class="price">
                                ¥168 <span class="unit">/ 小时</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="companion-card">
                        <img src="https://via.placeholder.com/50" alt="Avatar" class="avatar">
                        <div class="companion-info">
                            <div class="companion-name">
                                张先生
                                <img src="https://via.placeholder.com/16" alt="Verified" class="verified">
                            </div>
                            <div style="display: flex; align-items: center;">
                                <div class="rating">★★★★★</div>
                                <span style="font-size: 12px; color: var(--gray-color); margin-left: 8px;">4.9 (78条评价)</span>
                            </div>
                            <div class="tags">
                                <span class="badge badge-primary">学习</span>
                                <span class="badge badge-primary">电影</span>
                                <span class="badge badge-primary">聊天</span>
                            </div>
                            <div class="price">
                                ¥198 <span class="unit">/ 小时</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="section-title">
                    <span>热门活动</span>
                    <span class="more">查看更多 ></span>
                </div>
                
                <div style="padding: 0 16px 16px;">
                    <div class="card" style="margin-bottom: 12px;">
                        <img src="https://via.placeholder.com/328x120" alt="Activity" style="width: 100%; border-radius: var(--border-radius); margin-bottom: 12px;">
                        <h3>周末电影马拉松</h3>
                        <p style="font-size: 14px; color: var(--gray-color); margin: 8px 0;">一起享受周末电影时光，分享彼此的观影体验</p>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="font-size: 14px;">12月18日-19日</span>
                            <button class="btn btn-outline" style="padding: 8px 16px;">立即参与</button>
                        </div>
                    </div>
                    
                    <div class="card">
                        <img src="https://via.placeholder.com/328x120" alt="Activity" style="width: 100%; border-radius: var(--border-radius); margin-bottom: 12px;">
                        <h3>美食探店团</h3>
                        <p style="font-size: 14px; color: var(--gray-color); margin: 8px 0;">与志同道合的伙伴一起探索城市美食，不再独自就餐</p>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="font-size: 14px;">每周六下午</span>
                            <button class="btn btn-outline" style="padding: 8px 16px;">立即参与</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bottom-nav">
                <a href="#" class="nav-item active" onclick="switchScreen('home-screen')">
                    <img src="https://via.placeholder.com/24" alt="Home" class="icon">
                    <span>首页</span>
                </a>
                <a href="#" class="nav-item" onclick="switchScreen('home-screen')">
                    <img src="https://via.placeholder.com/24" alt="Discover" class="icon">
                    <span>发现</span>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-publish">
                        <img src="https://via.placeholder.com/24" alt="Publish" class="icon" style="filter: brightness(0) invert(1);">
                    </div>
                    <span>发布</span>
                </a>
                <a href="#" class="nav-item" onclick="switchScreen('chat-list-screen')">
                    <img src="https://via.placeholder.com/24" alt="Message" class="icon">
                    <span>消息</span>
                </a>
                <a href="#" class="nav-item" onclick="switchScreen('profile-screen')">
                    <img src="https://via.placeholder.com/24" alt="Profile" class="icon">
                    <span>我的</span>
                </a>
            </div>
        </div>
        
        <!-- 服务详情页 -->
        <div class="screen" id="service-detail-screen">
            <div class="header">
                <img src="https://via.placeholder.com/24" alt="Back" class="icon" onclick="switchScreen('home-screen')">
                <div class="page-title">服务详情</div>
                <img src="https://via.placeholder.com/24" alt="Share" class="icon">
            </div>
            
            <div class="content service-detail">
                <div class="service-header">
                    <img src="https://via.placeholder.com/390x250" alt="Service Cover">
                    <div class="overlay">
                        <h2>专业电影陪看服务</h2>
                        <div class="rating">★★★★★ 5.0</div>
                    </div>
                </div>
                
                <div class="service-content">
                    <div class="service-provider">
                        <img src="https://via.placeholder.com/60" alt="Provider" class="avatar">
                        <div class="provider-info">
                            <h3>小林 <img src="https://via.placeholder.com/16" alt="Verified" style="width: 16px; height: 16px; vertical-align: middle;"></h3>
                            <div style="font-size: 14px; color: var(--gray-color);">电影爱好者 | 324条评价</div>
                        </div>
                    </div>
                    
                    <div class="tags">
                        <span class="badge badge-primary">电影</span>
                        <span class="badge badge-primary">美食</span>
                        <span class="badge badge-primary">聊天</span>
                    </div>
                    
                    <div class="service-description">
                        <h3 style="margin-bottom: 10px;">服务介绍</h3>
                        <p>
                            提供专业的电影陪看服务，擅长各类型电影解析与讨论。可线上或线下，根据您的喜好推荐电影，并在观影过程中提供专业的电影知识讲解。观影后可进行深度讨论，分享彼此的观影感受。
                        </p>
                    </div>
                    
                    <div class="divider"></div>
                    
                    <div class="service-details">
                        <div class="detail-item">
                            <img src="https://via.placeholder.com/24" alt="Clock" class="icon">
                            <span>服务时长: 2-3小时</span>
                        </div>
                        <div class="detail-item">
                            <img src="https://via.placeholder.com/24" alt="Location" class="icon">
                            <span>服务方式: 线上/线下</span>
                        </div>
                        <div class="detail-item">
                            <img src="https://via.placeholder.com/24" alt="Order" class="icon">
                            <span>完成订单: 78单</span>
                        </div>
                        <div class="detail-item">
                            <img src="https://via.placeholder.com/24" alt="Response" class="icon">
                            <span>响应速度: 5分钟内</span>
                        </div>
                    </div>
                    
                    <div class="divider"></div>
                    
                    <div class="service-reviews">
                        <h3 style="margin-bottom: 16px;">用户评价 (324)</h3>
                        
                        <div class="review-item">
                            <div class="review-header">
                                <img src="https://via.placeholder.com/36" alt="Reviewer" class="avatar">
                                <div class="reviewer-info">
                                    <div class="name">用户15387</div>
                                    <div class="date">2023-10-15</div>
                                </div>
                                <div class="rating" style="margin-left: auto;">★★★★★</div>
                            </div>
                            <div class="review-content">
                                非常专业的电影讲解，让我对电影有了更深入的理解，下次还会约！
                            </div>
                        </div>
                        
                        <div class="review-item">
                            <div class="review-header">
                                <img src="https://via.placeholder.com/36" alt="Reviewer" class="avatar">
                                <div class="reviewer-info">
                                    <div class="name">小王同学</div>
                                    <div class="date">2023-10-10</div>
                                </div>
                                <div class="rating" style="margin-left: auto;">★★★★☆</div>
                            </div>
                            <div class="review-content">
                                服务很好，推荐的电影很合我口味，聊天也很愉快，就是时间有点短。
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="fixed-bottom">
                <div style="display: flex; flex-direction: column; justify-content: center;">
                    <div class="price" style="font-size: 20px;">
                        ¥188 <span class="unit">/ 小时</span>
                    </div>
                    <div style="font-size: 12px; color: var(--gray-color);">
                        可用时间: 今日19:00后
                    </div>
                </div>
                <div style="display: flex; gap: 10px;">
                    <button class="btn btn-outline" onclick="switchScreen('chat-screen')">联系TA</button>
                    <button class="btn btn-primary">立即预约</button>
                </div>
            </div>
        </div>
        
        <!-- 消息列表 -->
        <div class="screen" id="chat-list-screen">
            <div class="header">
                <div class="page-title">消息</div>
                <img src="https://via.placeholder.com/24" alt="Setting" class="icon">
            </div>
            
            <div class="content">
                <div class="search-bar" style="margin: 16px;">
                    <img src="https://via.placeholder.com/18" alt="Search" class="icon" style="width: 18px; height: 18px;">
                    <input type="text" placeholder="搜索聊天记录">
                </div>
                
                <div style="display: flex; padding: 0 16px 16px;">
                    <span class="badge badge-primary" style="margin-right: 10px;">全部</span>
                    <span class="badge badge-secondary">未读消息</span>
                </div>
                
                <div onclick="switchScreen('chat-screen')">
                    <div class="companion-card">
                        <img src="https://via.placeholder.com/50" alt="Avatar" class="avatar">
                        <div class="companion-info">
                            <div style="display: flex; justify-content: space-between;">
                                <div class="companion-name">小林</div>
                                <span style="font-size: 12px; color: var(--gray-color);">14:32</span>
                            </div>
                            <p style="font-size: 14px; color: var(--gray-color); margin-top: 4px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                好的，我明天下午有空，可以一起去看那部新电影
                            </p>
                        </div>
                    </div>
                    
                    <div class="companion-card">
                        <img src="https://via.placeholder.com/50" alt="Avatar" class="avatar">
                        <div class="companion-info">
                            <div style="display: flex; justify-content: space-between;">
                                <div class="companion-name">王小美</div>
                                <span style="font-size: 12px; color: var(--gray-color);">昨天</span>
                            </div>
                            <p style="font-size: 14px; color: var(--gray-color); margin-top: 4px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                [图片] 这是我上次去的餐厅，味道很不错，下次可以一起去
                            </p>
                        </div>
                    </div>
                    
                    <div class="companion-card">
                        <img src="https://via.placeholder.com/50" alt="Avatar" class="avatar">
                        <div class="companion-info">
                            <div style="display: flex; justify-content: space-between;">
                                <div class="companion-name">张先生</div>
                                <span style="font-size: 12px; color: var(--gray-color);">周一</span>
                            </div>
                            <p style="font-size: 14px; color: var(--gray-color); margin-top: 4px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                谢谢你的辅导，物理题我现在明白多了
                            </p>
                        </div>
                    </div>
                    
                    <div class="companion-card">
                        <img src="https://via.placeholder.com/50" alt="System" class="avatar">
                        <div class="companion-info">
                            <div style="display: flex; justify-content: space-between;">
                                <div class="companion-name">系统通知</div>
                                <span style="font-size: 12px; color: var(--gray-color);">10-12</span>
                            </div>
                            <p style="font-size: 14px; color: var(--gray-color); margin-top: 4px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                您的实名认证已通过审核，现在可以使用全部功能了
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bottom-nav">
                <a href="#" class="nav-item" onclick="switchScreen('home-screen')">
                    <img src="https://via.placeholder.com/24" alt="Home" class="icon">
                    <span>首页</span>
                </a>
                <a href="#" class="nav-item" onclick="switchScreen('home-screen')">
                    <img src="https://via.placeholder.com/24" alt="Discover" class="icon">
                    <span>发现</span>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-publish">
                        <img src="https://via.placeholder.com/24" alt="Publish" class="icon" style="filter: brightness(0) invert(1);">
                    </div>
                    <span>发布</span>
                </a>
                <a href="#" class="nav-item active" onclick="switchScreen('chat-list-screen')">
                    <img src="https://via.placeholder.com/24" alt="Message" class="icon">
                    <span>消息</span>
                </a>
                <a href="#" class="nav-item" onclick="switchScreen('profile-screen')">
                    <img src="https://via.placeholder.com/24" alt="Profile" class="icon">
                    <span>我的</span>
                </a>
            </div>
        </div>
        
        <!-- 聊天详情 -->
        <div class="screen" id="chat-screen">
            <div class="chat-container">
                <div class="chat-header">
                    <img src="https://via.placeholder.com/24" alt="Back" class="icon" onclick="switchScreen('chat-list-screen')">
                    <img src="https://via.placeholder.com/40" alt="Avatar" class="avatar" style="width: 40px; height: 40px;">
                    <div>
                        <div class="name">小林</div>
                        <div class="status">在线</div>
                    </div>
                    <div class="actions">
                        <img src="https://via.placeholder.com/24" alt="Call" class="icon">
                        <img src="https://via.placeholder.com/24" alt="Video" class="icon">
                        <img src="https://via.placeholder.com/24" alt="More" class="icon">
                    </div>
                </div>
                
                <div class="chat-messages">
                    <div class="message received">
                        <div class="message-content">
                            您好，很高兴为您提供陪看电影服务，请问您想看什么类型的电影呢？
                        </div>
                        <div class="message-time">14:25</div>
                    </div>
                    
                    <div class="message sent">
                      <div class="message-content">
                          你好，我想看最近新上映的科幻电影，有什么推荐吗？
                      </div>
                      <div class="message-time">14:27</div>
                  </div>
                  
                  <div class="message received">
                      <div class="message-content">
                          最近有部《沙丘2》非常不错，获得了很高的评价。还有《明日边缘2》也是很精彩的科幻片，您对哪个更感兴趣呢？
                      </div>
                      <div class="message-time">14:29</div>
                  </div>
                  
                  <div class="message sent">
                      <div class="message-content">
                          《沙丘2》听起来不错，这周末有时间一起看吗？
                      </div>
                      <div class="message-time">14:30</div>
                  </div>
                  
                  <div class="message received">
                      <div class="message-content">
                          好的，我明天下午有空，可以一起去看那部新电影。您想去哪个影院呢？
                      </div>
                      <div class="message-time">14:32</div>
                  </div>
              </div>
              
              <div class="chat-input">
                  <img src="https://via.placeholder.com/24" alt="Plus" class="icon">
                  <input type="text" class="input-field" placeholder="发送消息...">
                  <img src="https://via.placeholder.com/24" alt="Emoji" class="icon">
                  <img src="https://via.placeholder.com/24" alt="Send" class="icon">
              </div>
          </div>
      </div>
      
      <!-- 个人资料页 -->
      <div class="screen" id="profile-screen">
          <div class="profile-header">
              <img src="https://via.placeholder.com/100" alt="User Avatar" class="profile-avatar">
              <h2>用户名</h2>
              <p style="margin-top: 8px; opacity: 0.8;">ID: 10086123</p>
              
              <div class="profile-stats">
                  <div class="stat-item">
                      <div class="stat-value">12</div>
                      <div class="stat-label">收藏</div>
                  </div>
                  <div class="stat-item">
                      <div class="stat-value">28</div>
                      <div class="stat-label">关注</div>
                  </div>
                  <div class="stat-item">
                      <div class="stat-value">56</div>
                      <div class="stat-label">粉丝</div>
                  </div>
              </div>
          </div>
          
          <div class="content">
              <div style="padding: 16px; background-color: white; margin-bottom: 10px; display: flex; justify-content: space-around;">
                  <div style="display: flex; flex-direction: column; align-items: center;">
                      <img src="https://via.placeholder.com/24" alt="Wallet" class="icon">
                      <span style="font-size: 14px; margin-top: 4px;">钱包</span>
                  </div>
                  <div style="display: flex; flex-direction: column; align-items: center;">
                      <img src="https://via.placeholder.com/24" alt="Order" class="icon">
                      <span style="font-size: 14px; margin-top: 4px;">订单</span>
                  </div>
                  <div style="display: flex; flex-direction: column; align-items: center;">
                      <img src="https://via.placeholder.com/24" alt="Coupon" class="icon">
                      <span style="font-size: 14px; margin-top: 4px;">优惠券</span>
                  </div>
                  <div style="display: flex; flex-direction: column; align-items: center;">
                      <img src="https://via.placeholder.com/24" alt="Service" class="icon">
                      <span style="font-size: 14px; margin-top: 4px;">客服</span>
                  </div>
              </div>
              
              <div style="display: flex; padding: 16px; background-color: white; margin-bottom: 10px; align-items: center; justify-content: space-between;">
                  <div style="display: flex; align-items: center;">
                      <img src="https://via.placeholder.com/24" alt="Identity" class="icon" style="margin-right: 12px;">
                      <span>身份切换</span>
                  </div>
                  <div style="display: flex; align-items: center;">
                      <span style="margin-right: 10px; color: var(--primary-color);">需求方</span>
                      <img src="https://via.placeholder.com/24" alt="Switch" class="icon">
                  </div>
              </div>
              
              <div class="menu-list">
                  <div class="menu-item">
                      <img src="https://via.placeholder.com/24" alt="Verify" class="icon">
                      <span>实名认证</span>
                      <span style="margin-left: auto; color: var(--success-color); font-size: 14px;">已认证</span>
                      <img src="https://via.placeholder.com/16" alt="Arrow" class="arrow" style="margin-left: 10px;">
                  </div>
                  
                  <div class="menu-item">
                      <img src="https://via.placeholder.com/24" alt="Security" class="icon">
                      <span>安全中心</span>
                      <img src="https://via.placeholder.com/16" alt="Arrow" class="arrow" style="margin-left: auto;">
                  </div>
                  
                  <div class="menu-item">
                      <img src="https://via.placeholder.com/24" alt="Privacy" class="icon">
                      <span>隐私设置</span>
                      <img src="https://via.placeholder.com/16" alt="Arrow" class="arrow" style="margin-left: auto;">
                  </div>
                  
                  <div class="menu-item">
                      <img src="https://via.placeholder.com/24" alt="History" class="icon">
                      <span>浏览历史</span>
                      <img src="https://via.placeholder.com/16" alt="Arrow" class="arrow" style="margin-left: auto;">
                  </div>
                  
                  <div class="menu-item">
                      <img src="https://via.placeholder.com/24" alt="Feedback" class="icon">
                      <span>意见反馈</span>
                      <img src="https://via.placeholder.com/16" alt="Arrow" class="arrow" style="margin-left: auto;">
                  </div>
                  
                  <div class="menu-item">
                      <img src="https://via.placeholder.com/24" alt="About" class="icon">
                      <span>关于我们</span>
                      <img src="https://via.placeholder.com/16" alt="Arrow" class="arrow" style="margin-left: auto;">
                  </div>
                  
                  <div class="menu-item">
                      <img src="https://via.placeholder.com/24" alt="Setting" class="icon">
                      <span>设置</span>
                      <img src="https://via.placeholder.com/16" alt="Arrow" class="arrow" style="margin-left: auto;">
                  </div>
              </div>
          </div>
          
          <div class="bottom-nav">
              <a href="#" class="nav-item" onclick="switchScreen('home-screen')">
                  <img src="https://via.placeholder.com/24" alt="Home" class="icon">
                  <span>首页</span>
              </a>
              <a href="#" class="nav-item" onclick="switchScreen('home-screen')">
                  <img src="https://via.placeholder.com/24" alt="Discover" class="icon">
                  <span>发现</span>
              </a>
              <a href="#" class="nav-item">
                  <div class="nav-publish">
                      <img src="https://via.placeholder.com/24" alt="Publish" class="icon" style="filter: brightness(0) invert(1);">
                  </div>
                  <span>发布</span>
              </a>
              <a href="#" class="nav-item" onclick="switchScreen('chat-list-screen')">
                  <img src="https://via.placeholder.com/24" alt="Message" class="icon">
                  <span>消息</span>
              </a>
              <a href="#" class="nav-item active" onclick="switchScreen('profile-screen')">
                  <img src="https://via.placeholder.com/24" alt="Profile" class="icon">
                  <span>我的</span>
              </a>
          </div>
      </div>
      
      <!-- 发布需求页面 -->
      <div class="screen" id="publish-screen">
          <div class="header">
              <img src="https://via.placeholder.com/24" alt="Close" class="icon" onclick="switchScreen('home-screen')">
              <div class="page-title">发布需求</div>
              <button class="btn btn-primary" style="padding: 6px 12px; font-size: 14px;">发布</button>
          </div>
          
          <div class="content">
              <div style="padding: 16px;">
                  <div class="form-group">
                      <label style="display: block; margin-bottom: 8px; font-weight: 500;">服务类型</label>
                      <div style="display: flex; flex-wrap: wrap; gap: 10px;">
                          <span class="badge badge-primary" style="padding: 8px 16px;">陪看电影</span>
                          <span class="badge badge-secondary" style="padding: 8px 16px;">陪吃饭</span>
                          <span class="badge badge-secondary" style="padding: 8px 16px;">陪打游戏</span>
                          <span class="badge badge-secondary" style="padding: 8px 16px;">陪聊天</span>
                          <span class="badge badge-secondary" style="padding: 8px 16px;">陪旅行</span>
                          <span class="badge badge-secondary" style="padding: 8px 16px;">陪学习</span>
                      </div>
                  </div>
                  
                  <div class="form-group">
                      <label style="display: block; margin-bottom: 8px; font-weight: 500;">标题</label>
                      <input type="text" class="form-control" placeholder="简单描述您的需求">
                  </div>
                  
                  <div class="form-group">
                      <label style="display: block; margin-bottom: 8px; font-weight: 500;">详细描述</label>
                      <textarea class="form-control" style="height: 120px;" placeholder="详细描述您的需求，以便更好地匹配合适的陪伴者"></textarea>
                  </div>
                  
                  <div class="form-group">
                      <label style="display: block; margin-bottom: 8px; font-weight: 500;">时间</label>
                      <div style="display: flex; gap: 10px;">
                          <input type="date" class="form-control" style="flex: 1;">
                          <input type="time" class="form-control" style="flex: 1;">
                      </div>
                  </div>
                  
                  <div class="form-group">
                      <label style="display: block; margin-bottom: 8px; font-weight: 500;">持续时长</label>
                      <select class="form-control">
                          <option>1小时</option>
                          <option>2小时</option>
                          <option>3小时</option>
                          <option>4小时</option>
                          <option>自定义</option>
                      </select>
                  </div>
                  
                  <div class="form-group">
                      <label style="display: block; margin-bottom: 8px; font-weight: 500;">地点</label>
                      <div class="search-bar" style="margin-bottom: 10px;">
                          <img src="https://via.placeholder.com/18" alt="Location" class="icon" style="width: 18px; height: 18px;">
                          <input type="text" placeholder="选择地点或输入地址">
                      </div>
                      <div style="height: 120px; background-color: #F9FAFB; border-radius: var(--border-radius); display: flex; align-items: center; justify-content: center;">
                          <span style="color: var(--gray-color);">地图预览</span>
                      </div>
                  </div>
                  
                  <div class="form-group">
                      <label style="display: block; margin-bottom: 8px; font-weight: 500;">价格</label>
                      <div style="display: flex; align-items: center;">
                          <span style="margin-right: 10px;">¥</span>
                          <input type="number" class="form-control" placeholder="您愿意支付的小时价格">
                          <span style="margin-left: 10px;">/小时</span>
                      </div>
                  </div>
                  
                  <div class="form-group">
                      <label style="display: block; margin-bottom: 8px; font-weight: 500;">添加照片</label>
                      <div style="display: flex; gap: 10px;">
                          <div style="width: 80px; height: 80px; background-color: #F9FAFB; border-radius: var(--border-radius); display: flex; align-items: center; justify-content: center; border: 1px dashed #E5E7EB;">
                              <img src="https://via.placeholder.com/24" alt="Plus" class="icon">
                          </div>
                      </div>
                  </div>
              </div>
          </div>
      </div>
  </div>
  
  <script>
      function switchScreen(screenId) {
          // 隐藏所有屏幕
          document.querySelectorAll('.screen').forEach(screen => {
              screen.classList.remove('active');
          });
          
          // 显示指定屏幕
          document.getElementById(screenId).classList.add('active');
      }
      
      // 添加延迟，模拟启动屏幕
      setTimeout(() => {
          // 如果用户已登录，则直接进入首页，否则显示登录页
          // switchScreen('home-screen');
      }, 2000);
  </script>
</body>
</html>