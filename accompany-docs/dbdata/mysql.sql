-------------------- ✅ 第一步，账号与认证系统 --------------------
-- 用户基础表
CREATE TABLE users (
    user_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID，主键',
    user_name VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名(昵称)，唯一',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希值',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱，唯一',
    phone VARCHAR(20) COMMENT '手机号码',
    avatar_url VARCHAR(255) COMMENT '头像URL',
    user_type VARCHAR(20) NOT NULL COMMENT '用户类型 (normal-普通用户|companion-陪伴者|admin-管理员)',
    gender VARCHAR(20) COMMENT '性别 (male-男性|female-女性|other-其他)',
    birth_date DATE COMMENT '出生日期',
    city VARCHAR(50) COMMENT '所在城市',
    self_introduction TEXT COMMENT '自我介绍',
    voice_intro_url VARCHAR(255) COMMENT '语音介绍URL',
    personal_tags VARCHAR(255) COMMENT '个人标签，逗号分隔',
    profile_images JSON COMMENT '个人相册图片URLs',
    background_image VARCHAR(255) COMMENT '主页背景图',
    privacy_settings JSON COMMENT '隐私设置(JSON格式)',
    is_verified BOOLEAN DEFAULT FALSE COMMENT '是否已认证',
    verified_at DATETIME COMMENT '认证通过时间',
    registration_ip VARCHAR(45) COMMENT '注册IP地址',
    user_ip VARCHAR(45) COMMENT '用户IP地址',
    last_login_at DATETIME COMMENT '最后登录时间',
    last_active_at DATETIME COMMENT '最后活跃时间',
    account_status VARCHAR(20) DEFAULT 'active' COMMENT '账号状态 (active-活跃|suspended-暂停|banned-封禁)',
    total_points INT DEFAULT 0 COMMENT '总积分',
    current_level_id BIGINT COMMENT '当前等级ID',
    referral_code VARCHAR(20) UNIQUE COMMENT '个人推荐码',
    referred_by BIGINT COMMENT '推荐人ID',
    credit_score INT DEFAULT 100 COMMENT '信用分（100为初始分）',
    total_orders INT DEFAULT 0 COMMENT '总订单数',
    completion_rate DECIMAL(4,2) DEFAULT 100.00 COMMENT '完成率（百分比）',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (current_level_id) REFERENCES user_levels(level_id),
    FOREIGN KEY (referred_by) REFERENCES users(user_id)
) COMMENT '用户基础信息表';

-- 用户认证信息表
CREATE TABLE user_verifications (
    verification_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '认证ID，主键',
    user_id BIGINT NOT NULL COMMENT '关联的用户ID',
    id_card_number VARCHAR(18) UNIQUE COMMENT '身份证号码',
    real_name VARCHAR(50) COMMENT '真实姓名',
    verification_status VARCHAR(20) DEFAULT 'pending' COMMENT '认证状态 (pending-待审核|approved-已通过|rejected-已拒绝|stopped-已停用)',
    face_verification_status VARCHAR(20) DEFAULT 'pending' COMMENT '人脸认证状态 (pending-待认证|passed-已通过|failed-未通过)',
    face_verification_at DATETIME COMMENT '人脸认证时间',
    background_check_status VARCHAR(20) DEFAULT 'not_required' COMMENT '背景调查状态 (not_required-不需要|pending-待调查|passed-已通过|failed-未通过)',
    background_check_at DATETIME COMMENT '背景调查时间',
    professional_certs JSON COMMENT '专业证书信息(JSON格式)',
    education_info JSON COMMENT '教育信息(JSON格式)',
    work_experience JSON COMMENT '工作经历(JSON格式)',
    verification_at DATETIME COMMENT '认证时间',
    reject_reason TEXT COMMENT '拒绝原因',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(user_id)
) COMMENT '用户实名认证信息表';

-- 第三方登录表
CREATE TABLE social_logins (
    social_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '第三方登录ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    provider VARCHAR(20) NOT NULL COMMENT '登录提供商 (wechat-微信|qq-QQ|apple-苹果|alipay-支付宝)',
    provider_user_id VARCHAR(100) NOT NULL COMMENT '第三方用户ID',
    provider_username VARCHAR(100) COMMENT '第三方用户名',
    provider_email VARCHAR(100) COMMENT '第三方邮箱',
    provider_avatar VARCHAR(255) COMMENT '第三方头像',
    access_token TEXT COMMENT '访问令牌',
    refresh_token TEXT COMMENT '刷新令牌',
    expires_at DATETIME COMMENT '令牌过期时间',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY unique_provider_user (provider, provider_user_id) COMMENT '第三方用户唯一索引',
    FOREIGN KEY (user_id) REFERENCES users(user_id)
) COMMENT '第三方登录绑定表';

-- 短信验证码表
CREATE TABLE sms_verification_codes (
    verification_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '验证ID',
    phone VARCHAR(20) NOT NULL COMMENT '手机号',
    code VARCHAR(10) NOT NULL COMMENT '验证码',
    purpose VARCHAR(20) NOT NULL COMMENT '用途 (register-注册|login-登录|reset_password-重置密码|bind_phone-绑定手机)',
    is_used BOOLEAN DEFAULT FALSE COMMENT '是否已使用',
    attempt_count INT DEFAULT 0 COMMENT '尝试次数',
    max_attempts INT DEFAULT 3 COMMENT '最大尝试次数',
    expires_at DATETIME NOT NULL COMMENT '过期时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    used_at DATETIME COMMENT '使用时间',
    INDEX idx_phone_purpose (phone, purpose) COMMENT '手机用途索引',
    INDEX idx_expires (expires_at) COMMENT '过期时间索引'
) COMMENT '短信验证码表';

-- 拉黑名单表-每人拉黑上限100人
CREATE TABLE blacklists (
    blacklist_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '黑名单ID，主键',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    black_user_id BIGINT NOT NULL COMMENT '被拉黑用户ID',
    reason TEXT COMMENT '拉黑原因',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (black_user_id) REFERENCES users(user_id)
) COMMENT '黑名单表';

-- 设备管理表
CREATE TABLE user_devices (
    device_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '设备ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    device_token VARCHAR(255) NOT NULL COMMENT '设备令牌',
    device_type VARCHAR(20) NOT NULL COMMENT '设备类型 (ios|android|web)',
    device_model VARCHAR(100) COMMENT '设备型号',
    os_version VARCHAR(50) COMMENT '系统版本',
    app_version VARCHAR(50) COMMENT 'APP版本',
    push_enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用推送',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否活跃',
    last_login_at DATETIME COMMENT '最后登录时间',
    last_ip VARCHAR(45) COMMENT '最后登录IP',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY unique_device_token (device_token) COMMENT '设备令牌唯一索引',
    INDEX idx_user_active (user_id, is_active) COMMENT '用户活跃设备索引',
    FOREIGN KEY (user_id) REFERENCES users(user_id)
) COMMENT '用户设备管理表';

-- API调用日志表
CREATE TABLE api_logs (
    log_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    user_id BIGINT COMMENT '用户ID',
    api_endpoint VARCHAR(255) NOT NULL COMMENT 'API端点',
    http_method VARCHAR(10) NOT NULL COMMENT 'HTTP方法',
    request_ip VARCHAR(45) COMMENT '请求IP',
    user_agent TEXT COMMENT '用户代理',
    request_params JSON COMMENT '请求参数',
    response_status INT COMMENT '响应状态码',
    response_time_ms INT COMMENT '响应时间(毫秒)',
    error_message TEXT COMMENT '错误信息',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '请求时间',
    INDEX idx_user_time (user_id, created_at) COMMENT '用户时间索引',
    INDEX idx_endpoint_status (api_endpoint, response_status) COMMENT '端点状态索引',
    INDEX idx_created_time (created_at) COMMENT '创建时间索引',
    FOREIGN KEY (user_id) REFERENCES users(user_id)
) COMMENT 'API调用日志表';

-------------------- ✅ 第二步：陪伴服务系统（核心功能） --------------------

-- 陪伴者技能表
CREATE TABLE companion_skills (
    skill_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '技能ID，主键',
    user_id BIGINT NOT NULL COMMENT '关联的用户ID',
    skill_type VARCHAR(50) NOT NULL COMMENT '技能类型',
    skill_level VARCHAR(20) COMMENT '技能水平 (beginner-初级|intermediate-中级|senior-高级|expert-专家)',
    skill_name VARCHAR(50) NOT NULL COMMENT '技能名称',
    description TEXT COMMENT '技能描述',
    is_verified BOOLEAN DEFAULT FALSE COMMENT '是否已验证',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(user_id)
) COMMENT '陪伴者技能信息表';

-- 陪伴服务表
CREATE TABLE accompany_services (
    service_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '服务ID，主键',
    provider_id BIGINT NOT NULL COMMENT '服务提供者ID',
    service_type VARCHAR(20) NOT NULL COMMENT '服务类型 (chat-聊天|game-游戏|study-学习|sport-运动|shopping-购物|travel-旅行|movie-电影|food-美食|other-其他)',
    title VARCHAR(100) NOT NULL COMMENT '服务标题',
    description TEXT COMMENT '服务描述',
    service_images JSON COMMENT '服务图片URLs',
    video_intro_url VARCHAR(255) COMMENT '服务介绍视频URL',
    price DECIMAL(10,2) NOT NULL COMMENT '服务价格',
    time_unit VARCHAR(20) NOT NULL COMMENT '时间单位 (hour-小时|session-次|day-天)',
    min_duration INT NOT NULL COMMENT '最小服务时长',
    max_duration INT COMMENT '最大服务时长',
    location VARCHAR(255) COMMENT '服务地点',
    service_mode VARCHAR(20) NOT NULL DEFAULT 'both' COMMENT '服务模式 (online-线上|offline-线下|both-两者皆可)',
    tags VARCHAR(255) COMMENT '服务标签，逗号分隔',
    availability_schedule JSON COMMENT '可用时间安排(JSON格式)',
    advance_booking_hours INT DEFAULT 2 COMMENT '需要提前预约的小时数',
    auto_accept BOOLEAN DEFAULT FALSE COMMENT '是否自动接受订单',
    cancellation_policy TEXT COMMENT '取消政策说明',
    languages VARCHAR(255) COMMENT '支持的语言，逗号分隔',
    age_requirement VARCHAR(50) COMMENT '年龄要求',
    gender_preference VARCHAR(20) COMMENT '性别偏好 (no_preference-无偏好|male-男性|female-女性)',
    max_group_size INT DEFAULT 1 COMMENT '最大服务人数',
    status VARCHAR(20) DEFAULT 'reviewing' COMMENT '服务状态 (active-活跃|inactive-下架|reviewing-审核中)',
    is_featured BOOLEAN DEFAULT FALSE COMMENT '是否精选服务',
    featured_until DATETIME COMMENT '精选截止时间',
    views_count INT DEFAULT 0 COMMENT '浏览次数',
    orders_count INT DEFAULT 0 COMMENT '订单数量',
    rating_avg DECIMAL(2,1) DEFAULT 0.0 COMMENT '平均评分',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (provider_id) REFERENCES users(user_id)
) COMMENT '陪伴服务信息表';

-- 服务时间段表
CREATE TABLE service_schedules (
    schedule_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '时间段ID，主键',
    user_id BIGINT NOT NULL COMMENT '关联的用户ID',
    service_id BIGINT NOT NULL COMMENT '关联的服务ID',
    day_of_week TINYINT NOT NULL COMMENT '星期几（1-7）',
    start_at TIME NOT NULL COMMENT '开始时间',
    end_at TIME NOT NULL COMMENT '结束时间',
    is_available BOOLEAN DEFAULT TRUE COMMENT '是否可用',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (service_id) REFERENCES accompany_services(service_id)
) COMMENT '服务可用时间表';

-- 服务区域表
CREATE TABLE service_areas (
    area_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '区域ID',
    area_name VARCHAR(100) NOT NULL COMMENT '区域名称',
    area_type VARCHAR(20) NOT NULL COMMENT '区域类型 (country-国家|province-省份|city-城市|district-区县)',
    parent_id BIGINT COMMENT '父级区域ID',
    area_code VARCHAR(20) COMMENT '区域编码',
    center_lat DECIMAL(10,8) COMMENT '中心纬度',
    center_lng DECIMAL(11,8) COMMENT '中心经度',
    radius_km DECIMAL(8,2) COMMENT '服务半径(公里)',
    is_supported BOOLEAN DEFAULT TRUE COMMENT '是否支持服务',
    service_fee_rate DECIMAL(4,2) DEFAULT 0.20 COMMENT '服务费率',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (parent_id) REFERENCES service_areas(area_id)
) COMMENT '服务区域表';

-- 订单表
CREATE TABLE orders (
    order_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '订单ID，主键',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    service_id BIGINT NOT NULL COMMENT '服务ID',
    service_provider_id BIGINT NOT NULL COMMENT '服务提供者ID',
    start_at DATETIME NOT NULL COMMENT '服务开始时间',
    end_at DATETIME NOT NULL COMMENT '服务结束时间',
    actual_start_at DATETIME COMMENT '实际开始时间',
    actual_end_at DATETIME COMMENT '实际结束时间',
    service_duration_minutes INT COMMENT '实际服务时长(分钟)',
    location_address VARCHAR(255) COMMENT '服务地点地址',
    location_lat DECIMAL(10,8) COMMENT '服务地点纬度',
    location_lng DECIMAL(11,8) COMMENT '服务地点经度',
    special_requirements TEXT COMMENT '特殊要求',
    status VARCHAR(20) NOT NULL COMMENT '订单状态 (pending-待支付|paid-已支付|in_progress-进行中|completed-已完成|cancelled-已取消|refunded-已退款)',
    amount DECIMAL(10,2) NOT NULL COMMENT '订单金额',
    platform_fee DECIMAL(10,2) DEFAULT 0.00 COMMENT '平台服务费',
    service_fee DECIMAL(10,2) DEFAULT 0.00 COMMENT '服务费用',
    discount_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '优惠金额',
    tip_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '小费金额',
    refund_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '退款金额',
    cancel_fee DECIMAL(10,2) DEFAULT 0.00 COMMENT '取消费用',
    payment_method VARCHAR(50) COMMENT '支付方式',
    payment_status VARCHAR(20) COMMENT '支付状态 (unpaid-未支付|paid-已支付|refunded-已退款)',
    cancel_reason TEXT COMMENT '取消原因',
    cancelled_by BIGINT COMMENT '取消操作用户ID',
    cancelled_at DATETIME COMMENT '取消时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    completed_at DATETIME COMMENT '完成时间',
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (service_id) REFERENCES accompany_services(service_id),
    FOREIGN KEY (service_provider_id) REFERENCES users(user_id),
    FOREIGN KEY (cancelled_by) REFERENCES users(user_id)
) COMMENT '订单信息表';

-- 用户评价表
CREATE TABLE reviews (
    review_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '评价ID，主键',
    order_id BIGINT NOT NULL COMMENT '关联的订单ID',
    owner_user_id BIGINT NOT NULL COMMENT '评价人自己ID',
    review_user_id BIGINT NOT NULL COMMENT '被评价人ID',
    service_id BIGINT NOT NULL COMMENT '服务ID',
    rating TINYINT NOT NULL CHECK (rating BETWEEN 1 AND 5) COMMENT '评分（1-5星）',
    service_quality_rating TINYINT COMMENT '服务质量评分(1-5)',
    attitude_rating TINYINT COMMENT '服务态度评分(1-5)',
    punctuality_rating TINYINT COMMENT '准时性评分(1-5)',
    professionalism_rating TINYINT COMMENT '专业度评分(1-5)',
    value_for_money_rating TINYINT COMMENT '性价比评分(1-5)',
    content TEXT COMMENT '评价内容',
    review_images JSON COMMENT '评价图片URLs',
    anonymous BOOLEAN DEFAULT FALSE COMMENT '是否匿名评价',
    helpful_count INT DEFAULT 0 COMMENT '有用数量',
    reply_content TEXT COMMENT '回复内容',
    replied_at DATETIME COMMENT '回复时间',
    is_featured BOOLEAN DEFAULT FALSE COMMENT '是否精选评价',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (order_id) REFERENCES orders(order_id),
    FOREIGN KEY (owner_user_id) REFERENCES users(user_id),
    FOREIGN KEY (review_user_id) REFERENCES users(user_id),
    FOREIGN KEY (service_id) REFERENCES accompany_services(service_id)
) COMMENT '用户评价表';

-- 用户行为记录表
CREATE TABLE user_behaviors (
    behavior_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '行为ID，主键',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    behavior_type VARCHAR(20) NOT NULL COMMENT '行为类型 (order_cancel-取消订单|service_reject-拒绝服务|no_show-缺席|late_arrival-迟到|early_leave-早退|positive_review-好评|negative_review-差评)',
    related_order_id BIGINT COMMENT '相关订单ID',
    related_user_id BIGINT COMMENT '相关用户ID',
    description TEXT COMMENT '行为描述',
    severity VARCHAR(20) DEFAULT 'medium' COMMENT '严重程度 (low-轻微|medium-中等|high-严重)',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '记录时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (related_order_id) REFERENCES orders(order_id),
    FOREIGN KEY (related_user_id) REFERENCES users(user_id)
) COMMENT '用户行为记录表';

-- 内容审核表
CREATE TABLE content_moderations (
    moderation_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '审核ID',
    content_type VARCHAR(20) NOT NULL COMMENT '内容类型 (service-服务|post-帖子|comment-评论|image-图片|profile-个人资料)',
    content_id BIGINT NOT NULL COMMENT '内容ID',
    submitter_id BIGINT NOT NULL COMMENT '提交者ID',
    content_text TEXT COMMENT '文本内容',
    content_urls JSON COMMENT '相关文件URLs',
    auto_review_result VARCHAR(20) COMMENT '自动审核结果 (approved-通过|rejected-拒绝|manual-需人工)',
    auto_review_score DECIMAL(3,2) COMMENT '自动审核评分(0-1)',
    manual_review_result VARCHAR(20) COMMENT '人工审核结果 (pending-待审核|approved-通过|rejected-拒绝)',
    reviewer_id BIGINT COMMENT '审核员ID',
    reject_reason TEXT COMMENT '拒绝原因',
    sensitive_words JSON COMMENT '检测到的敏感词',
    reviewed_at DATETIME COMMENT '审核时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_content_type_id (content_type, content_id) COMMENT '内容类型ID索引',
    INDEX idx_submitter (submitter_id) COMMENT '提交者索引',
    INDEX idx_review_status (manual_review_result, created_at) COMMENT '审核状态索引',
    FOREIGN KEY (submitter_id) REFERENCES users(user_id)
) COMMENT '内容审核表';

-------------------- ✅ 第三步：交易支付系统 --------------------

-- 用户钱包表
CREATE TABLE wallets (
    wallet_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '钱包ID，主键',
    user_id BIGINT NOT NULL UNIQUE COMMENT '用户ID',
    balance DECIMAL(10,2) DEFAULT 0.00 COMMENT '可用余额',
    frozen_balance DECIMAL(10,2) DEFAULT 0.00 COMMENT '冻结余额',
    total_income DECIMAL(10,2) DEFAULT 0.00 COMMENT '总收入',
    total_expense DECIMAL(10,2) DEFAULT 0.00 COMMENT '总支出',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(user_id)
) COMMENT '用户钱包表';

-- 交易记录表
CREATE TABLE transactions (
    transaction_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '交易ID，主键',
    transaction_no VARCHAR(50) UNIQUE COMMENT '交易流水号',
    wallet_id BIGINT NOT NULL COMMENT '钱包ID',
    order_id BIGINT COMMENT '关联的订单ID',
    type VARCHAR(20) NOT NULL COMMENT '交易类型 (deposit-充值|withdraw-提现|payment-支付|refund-退款|income-收入)',
    payment_channel VARCHAR(20) COMMENT '支付渠道 (wechat-微信|alipay-支付宝|bank-银行卡|balance-余额)',
    third_party_transaction_id VARCHAR(100) COMMENT '第三方交易ID',
    amount DECIMAL(10,2) NOT NULL COMMENT '交易金额',
    fee_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '手续费',
    actual_amount DECIMAL(10,2) COMMENT '实际到账金额',
    remark TEXT COMMENT '交易备注',
    failed_reason TEXT COMMENT '失败原因',
    status VARCHAR(20) NOT NULL COMMENT '交易状态 (pending-待处理|completed-已完成|failed-失败)',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    completed_at DATETIME COMMENT '完成时间',
    FOREIGN KEY (wallet_id) REFERENCES wallets(wallet_id),
    FOREIGN KEY (order_id) REFERENCES orders(order_id)
) COMMENT '交易记录表';

-- 小费记录表
CREATE TABLE tips (
    tip_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '小费ID，主键',
    order_id BIGINT NOT NULL COMMENT '订单ID',
    giver_id BIGINT NOT NULL COMMENT '给小费者ID',
    receiver_id BIGINT NOT NULL COMMENT '收小费者ID',
    amount DECIMAL(10,2) NOT NULL COMMENT '小费金额',
    reason VARCHAR(255) COMMENT '小费原因',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '给小费时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (order_id) REFERENCES orders(order_id),
    FOREIGN KEY (giver_id) REFERENCES users(user_id),
    FOREIGN KEY (receiver_id) REFERENCES users(user_id)
) COMMENT '小费记录表';

-- 优惠券表
CREATE TABLE coupons (
    coupon_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '优惠券ID，主键',
    coupon_name VARCHAR(100) NOT NULL COMMENT '优惠券名称',
    coupon_type VARCHAR(20) NOT NULL COMMENT '优惠券类型 (discount-折扣|amount-金额|percentage-百分比)',
    discount_value DECIMAL(10,2) NOT NULL COMMENT '优惠值',
    min_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '最小使用金额',
    max_discount DECIMAL(10,2) COMMENT '最大优惠金额',
    total_quantity INT NOT NULL COMMENT '总发放数量',
    used_quantity INT DEFAULT 0 COMMENT '已使用数量',
    valid_from DATETIME NOT NULL COMMENT '有效期开始',
    valid_to DATETIME NOT NULL COMMENT '有效期结束',
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态 (active-活跃|inactive-停用|expired-过期)',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '优惠券基础表';

-- 用户优惠券表
CREATE TABLE user_coupons (
    user_coupon_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户优惠券ID，主键',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    coupon_id BIGINT NOT NULL COMMENT '优惠券ID',
    order_id BIGINT COMMENT '使用的订单ID',
    status VARCHAR(20) DEFAULT 'unused' COMMENT '使用状态 (unused-未使用|used-已使用|expired-已过期)',
    received_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间',
    used_at DATETIME COMMENT '使用时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (coupon_id) REFERENCES coupons(coupon_id),
    FOREIGN KEY (order_id) REFERENCES orders(order_id)
) COMMENT '用户优惠券表';

-- 积分记录表
CREATE TABLE points_records (
    record_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID，主键',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    points INT NOT NULL COMMENT '积分变化（正负数）',
    type VARCHAR(20) NOT NULL COMMENT '积分类型 (earn-获得|spend-消费|expire-过期|adjust-调整)',
    source VARCHAR(20) NOT NULL COMMENT '积分来源 (order_complete-完成订单|review-评价|referral-推荐|daily_check-每日签到|activity-活动|admin_adjust-管理员调整)',
    related_id BIGINT COMMENT '相关ID（订单、评价等）',
    description VARCHAR(255) COMMENT '描述',
    expires_at DATETIME COMMENT '过期时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(user_id)
) COMMENT '积分记录表';

-------------------- ✅ 第四步：赏金任务 + 投诉报警系统 --------------------
-- 赏金任务表
CREATE TABLE bounty_tasks (
    task_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '任务ID，主键',
    publisher_id BIGINT NOT NULL COMMENT '发布者ID',
    title VARCHAR(100) NOT NULL COMMENT '任务标题',
    description TEXT NOT NULL COMMENT '任务描述',
    service_type VARCHAR(20) NOT NULL COMMENT '服务类型 (chat-聊天|game-游戏|study-学习|sport-运动|shopping-购物|travel-旅行|movie-电影|food-美食|other-其他)',
    bounty_amount DECIMAL(10,2) NOT NULL COMMENT '赏金金额',
    location VARCHAR(255) COMMENT '服务地点',
    scheduled_at DATETIME NOT NULL COMMENT '预约时间',
    duration_hours DECIMAL(3,1) COMMENT '预计时长（小时）',
    max_applicants INT DEFAULT 5 COMMENT '最大申请人数',
    status VARCHAR(20) DEFAULT 'open' COMMENT '任务状态 (open-开放|assigned-已分配|in_progress-进行中|completed-已完成|cancelled-已取消|expired-已过期)',
    priority VARCHAR(20) DEFAULT 'medium' COMMENT '紧急程度 (low-低|medium-中|high-高|urgent-紧急)',
    requirements TEXT COMMENT '特殊要求',
    tags VARCHAR(255) COMMENT '标签，逗号分隔',
    expires_at DATETIME COMMENT '任务过期时间',
    assigned_to BIGINT COMMENT '被分配的陪伴者ID',
    assigned_at DATETIME COMMENT '分配时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (publisher_id) REFERENCES users(user_id),
    FOREIGN KEY (assigned_to) REFERENCES users(user_id)
) COMMENT '赏金任务表';

-- 任务申请表
CREATE TABLE task_applications (
    application_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '申请ID，主键',
    task_id BIGINT NOT NULL COMMENT '任务ID',
    applicant_id BIGINT NOT NULL COMMENT '申请者ID',
    application_message TEXT COMMENT '申请留言',
    status VARCHAR(20) DEFAULT 'pending' COMMENT '申请状态 (pending-待审核|approved-已通过|rejected-已拒绝|withdrawn-已撤回)',
    quoted_price DECIMAL(10,2) COMMENT '报价（可选）',
    estimated_duration DECIMAL(3,1) COMMENT '预估时长',
    response_message TEXT COMMENT '回复消息',
    responded_at DATETIME COMMENT '回复时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY unique_task_applicant (task_id, applicant_id) COMMENT '任务申请者唯一索引',
    FOREIGN KEY (task_id) REFERENCES bounty_tasks(task_id),
    FOREIGN KEY (applicant_id) REFERENCES users(user_id)
) COMMENT '任务申请表';

-- 投诉举报表
CREATE TABLE complaints (
    complaint_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '投诉ID，主键',
    user_id BIGINT NOT NULL COMMENT '投诉人ID',
    target_id BIGINT NOT NULL COMMENT '被投诉对象ID',
    order_id BIGINT COMMENT '关联的订单ID',
    type VARCHAR(20) NOT NULL COMMENT '投诉类型 (service-服务|user-用户|chat-聊天|other-其他)',
    priority VARCHAR(20) DEFAULT 'medium' COMMENT '优先级 (low-低|medium-中|high-高|urgent-紧急)',
    content TEXT NOT NULL COMMENT '投诉内容',
    evidence_urls JSON COMMENT '证据文件URLs',
    status VARCHAR(20) DEFAULT 'pending' COMMENT '处理状态 (pending-待处理|processing-处理中|resolved-已解决|rejected-已拒绝)',
    handler_id BIGINT COMMENT '处理人ID',
    processing_notes TEXT COMMENT '处理记录',
    resolution_result TEXT COMMENT '处理结果',
    compensation_amount DECIMAL(10,2) COMMENT '赔偿金额',
    follow_up_required BOOLEAN DEFAULT FALSE COMMENT '是否需要后续跟进',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    resolved_at DATETIME COMMENT '解决时间',
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (target_id) REFERENCES users(user_id),
    FOREIGN KEY (order_id) REFERENCES orders(order_id)
) COMMENT '投诉举报表';

-- 紧急求助记录表
CREATE TABLE emergency_requests (
    request_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '求助ID，主键',
    user_id BIGINT NOT NULL COMMENT '求助用户ID',
    order_id BIGINT COMMENT '相关订单ID',
    location_lat DECIMAL(10,8) COMMENT '纬度',
    location_lng DECIMAL(11,8) COMMENT '经度',
    location_address VARCHAR(255) COMMENT '地址描述',
    emergency_type VARCHAR(20) NOT NULL COMMENT '紧急类型 (safety-安全|medical-医疗|harassment-骚扰|fraud-欺诈|other-其他)',
    description TEXT COMMENT '求助描述',
    status VARCHAR(20) DEFAULT 'pending' COMMENT '处理状态 (pending-待处理|processing-处理中|resolved-已解决|false_alarm-误报)',
    handler_id BIGINT COMMENT '处理人员ID',
    resolved_at DATETIME COMMENT '解决时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '求助时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (order_id) REFERENCES orders(order_id)
) COMMENT '紧急求助记录表';

-------------------- ✅ 第五步：社交互动系统 --------------------

-- 收藏表
CREATE TABLE favorites (
    favorite_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '收藏ID，主键',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    service_id BIGINT NOT NULL COMMENT '服务ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY unique_user_service (user_id, service_id) COMMENT '用户服务唯一索引',
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (service_id) REFERENCES accompany_services(service_id)
) COMMENT '用户收藏表';

-- 关注表
CREATE TABLE follows (
    follow_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关注ID，主键',
    follower_id BIGINT NOT NULL COMMENT '关注者ID',
    following_id BIGINT NOT NULL COMMENT '被关注者ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '关注时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY unique_follow (follower_id, following_id) COMMENT '关注关系唯一索引',
    FOREIGN KEY (follower_id) REFERENCES users(user_id),
    FOREIGN KEY (following_id) REFERENCES users(user_id)
) COMMENT '用户关注表';

-- 浏览历史表
CREATE TABLE browse_history (
    history_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '历史记录ID，主键',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    service_id BIGINT NOT NULL COMMENT '服务ID',
    browse_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '浏览时间',
    stay_duration INT DEFAULT 0 COMMENT '停留时长（秒）',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_time (user_id, browse_at) COMMENT '用户时间索引',
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (service_id) REFERENCES accompany_services(service_id)
) COMMENT '浏览历史表';

-- 社区帖子表
CREATE TABLE community_posts (
    post_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '帖子ID，主键',
    author_id BIGINT NOT NULL COMMENT '作者ID',
    title VARCHAR(100) NOT NULL COMMENT '帖子标题',
    content TEXT NOT NULL COMMENT '帖子内容',
    post_type VARCHAR(20) NOT NULL COMMENT '帖子类型 (experience-体验分享|activity-活动|question-问题|recommendation-推荐)',
    category VARCHAR(50) COMMENT '分类',
    tags VARCHAR(255) COMMENT '标签',
    images JSON COMMENT '图片URLs',
    likes_count INT DEFAULT 0 COMMENT '点赞数',
    comments_count INT DEFAULT 0 COMMENT '评论数',
    views_count INT DEFAULT 0 COMMENT '浏览数',
    status VARCHAR(20) DEFAULT 'published' COMMENT '状态 (published-已发布|hidden-隐藏|deleted-已删除)',
    is_pinned BOOLEAN DEFAULT FALSE COMMENT '是否置顶',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (author_id) REFERENCES users(user_id)
) COMMENT '社区帖子表';

-- 帖子评论表
CREATE TABLE post_comments (
    comment_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '评论ID，主键',
    post_id BIGINT NOT NULL COMMENT '帖子ID',
    user_id BIGINT NOT NULL COMMENT '评论用户ID',
    parent_id BIGINT COMMENT '父评论ID（回复功能）',
    content TEXT NOT NULL COMMENT '评论内容',
    likes_count INT DEFAULT 0 COMMENT '点赞数',
    status VARCHAR(20) DEFAULT 'published' COMMENT '状态 (published-已发布|hidden-隐藏|deleted-已删除)',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '评论时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (post_id) REFERENCES community_posts(post_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (parent_id) REFERENCES post_comments(comment_id)
) COMMENT '帖子评论表';

-- 用户等级表
CREATE TABLE user_levels (
    level_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '等级ID，主键',
    level_name VARCHAR(50) NOT NULL COMMENT '等级名称',
    min_points INT NOT NULL COMMENT '最低积分要求',
    max_points INT COMMENT '最高积分限制',
    level_benefits JSON COMMENT '等级权益（JSON格式）',
    badge_icon VARCHAR(255) COMMENT '等级徽章图标',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '用户等级表';

-- 用户推荐邀请表
CREATE TABLE user_referrals (
    referral_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '推荐ID',
    referrer_id BIGINT NOT NULL COMMENT '推荐人ID',
    referee_id BIGINT COMMENT '被推荐人ID',
    referral_code VARCHAR(20) NOT NULL UNIQUE COMMENT '推荐码',
    referee_phone VARCHAR(20) COMMENT '被推荐人手机号',
    referee_email VARCHAR(100) COMMENT '被推荐人邮箱',
    status VARCHAR(20) DEFAULT 'pending' COMMENT '状态 (pending-待注册|registered-已注册|completed-已完成|expired-已过期)',
    reward_type VARCHAR(20) COMMENT '奖励类型 (points-积分|coupon-优惠券|cash-现金)',
    reward_amount DECIMAL(10,2) COMMENT '奖励金额',
    reward_given BOOLEAN DEFAULT FALSE COMMENT '是否已发放奖励',
    completed_at DATETIME COMMENT '完成时间',
    expires_at DATETIME COMMENT '过期时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (referrer_id) REFERENCES users(user_id),
    FOREIGN KEY (referee_id) REFERENCES users(user_id)
) COMMENT '用户推荐邀请表';

-------------------- ✅ 第六步：活动与兴趣小组模块 --------------------
-- 活动管理表
CREATE TABLE activities (
    activity_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '活动ID',
    organizer_id BIGINT NOT NULL COMMENT '组织者ID',
    title VARCHAR(100) NOT NULL COMMENT '活动标题',
    description TEXT COMMENT '活动描述',
    activity_type VARCHAR(20) NOT NULL COMMENT '活动类型 (online-线上|offline-线下|hybrid-混合)',
    category VARCHAR(50) COMMENT '活动分类',
    location VARCHAR(255) COMMENT '活动地点',
    location_lat DECIMAL(10,8) COMMENT '纬度',
    location_lng DECIMAL(11,8) COMMENT '经度',
    start_at DATETIME NOT NULL COMMENT '开始时间',
    end_at DATETIME NOT NULL COMMENT '结束时间',
    registration_start DATETIME COMMENT '报名开始时间',
    registration_end DATETIME COMMENT '报名结束时间',
    max_participants INT COMMENT '最大参与人数',
    current_participants INT DEFAULT 0 COMMENT '当前参与人数',
    fee DECIMAL(10,2) DEFAULT 0.00 COMMENT '参与费用',
    status VARCHAR(20) DEFAULT 'draft' COMMENT '活动状态 (draft-草稿|published-已发布|ongoing-进行中|completed-已完成|cancelled-已取消)',
    cover_image VARCHAR(255) COMMENT '封面图片',
    images JSON COMMENT '活动图片',
    tags VARCHAR(255) COMMENT '活动标签',
    requirements TEXT COMMENT '参与要求',
    is_featured BOOLEAN DEFAULT FALSE COMMENT '是否精选活动',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (organizer_id) REFERENCES users(user_id)
) COMMENT '活动管理表';

-- 活动报名表
CREATE TABLE activity_registrations (
    registration_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '报名ID',
    activity_id BIGINT NOT NULL COMMENT '活动ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    registration_message TEXT COMMENT '报名留言',
    status VARCHAR(20) DEFAULT 'pending' COMMENT '报名状态 (pending-待审核|approved-已通过|rejected-已拒绝|cancelled-已取消)',
    payment_status VARCHAR(20) DEFAULT 'unpaid' COMMENT '支付状态 (unpaid-未支付|paid-已支付|refunded-已退款)',
    check_in_status VARCHAR(20) DEFAULT 'not_checked' COMMENT '签到状态 (not_checked-未签到|checked_in-已签到|absent-缺席)',
    check_in_at DATETIME COMMENT '签到时间',
    rating TINYINT COMMENT '活动评分(1-5)',
    feedback TEXT COMMENT '活动反馈',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '报名时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY unique_activity_user (activity_id, user_id) COMMENT '活动用户唯一索引',
    FOREIGN KEY (activity_id) REFERENCES activities(activity_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id)
) COMMENT '活动报名表';

-- 兴趣小组表
CREATE TABLE interest_groups (
    group_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '小组ID',
    creator_id BIGINT NOT NULL COMMENT '创建者ID',
    group_name VARCHAR(100) NOT NULL COMMENT '小组名称',
    description TEXT COMMENT '小组描述',
    group_type VARCHAR(20) NOT NULL COMMENT '小组类型 (interest-兴趣|location-地域|skill-技能|other-其他)',
    category VARCHAR(50) COMMENT '分类',
    tags VARCHAR(255) COMMENT '标签',
    avatar_url VARCHAR(255) COMMENT '小组头像',
    cover_image VARCHAR(255) COMMENT '封面图片',
    member_limit INT DEFAULT 1000 COMMENT '成员上限',
    current_members INT DEFAULT 1 COMMENT '当前成员数',
    join_policy VARCHAR(20) DEFAULT 'approval' COMMENT '加入策略 (open-开放|approval-审核|invite-邀请)',
    is_public BOOLEAN DEFAULT TRUE COMMENT '是否公开',
    status VARCHAR(20) DEFAULT 'active' COMMENT '小组状态 (active-活跃|inactive-不活跃|disbanded-已解散)',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (creator_id) REFERENCES users(user_id)
) COMMENT '兴趣小组表';

-- 小组成员表
CREATE TABLE group_members (
    membership_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '成员关系ID',
    group_id BIGINT NOT NULL COMMENT '小组ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role VARCHAR(20) DEFAULT 'member' COMMENT '角色 (creator-创建者|admin-管理员|member-成员)',
    join_status VARCHAR(20) DEFAULT 'active' COMMENT '状态 (pending-待审核|active-活跃|banned-被禁)',
    join_message TEXT COMMENT '申请加入留言',
    joined_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
    last_active_at DATETIME COMMENT '最后活跃时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY unique_group_user (group_id, user_id) COMMENT '小组用户唯一索引',
    FOREIGN KEY (group_id) REFERENCES interest_groups(group_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id)
) COMMENT '小组成员表';

-------------------- ✅ 第七步：系统配置与消息体系 --------------------
-- 系统配置表
CREATE TABLE system_configs (
    config_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    config_type VARCHAR(20) NOT NULL COMMENT '配置类型 (string|int|float|boolean|json)',
    category VARCHAR(50) COMMENT '配置分类',
    description TEXT COMMENT '配置描述',
    is_editable BOOLEAN DEFAULT TRUE COMMENT '是否可编辑',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开(客户端可见)',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '系统配置表';

-- 系统消息模板表
CREATE TABLE message_templates (
    template_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '模板ID',
    template_code VARCHAR(50) NOT NULL UNIQUE COMMENT '模板编码',
    template_name VARCHAR(100) NOT NULL COMMENT '模板名称',
    template_type VARCHAR(20) NOT NULL COMMENT '模板类型 (sms-短信|email-邮件|push-推送|system-系统消息)',
    subject VARCHAR(200) COMMENT '消息主题',
    content TEXT NOT NULL COMMENT '消息内容',
    variables JSON COMMENT '变量说明',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '系统消息模板表';

-- 位置共享记录表
CREATE TABLE location_shares (
    share_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '共享ID，主键',
    order_id BIGINT NOT NULL COMMENT '订单ID',
    sharer_id BIGINT NOT NULL COMMENT '共享者ID',
    shared_with_id BIGINT COMMENT '共享给谁（可为空表示公开）',
    location_lat DECIMAL(10,8) NOT NULL COMMENT '纬度',
    location_lng DECIMAL(11,8) NOT NULL COMMENT '经度',
    location_address VARCHAR(255) COMMENT '地址描述',
    share_type VARCHAR(20) NOT NULL COMMENT '共享类型 (real_time-实时共享|check_in-签到|emergency-紧急共享)',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否活跃',
    expires_at DATETIME COMMENT '过期时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '共享时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (order_id) REFERENCES orders(order_id),
    FOREIGN KEY (sharer_id) REFERENCES users(user_id),
    FOREIGN KEY (shared_with_id) REFERENCES users(user_id)
) COMMENT '位置共享记录表';

-------------------- ✅ 第八步：索引创建 --------------------

CREATE INDEX idx_users_email ON users(email) COMMENT '用户邮箱索引';
CREATE INDEX idx_users_phone ON users(phone) COMMENT '用户手机索引';
CREATE INDEX idx_users_type_status ON users(user_type, account_status) COMMENT '用户类型状态索引';

CREATE INDEX idx_blacklists_user ON blacklists(user_id) COMMENT '黑名单用户索引';
CREATE INDEX idx_blacklists_black_user ON blacklists(black_user_id) COMMENT '被拉黑用户索引';

CREATE INDEX idx_verifications_user ON user_verifications(user_id) COMMENT '认证用户索引';
CREATE INDEX idx_verifications_status ON user_verifications(verification_status) COMMENT '认证状态索引';

CREATE INDEX idx_skills_user ON companion_skills(user_id) COMMENT '技能用户索引';
CREATE INDEX idx_skills_type ON companion_skills(skill_type) COMMENT '技能类型索引';

CREATE INDEX idx_services_provider ON accompany_services(provider_id) COMMENT '服务提供者索引';
CREATE INDEX idx_services_type_status ON accompany_services(service_type, status) COMMENT '服务类型状态索引';
CREATE INDEX idx_services_price ON accompany_services(price) COMMENT '服务价格索引';
CREATE INDEX idx_services_rating ON accompany_services(rating_avg DESC) COMMENT '服务评分索引';

CREATE INDEX idx_schedules_service ON service_schedules(service_id) COMMENT '时间表服务索引';
CREATE INDEX idx_schedules_user_day ON service_schedules(user_id, day_of_week) COMMENT '用户星期索引';

CREATE INDEX idx_orders_user ON orders(user_id) COMMENT '订单用户索引';
CREATE INDEX idx_orders_service ON orders(service_id) COMMENT '订单服务索引';
CREATE INDEX idx_orders_status ON orders(status) COMMENT '订单状态索引';
CREATE INDEX idx_orders_time ON orders(start_at, end_at) COMMENT '订单时间索引';

CREATE INDEX idx_reviews_order ON reviews(order_id) COMMENT '评价订单索引';
CREATE INDEX idx_reviews_service ON reviews(service_id) COMMENT '评价服务索引';
CREATE INDEX idx_reviews_reviewer ON reviews(review_user_id) COMMENT '被评价用户索引';

CREATE INDEX idx_transactions_wallet ON transactions(wallet_id) COMMENT '交易钱包索引';
CREATE INDEX idx_transactions_type_status ON transactions(type, status) COMMENT '交易类型状态索引';
CREATE INDEX idx_transactions_time ON transactions(created_at) COMMENT '交易时间索引';

CREATE INDEX idx_complaints_user ON complaints(user_id) COMMENT '投诉用户索引';
CREATE INDEX idx_complaints_target ON complaints(target_id) COMMENT '被投诉用户索引';
CREATE INDEX idx_complaints_status ON complaints(status) COMMENT '投诉状态索引';

CREATE INDEX idx_bounty_tasks_publisher ON bounty_tasks(publisher_id) COMMENT '赏金任务发布者索引';
CREATE INDEX idx_bounty_tasks_status ON bounty_tasks(status, scheduled_at) COMMENT '赏金任务状态时间索引';
CREATE INDEX idx_bounty_tasks_type ON bounty_tasks(service_type, status) COMMENT '赏金任务类型状态索引';
CREATE INDEX idx_bounty_tasks_location ON bounty_tasks(location) COMMENT '赏金任务地点索引';

CREATE INDEX idx_task_applications_task ON task_applications(task_id) COMMENT '任务申请索引';
CREATE INDEX idx_task_applications_applicant ON task_applications(applicant_id) COMMENT '申请者索引';

CREATE INDEX idx_tips_order ON tips(order_id) COMMENT '小费订单索引';
CREATE INDEX idx_tips_receiver ON tips(receiver_id) COMMENT '收小费者索引';

CREATE INDEX idx_user_behaviors_user ON user_behaviors(user_id) COMMENT '用户行为索引';
CREATE INDEX idx_user_behaviors_type ON user_behaviors(behavior_type) COMMENT '行为类型索引';

CREATE INDEX idx_community_posts_author ON community_posts(author_id) COMMENT '帖子作者索引';
CREATE INDEX idx_community_posts_type ON community_posts(post_type, status) COMMENT '帖子类型状态索引';
CREATE INDEX idx_community_posts_time ON community_posts(created_at DESC) COMMENT '帖子时间索引';

CREATE INDEX idx_post_comments_post ON post_comments(post_id) COMMENT '帖子评论索引';
CREATE INDEX idx_post_comments_user ON post_comments(user_id) COMMENT '评论用户索引';

CREATE INDEX idx_points_records_user ON points_records(user_id) COMMENT '积分用户索引';
CREATE INDEX idx_points_records_type ON points_records(type, source) COMMENT '积分类型来源索引';

CREATE INDEX idx_emergency_requests_user ON emergency_requests(user_id) COMMENT '紧急求助用户索引';
CREATE INDEX idx_emergency_requests_status ON emergency_requests(status, created_at) COMMENT '紧急求助状态索引';

CREATE INDEX idx_location_shares_order ON location_shares(order_id) COMMENT '位置共享订单索引';
CREATE INDEX idx_location_shares_user ON location_shares(sharer_id) COMMENT '位置共享用户索引';

CREATE INDEX idx_social_logins_user ON social_logins(user_id) COMMENT '第三方登录用户索引';
CREATE INDEX idx_social_logins_provider ON social_logins(provider, is_active) COMMENT '第三方登录提供商索引';

CREATE INDEX idx_content_moderations_type_status ON content_moderations(content_type, manual_review_result) COMMENT '内容审核类型状态索引';
CREATE INDEX idx_content_moderations_reviewer ON content_moderations(reviewer_id) COMMENT '审核员索引';

CREATE INDEX idx_activities_organizer ON activities(organizer_id) COMMENT '活动组织者索引';
CREATE INDEX idx_activities_type_status ON activities(activity_type, status) COMMENT '活动类型状态索引';
CREATE INDEX idx_activities_time ON activities(start_at, end_at) COMMENT '活动时间索引';
CREATE INDEX idx_activities_location ON activities(location_lat, location_lng) COMMENT '活动位置索引';

CREATE INDEX idx_activity_registrations_activity ON activity_registrations(activity_id) COMMENT '活动报名索引';
CREATE INDEX idx_activity_registrations_user ON activity_registrations(user_id) COMMENT '用户报名索引';

CREATE INDEX idx_interest_groups_creator ON interest_groups(creator_id) COMMENT '小组创建者索引';
CREATE INDEX idx_interest_groups_type ON interest_groups(group_type, status) COMMENT '小组类型状态索引';

CREATE INDEX idx_group_members_group ON group_members(group_id) COMMENT '小组成员索引';
CREATE INDEX idx_group_members_user ON group_members(user_id) COMMENT '用户小组索引';

CREATE INDEX idx_user_referrals_referrer ON user_referrals(referrer_id) COMMENT '推荐人索引';
CREATE INDEX idx_user_referrals_code ON user_referrals(referral_code) COMMENT '推荐码索引';

CREATE INDEX idx_user_devices_user ON user_devices(user_id) COMMENT '用户设备索引';
CREATE INDEX idx_user_devices_token ON user_devices(device_token) COMMENT '设备令牌索引';

CREATE INDEX idx_service_areas_parent ON service_areas(parent_id) COMMENT '父级区域索引';
CREATE INDEX idx_service_areas_type ON service_areas(area_type, is_supported) COMMENT '区域类型索引';

CREATE INDEX idx_users_city ON users(city) COMMENT '用户城市索引';
CREATE INDEX idx_users_verified ON users(is_verified, verified_at) COMMENT '用户认证状态索引';
CREATE INDEX idx_users_referral ON users(referral_code) COMMENT '用户推荐码索引';
CREATE INDEX idx_users_active ON users(last_active_at DESC) COMMENT '用户活跃时间索引';

CREATE INDEX idx_orders_provider ON orders(service_provider_id) COMMENT '订单服务提供者索引';
CREATE INDEX idx_orders_location ON orders(location_lat, location_lng) COMMENT '订单位置索引';
CREATE INDEX idx_orders_actual_time ON orders(actual_start_at, actual_end_at) COMMENT '订单实际时间索引';

CREATE INDEX idx_reviews_rating_quality ON reviews(service_quality_rating DESC) COMMENT '服务质量评分索引';
CREATE INDEX idx_reviews_featured ON reviews(is_featured, created_at DESC) COMMENT '精选评价索引';

CREATE INDEX idx_services_featured ON accompany_services(is_featured, featured_until) COMMENT '精选服务索引';
CREATE INDEX idx_services_auto_accept ON accompany_services(auto_accept, status) COMMENT '自动接受订单索引';
CREATE INDEX idx_services_location_mode ON accompany_services(service_mode, status) COMMENT '服务模式索引';

CREATE INDEX idx_verifications_face ON user_verifications(face_verification_status) COMMENT '人脸认证状态索引';
CREATE INDEX idx_verifications_background ON user_verifications(background_check_status) COMMENT '背景调查状态索引';

CREATE INDEX idx_transactions_no ON transactions(transaction_no) COMMENT '交易流水号索引';
CREATE INDEX idx_transactions_channel ON transactions(payment_channel, status) COMMENT '支付渠道状态索引';
CREATE INDEX idx_transactions_third_party ON transactions(third_party_transaction_id) COMMENT '第三方交易ID索引';

CREATE INDEX idx_complaints_priority ON complaints(priority, created_at DESC) COMMENT '投诉优先级索引';
CREATE INDEX idx_complaints_handler ON complaints(handler_id, status) COMMENT '投诉处理人索引';

-------------------- ✅ 第九步：系统日志 --------------------



-------------------- ✅ 第十步：创建视图简化复杂查询 --------------------
-- 用户统计信息视图
CREATE VIEW v_user_statistics AS
SELECT 
    u.user_id,
    u.user_name,
    u.user_type,
    u.credit_score,
    u.total_points,
    ul.level_name,
    COUNT(DISTINCT o1.order_id) as total_orders_as_user,
    COUNT(DISTINCT o2.order_id) as total_orders_as_provider,
    COUNT(DISTINCT CASE WHEN o1.status = 'completed' THEN o1.order_id END) as completed_orders_as_user,
    COUNT(DISTINCT CASE WHEN o2.status = 'completed' THEN o2.order_id END) as completed_orders_as_provider,
    AVG(r.rating) as avg_rating_received,
    COUNT(DISTINCT c.complaint_id) as complaint_count,
    w.balance as wallet_balance,
    w.total_income,
    w.total_expense
FROM users u
LEFT JOIN user_levels ul ON u.current_level_id = ul.level_id
LEFT JOIN orders o1 ON u.user_id = o1.user_id
LEFT JOIN orders o2 ON u.user_id = o2.service_provider_id
LEFT JOIN reviews r ON u.user_id = r.review_user_id
LEFT JOIN complaints c ON u.user_id = c.target_id
LEFT JOIN wallets w ON u.user_id = w.user_id
GROUP BY u.user_id;

-- 服务统计信息视图
CREATE VIEW v_service_statistics AS
SELECT 
    s.service_id,
    s.title,
    s.service_type,
    s.price,
    s.status,
    u.user_name as provider_name,
    s.views_count,
    s.orders_count,
    s.rating_avg,
    COUNT(DISTINCT r.review_id) as review_count,
    COUNT(DISTINCT f.favorite_id) as favorite_count,
    DATEDIFF(NOW(), s.created_at) as days_since_created
FROM accompany_services s
LEFT JOIN users u ON s.provider_id = u.user_id
LEFT JOIN reviews r ON s.service_id = r.service_id
LEFT JOIN favorites f ON s.service_id = f.service_id
GROUP BY s.service_id;

-- 订单详情视图
CREATE VIEW v_order_details AS
SELECT 
    o.order_id,
    o.status,
    o.start_at,
    o.end_at,
    o.actual_start_at,
    o.actual_end_at,
    o.amount,
    o.platform_fee,
    o.service_fee,
    o.tip_amount,
    u1.user_name as customer_name,
    u2.user_name as provider_name,
    s.title as service_title,
    s.service_type,
    r.rating as customer_rating,
    TIMESTAMPDIFF(MINUTE, o.actual_start_at, o.actual_end_at) as actual_duration_minutes
FROM orders o
LEFT JOIN users u1 ON o.user_id = u1.user_id
LEFT JOIN users u2 ON o.service_provider_id = u2.user_id
LEFT JOIN accompany_services s ON o.service_id = s.service_id
LEFT JOIN reviews r ON o.order_id = r.order_id;

-- 5. 插入系统基础配置数据

INSERT INTO system_configs (config_key, config_value, config_type, category, description, is_public) VALUES
('platform_fee_rate', '0.20', 'float', 'business', '平台服务费率', false),
('min_service_price', '50.00', 'float', 'business', '最低服务价格', true),
('max_service_price', '5000.00', 'float', 'business', '最高服务价格', true),
('cancel_fee_rate_24h', '0.20', 'float', 'business', '24小时内取消费率', true),
('cancel_fee_rate_48h', '0.10', 'float', 'business', '24-48小时取消费率', true),
('max_blacklist_users', '100', 'int', 'user', '每个用户最大拉黑数量', true),
('sms_code_expire_minutes', '10', 'int', 'system', '短信验证码过期时间(分钟)', false),
('max_daily_sms_send', '5', 'int', 'system', '每日最大短信发送数量', false),
('review_reply_days_limit', '7', 'int', 'business', '评价回复时间限制(天)', true),
('referral_reward_points', '100', 'int', 'business', '推荐奖励积分', false),
('daily_checkin_points', '5', 'int', 'business', '每日签到积分', true),
('app_version_android', '1.0.0', 'string', 'app', 'Android应用版本', true),
('app_version_ios', '1.0.0', 'string', 'app', 'iOS应用版本', true),
('service_radius_km', '30', 'int', 'business', '服务半径(公里)', true),
('emergency_contact_phone', '************', 'string', 'emergency', '紧急联系电话', true);

-- 插入用户等级配置
INSERT INTO user_levels (level_name, min_points, max_points, level_benefits, badge_icon) VALUES
('新手', 0, 99, '{"discount": 0, "priority_support": false, "free_cancellation": 1}', '/icons/level_newbie.png'),
('铜牌', 100, 499, '{"discount": 0.05, "priority_support": false, "free_cancellation": 2}', '/icons/level_bronze.png'),
('银牌', 500, 1499, '{"discount": 0.08, "priority_support": true, "free_cancellation": 3}', '/icons/level_silver.png'),
('金牌', 1500, 4999, '{"discount": 0.10, "priority_support": true, "free_cancellation": 5}', '/icons/level_gold.png'),
('钻石', 5000, NULL, '{"discount": 0.15, "priority_support": true, "free_cancellation": 10}', '/icons/level_diamond.png');

-- 插入消息模板
INSERT INTO message_templates (template_code, template_name, template_type, subject, content, variables) VALUES
('SMS_REGISTER', '注册验证码', 'sms', NULL, '您的验证码是：{code}，{expire_minutes}分钟内有效。', '{"code": "验证码", "expire_minutes": "过期时间"}'),
('SMS_LOGIN', '登录验证码', 'sms', NULL, '您的登录验证码是：{code}，{expire_minutes}分钟内有效。', '{"code": "验证码", "expire_minutes": "过期时间"}'),
('PUSH_ORDER_CONFIRMED', '订单确认', 'push', '订单确认通知', '您的订单已确认，服务时间：{start_time}', '{"start_time": "开始时间"}'),
('PUSH_ORDER_COMPLETED', '订单完成', 'push', '订单完成通知', '您的订单已完成，请对本次服务进行评价', '{}'),
('EMAIL_ORDER_RECEIPT', '订单收据', 'email', '订单收据 - {order_id}', '感谢您使用我们的服务，订单详情：...', '{"order_id": "订单号"}'),
('SYSTEM_WELCOME', '欢迎消息', 'system', '欢迎使用Accompany', '欢迎您加入Accompany！开始您的陪伴之旅吧。', '{}');

-- 插入服务区域数据（示例）
INSERT INTO service_areas (area_name, area_type, area_code, center_lat, center_lng, radius_km) VALUES
('中国', 'country', 'CN', 35.8617, 104.1954, 5000),
('北京市', 'city', 'BJ', 39.9042, 116.4074, 50),
('上海市', 'city', 'SH', 31.2304, 121.4737, 50),
('广州市', 'city', 'GZ', 23.1291, 113.2644, 50),
('深圳市', 'city', 'SZ', 22.5431, 114.0579, 50),
('杭州市', 'city', 'HZ', 30.2741, 120.1551, 50);