// 聊天记录集合
{
  collection: "chat_messages",
  document: {
    _id: ObjectId(),            // 消息ID
    order_id: Long,             // 关联的订单ID
    sender_id: Long,            // 发送者ID
    receiver_id: Long,          // 接收者ID
    content: String,            // 消息内容
    message_type: String,       // 消息类型：text文本, image图片, voice语音, video视频
    file_url: String,          // 媒体文件URL
    created_at: ISODate,       // 创建时间
    is_read: Boolean,          // 是否已读
    read_at: ISODate,          // 读取时间
    updated_at: ISODate        // 更新时间
  }
}

// 用户状态集合
{
  collection: "user_status",
  document: {
    _id: ObjectId(),           // 状态记录ID
    user_id: Long,             // 用户ID
    online_status: String,     // 在线状态：online在线, offline离线, busy忙碌, invisible隐身
    last_active: ISODate,      // 最后活跃时间
    current_order_id: Long,    // 当前进行中的订单ID
    location: {                // 位置信息
      type: "Point",
      coordinates: [Number]    // 经纬度坐标 [longitude, latitude]
    },
    device_info: {             // 设备信息
      device_type: String,     // 设备类型
      platform: String,        // 平台
      app_version: String      // APP版本
    },
    created_at: ISODate,       // 创建时间
    updated_at: ISODate        // 更新时间
  }
}

// 陪伴者档案集合
{
  collection: "companion_profiles",
  document: {
    _id: ObjectId(),           // 档案ID
    user_id: Long,             // 用户ID
    introduction: String,      // 个人介绍
    skills: [{                 // 技能列表
      skill_id: String,        // 技能ID
      name: String,            // 技能名称
      level: String,           // 技能等级
      certificates: [{         // 证书信息
        cert_id: String,       // 证书ID
        name: String,          // 证书名称
        url: String,           // 证书图片URL
        verified: Boolean,     // 是否已验证
        verified_at: ISODate   // 验证时间
      }]
    }],
    languages: [{              // 语言能力
      language: String,        // 语言
      proficiency: String      // 熟练程度
    }],
    availability: [{           // 可用时间
      day_of_week: Number,     // 星期几 (0-6)
      time_slots: [{           // 时间段
        start_time: String,    // 开始时间 "HH:mm"
        end_time: String       // 结束时间 "HH:mm"
      }]
    }],
    tags: [String],           // 标签
    statistics: {              // 统计信息
      total_service_hours: Number,  // 总服务时长
      completed_orders: Number,     // 完成订单数
      average_rating: Number,       // 平均评分
      total_income: Number,         // 总收入
      last_updated: ISODate         // 统计最后更新时间
    },
    preferences: {             // 偏好设置
      preferred_service_types: [String],  // 偏好服务类型
      preferred_clients: String,          // 偏好客户类型
      preferred_locations: [String]       // 偏好服务地点
    },
    created_at: ISODate,       // 创建时间
    updated_at: ISODate        // 更新时间
  }
}

// 系统通知集合
{
  collection: "notifications",
  document: {
    _id: ObjectId(),           // 通知ID
    user_id: Long,             // 接收用户ID
    type: String,              // 通知类型：system系统, order订单, chat聊天, payment支付
    title: String,             // 通知标题
    content: String,           // 通知内容
    related_id: String,        // 相关ID
    is_read: Boolean,          // 是否已读
    priority: String,          // 优先级：high, medium, low
    expires_at: ISODate,       // 过期时间
    created_at: ISODate,       // 创建时间
    read_at: ISODate,          // 读取时间
    updated_at: ISODate        // 更新时间
  }
}

// ===== 索引设计 =====

// 1. chat_messages 集合索引
db.chat_messages.createIndex({ "order_id": 1, "created_at": -1 })  // 查询订单聊天记录
db.chat_messages.createIndex({ "sender_id": 1, "receiver_id": 1, "created_at": -1 })  // 查询用户间聊天
db.chat_messages.createIndex({ "receiver_id": 1, "is_read": 1 })  // 查询未读消息
db.chat_messages.createIndex({ "created_at": -1 })  // 按时间排序

// 2. user_status 集合索引
db.user_status.createIndex({ "user_id": 1 }, { unique: true })  // 用户状态唯一索引
db.user_status.createIndex({ "online_status": 1, "last_active": -1 })  // 在线用户查询
db.user_status.createIndex({ "location": "2dsphere" })  // 地理位置索引
db.user_status.createIndex({ "current_order_id": 1 })  // 当前订单索引

// 3. companion_profiles 集合索引
db.companion_profiles.createIndex({ "user_id": 1 }, { unique: true })  // 用户档案唯一索引
db.companion_profiles.createIndex({ "tags": 1 })  // 标签搜索
db.companion_profiles.createIndex({ "skills.name": 1 })  // 技能搜索
db.companion_profiles.createIndex({ "statistics.average_rating": -1 })  // 评分排序
db.companion_profiles.createIndex({ "availability.day_of_week": 1 })  // 可用时间查询
db.companion_profiles.createIndex({ "preferences.preferred_service_types": 1 })  // 服务类型过滤

// 4. notifications 集合索引
db.notifications.createIndex({ "user_id": 1, "created_at": -1 })  // 用户通知查询
db.notifications.createIndex({ "user_id": 1, "is_read": 1 })  // 未读通知查询
db.notifications.createIndex({ "type": 1, "created_at": -1 })  // 通知类型查询
db.notifications.createIndex({ "expires_at": 1 }, { expireAfterSeconds: 0 })  // TTL索引自动清理过期通知

// ===== 数据验证规则 =====

// chat_messages 验证规则
db.createCollection("chat_messages", {
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: ["order_id", "sender_id", "receiver_id", "message_type", "created_at"],
      properties: {
        message_type: {
          enum: ["text", "image", "voice", "video", "file"]
        },
        is_read: {
          bsonType: "bool",
          default: false
        }
      }
    }
  }
})

// user_status 验证规则
db.createCollection("user_status", {
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: ["user_id", "online_status"],
      properties: {
        online_status: {
          enum: ["online", "offline", "busy", "invisible"]
        },
        location: {
          bsonType: "object",
          properties: {
            type: { enum: ["Point"] },
            coordinates: {
              bsonType: "array",
              minItems: 2,
              maxItems: 2
            }
          }
        }
      }
    }
  }
})

// notifications 验证规则
db.createCollection("notifications", {
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: ["user_id", "type", "title", "content", "created_at"],
      properties: {
        type: {
          enum: ["system", "order", "chat", "payment"]
        },
        priority: {
          enum: ["high", "medium", "low"],
          default: "medium"
        },
        is_read: {
          bsonType: "bool",
          default: false
        }
      }
    }
  }
})