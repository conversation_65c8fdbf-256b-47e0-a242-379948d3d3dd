
# App 系统开发优先级分组与规划

## ✅ 第一步：账号与认证系统（必须）

> 建立用户生命周期、登录鉴权、安全控制

| 中文表名     | 英文表名                     | 说明              |
|----------|--------------------------|-----------------|
| 用户基础表    | `users`                  | 用户主信息           |
| 用户认证信息表  | `user_verifications`     | 实名 / 职业 / 头像审核等 |
| 第三方登录表   | `social_logins`          | 微信、QQ、Apple 登录等 |
| 短信验证码表   | `sms_verification_codes` | 登录注册验证码用        |
| 拉黑名单表    | `blacklists`             | 黑名单管理           |
| 用户设备管理表  | `user_devices`           | 防刷 / 登录设备统计     |
| API调用日志表 | `api_logs`               | 安全审计与接口记录       |

## ✅ 第二步：陪伴服务系统（核心功能）

> 用户下单、服务流程、技能时间段管理

| 中文表名    | 英文表名                  | 说明         |
|---------|-----------------------|------------|
| 陪伴者技能表  | `companion_skills`    | 陪伴者技能项维护   |
| 陪伴服务表   | `accompany_services`  | 服务项定义      |
| 服务时间段表  | `service_schedules`   | 提供服务时间管理   |
| 服务区域表   | `service_areas`       | 可服务的地理位置管理 |
| 订单表     | `orders`              | 下单、匹配      |
| 用户评价表   | `reviews`             | 服务后评分      |
| 用户行为记录表 | `user_behavior`       | 页面行为、浏览、点击 |
| 内容审核表   | `content_moderations` | 服务图文内容审核   |

## ✅ 第三步：交易支付系统

> 钱包体系、交易明细、优惠券系统

| 中文表名   | 英文表名             | 说明        |
|--------|------------------|-----------|
| 用户钱包表  | `wallets`        | 余额管理      |
| 交易记录表  | `transactions`   | 钱包的每一笔交易  |
| 小费记录表  | `tips`           | 打赏小费      |
| 优惠券表   | `coupons`        | 平台配置优惠券   |
| 用户优惠券表 | `user_coupons`   | 用户拥有的优惠券  |
| 积分记录表  | `points_records` | 行为积分兑换优惠等 |

## ✅ 第四步：赏金任务 + 投诉报警系统

> 增加互动环节、提供安全保障机制

| 中文表名    | 英文表名                 | 说明       |
|---------|----------------------|----------|
| 赏金任务表   | `bounty_tasks`       | 用户发起悬赏   |
| 任务申请表   | `task_applications`  | 陪伴者申请任务  |
| 投诉举报表   | `complaints`         | 服务中异常处理  |
| 紧急求助记录表 | `emergency_requests` | 一键求助保障安全 |

## ✅ 第五步：社交互动系统

> 拉动用户关系链，提高活跃度与内容产出

| 中文表名    | 英文表名              | 说明       |
|---------|-------------------|----------|
| 收藏表     | `favorites`       | 收藏服务或内容  |
| 关注表     | `follows`         | 关注用户     |
| 浏览历史表   | `browse_history`  | 浏览记录     |
| 社区帖子表   | `community_posts` | 用户发帖     |
| 帖子评论表   | `post_comments`   | 评论互动     |
| 用户等级表   | `user_levels`     | 等级与成长体系  |
| 用户推荐邀请表 | `user_referrals`  | 拉新、邀请码机制 |

## ✅ 第六步：活动与兴趣小组模块

> 提升用户留存、开展运营活动

| 中文表名  | 英文表名                     | 说明       |
|-------|--------------------------|----------|
| 活动管理表 | `activities`             | 活动发布     |
| 活动报名表 | `activity_registrations` | 用户报名参与活动 |
| 兴趣小组表 | `interest_groups`        | 兴趣小组     |
| 小组成员表 | `group_members`          | 小组成员记录   |

## ✅ 第七步：系统配置与消息体系

> 平台级支持模块，可后置开发

| 中文表名    | 英文表名                   | 说明        |
|---------|------------------------|-----------|
| 系统配置表   | `system_configs`           | 动态参数设置    |
| 系统消息模板表 | `message_templates` | 消息推送模板    |
| 位置共享记录表 | `location_shares`       | 可选功能：位置共享 |

## 📋 推荐开发节奏（按优先级）

| 阶段   | 功能目标                | 表数量 |
|------|---------------------|-----|
| 第1阶段 | 用户注册 / 登录 / 鉴权 / 安全 | 7张  |
| 第2阶段 | 服务上线、技能/时间段管理、下单支付  | 8张  |
| 第3阶段 | 钱包、交易、优惠券系统上线       | 6张  |
| 第4阶段 | 增加赏金、投诉等安全保障        | 4张  |
| 第5阶段 | 拉新、互动、社交活跃机制        | 7张  |
| 第6阶段 | 兴趣圈子 / 线下活动运营能力     | 4张  |
| 第7阶段 | 后台配置 / 消息推送体系支持     | 3张  |
