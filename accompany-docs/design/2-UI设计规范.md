# Accompany UI设计规范

## 设计理念
**品牌愿景**：连接人心，传递温暖
**设计原则**：简约而不简单，温暖而不失专业，安全而不失便利
**核心价值**：信任、温暖、专业、便捷

## 设计风格

### 视觉风格
- **整体风格**: 温暖、友好、简约现代、扁平化设计
- **设计语言**: Material Design 3.0 + 品牌化定制
- **视觉层次**: 清晰的信息架构，重点突出，层次分明
- **情感表达**: 传递安全感、温暖感、专业感

### 配色方案
**主色调**
- **品牌主色**: #FF6B6B (温暖珊瑚红) - 传递温暖与活力
- **辅助主色**: #4ECDC4 (清新薄荷绿) - 代表安全与信任
- **强调色**: #45B7D1 (天空蓝) - 用于重要操作和链接

**功能色彩**
- **成功色**: #96CEB4 (清新绿)
- **警告色**: #FFEAA7 (温暖黄)
- **错误色**: #FD79A8 (柔和红)
- **信息色**: #74B9FF (柔和蓝)

**中性色彩**
- **主文字**: #2D3436 (深灰)
- **次要文字**: #636E72 (中灰)
- **辅助文字**: #B2BEC3 (浅灰)
- **边框色**: #DDD6FE (极浅紫)
- **背景色**: #FAFAFB (微灰白)
- **卡片背景**: #FFFFFF (纯白)

### 字体规范
**中文字体**
- **主字体**: PingFang SC (iOS) / Source Han Sans (Android)
- **英文字体**: SF Pro Display (iOS) / Roboto (Android)
- **数字字体**: DIN Alternate (突出数据展示)

**字体层级**
- **H1 标题**: 24px/32px, Bold, 主要页面标题
- **H2 标题**: 20px/28px, Bold, 次级标题
- **H3 标题**: 18px/24px, Medium, 区块标题
- **H4 标题**: 16px/22px, Medium, 卡片标题
- **正文**: 14px/20px, Regular, 常规文本
- **小文字**: 12px/18px, Regular, 辅助信息
- **按钮文字**: 16px/22px, Medium, 操作按钮

### 图标设计
- **风格**: 圆润线性图标，线宽2px
- **尺寸规范**: 16px, 20px, 24px, 32px, 48px
- **颜色**: 遵循配色方案，支持多色态
- **状态**: 默认、选中、禁用、加载状态

### 插图设计
- **风格**: 扁平化插画，温暖色调
- **人物**: 多元化、包容性、亲和力
- **场景**: 真实陪伴场景，情感化表达
- **应用**: 空状态、引导页、功能说明

## 组件规范

### 基础组件

**按钮 (Button)**
- **主要按钮**: 品牌主色背景，白色文字，圆角8px，高度44px
- **次要按钮**: 透明背景，品牌主色边框和文字，圆角8px
- **文字按钮**: 无背景边框，品牌主色文字
- **禁用状态**: 灰色背景，浅灰色文字
- **加载状态**: 显示loading动画，文字变为"加载中..."

**输入框 (Input)**
- **默认状态**: 边框色#E1E5E9，圆角8px，高度44px
- **聚焦状态**: 品牌主色边框，内阴影效果
- **错误状态**: 错误色边框，错误提示文字
- **禁用状态**: 灰色背景，浅灰色文字
- **占位符**: 中性灰色文字

**卡片 (Card)**
- **背景**: 白色，圆角12px
- **阴影**: 0 2px 8px rgba(0,0,0,0.1)
- **内边距**: 16px
- **边框**: 无边框或1px浅灰色边框

### 导航组件

**顶部导航栏**
- **高度**: 56px (状态栏高度+导航栏高度)
- **背景**: 白色或品牌主色
- **标题**: 居中显示，H3字体规格
- **左侧**: 返回按钮或抽屉菜单
- **右侧**: 功能按钮（搜索、设置等）

**底部导航栏**
- **高度**: 56px + 安全区域
- **背景**: 白色，顶部1px分割线
- **标签数量**: 5个（首页、赏金、发布、消息、我的）
- **图标**: 24px，选中态变色
- **文字**: 12px，选中态加粗

**侧边栏 (Drawer)**
- **宽度**: 280px
- **背景**: 白色
- **用户信息**: 顶部展示头像、昵称、等级
- **菜单项**: 列表形式，图标+文字

### 数据展示组件

**列表项 (List Item)**
- **高度**: 最小56px，根据内容自适应
- **内边距**: 水平16px，垂直12px
- **分割线**: 1px浅灰色线
- **头像**: 40px圆形，默认占位图

**评分组件**
- **星星大小**: 16px
- **颜色**: 金黄色#FFD93D
- **显示**: 支持半星显示
- **文字**: 评分数值+评价数量

**标签 (Tag)**
- **高度**: 24px
- **圆角**: 12px (完全圆角)
- **内边距**: 水平8px
- **颜色**: 品牌色系的浅色背景

### 反馈组件

**消息提示 (Toast)**
- **位置**: 屏幕顶部或底部
- **圆角**: 8px
- **内边距**: 12px 16px
- **持续时间**: 3秒自动消失

**模态框 (Modal)**
- **背景遮罩**: 半透明黑色
- **内容区**: 白色背景，圆角12px
- **最大宽度**: 屏幕宽度-64px
- **动画**: 淡入淡出+缩放效果

**加载组件**
- **骨架屏**: 用于数据加载状态
- **下拉刷新**: 品牌色loading动画
- **上拉加载**: 底部显示加载更多

## 主要界面设计

### 启动与引导页
**启动页 (Splash)**
- **背景**: 品牌主色渐变
- **LOGO**: 居中显示，白色
- **文案**: 品牌slogan，优雅字体
- **加载**: 底部显示加载进度

**引导页 (Onboarding)**
- **页面数量**: 3-4页
- **布局**: 上图下文，占比7:3
- **插图**: 品牌插画风格
- **文案**: 简洁有力，突出核心价值
- **操作**: 点击跳过，滑动翻页

### 首页 (Home)
**布局结构**
- **顶部**: 搜索栏+消息图标
- **Banner**: 轮播图，展示活动和推荐
- **快捷入口**: 4x2网格，常用服务分类
- **推荐区域**: 横滑卡片列表
- **附近服务**: 列表形式，LBS推荐

**设计细节**
- **搜索栏**: 圆角搜索框，占位文字"搜索服务或陪伴者"
- **服务卡片**: 头像+姓名+评分+价格+标签
- **距离显示**: 右上角显示距离信息
- **快速预约**: 卡片上的"立即预约"按钮

### 搜索与筛选页
**搜索界面**
- **搜索栏**: 自动聚焦，支持语音输入
- **热门搜索**: 标签云形式展示
- **搜索历史**: 列表展示，支持清除
- **搜索建议**: 实时联想，关键词高亮

**筛选界面**
- **筛选条件**: 分组折叠展示
- **价格区间**: 滑动条选择
- **时间选择**: 日历组件
- **地区选择**: 级联选择器
- **重置按钮**: 清除所有筛选条件

**结果展示**
- **列表/网格**: 支持视图切换
- **排序方式**: 下拉选择器
- **加载更多**: 分页加载
- **空状态**: 插画+引导文案

### 个人资料页
**个人主页**
- **头部信息**: 大头像+基本信息+认证标识
- **统计数据**: 订单数/评分/收入等关键指标
- **服务展示**: 提供的服务类型和价格
- **评价展示**: 最新评价，支持查看更多
- **相册展示**: 个人照片和服务场景

**编辑页面**
- **表单布局**: 分组展示，清晰的视觉层次
- **图片上传**: 支持多图上传，实时预览
- **标签选择**: 多选标签，支持自定义
- **保存状态**: 实时保存提示

### 聊天界面
**消息列表**
- **会话项**: 头像+姓名+最后消息+时间+未读数
- **置顶会话**: 顶部固定显示
- **消息状态**: 已读/未读标识
- **滑动操作**: 左滑删除/置顶

**聊天详情**
- **消息气泡**: 左右布局，圆角设计
- **时间显示**: 消息间隔显示时间戳
- **输入区域**: 文字+表情+语音+图片
- **功能入口**: 位置/预约/支付等快捷操作

### 服务进行页
**服务状态**
- **进度条**: 可视化服务进度
- **倒计时**: 大号数字显示剩余时间
- **状态指示**: 等待开始/进行中/暂停/结束
- **位置信息**: 地图显示双方位置

**操作区域**
- **主要操作**: 开始/暂停/结束服务
- **辅助功能**: 聊天/电话/紧急求助
- **服务变更**: 延长时间/修改内容

### 发布页面
**服务发布**
- **分步表单**: 多步骤引导，进度指示
- **模板选择**: 常用服务模板快速填充
- **多媒体**: 支持图片/视频/音频
- **预览功能**: 发布前预览效果

**赏金任务**
- **紧急程度**: 可视化优先级选择
- **预算设置**: 滑动条+输入框
- **时间地点**: 日历+地图选择
- **要求描述**: 富文本编辑器

### 支付系统
**支付页面**
- **订单信息**: 清晰的费用明细
- **支付方式**: 图标化展示各种支付方式
- **优惠信息**: 优惠券/积分抵扣
- **安全提示**: 支付安全保障说明

**钱包界面**
- **余额显示**: 大号数字突出显示
- **交易记录**: 时间线形式展示
- **提现功能**: 简化操作流程
- **账单统计**: 图表化展示收支

## 交互设计

### 手势交互
- **下拉刷新**: 释放触发，回弹动画
- **上拉加载**: 阈值触发，加载指示
- **左滑操作**: 聊天删除，列表功能
- **长按操作**: 上下文菜单，快捷操作
- **双击操作**: 点赞，收藏功能

### 动画效果
**转场动画**
- **页面切换**: 滑动转场，300ms缓动
- **模态弹出**: 淡入+缩放，250ms
- **列表项**: 交错入场，增强层次感

**反馈动画**
- **按钮点击**: 轻微缩放+色彩变化
- **加载状态**: 品牌色呼吸灯效果
- **成功反馈**: 绿色勾选+弹跳动画
- **错误反馈**: 红色摇晃+振动

### 状态设计
**加载状态**
- **初次加载**: 骨架屏占位
- **刷新加载**: 下拉刷新动画
- **分页加载**: 底部加载指示器
- **按钮加载**: 按钮内loading状态

**空状态**
- **无数据**: 插画+引导文案+操作按钮
- **网络错误**: 错误插画+重试按钮
- **搜索无结果**: 搜索建议+换个关键词

**错误状态**
- **表单错误**: 红色边框+错误文案
- **网络异常**: Toast提示+重试机制
- **服务异常**: 友好的错误页面

## 响应式设计

### 适配规则
**屏幕尺寸**
- **小屏**: 320px-375px (iPhone SE/6/7/8)
- **中屏**: 375px-414px (iPhone X/11/12)
- **大屏**: 414px+ (iPhone Plus/Max)
- **平板**: 768px+ (iPad适配)

**适配策略**
- **弹性布局**: Flexbox布局，自适应屏幕
- **比例缩放**: 关键元素按比例缩放
- **断点设计**: 不同尺寸采用不同布局
- **安全区域**: 适配刘海屏和虚拟按键

### 横屏适配
- **聊天界面**: 左右分栏布局
- **视频通话**: 全屏显示，控制栏悬浮
- **地图查看**: 全屏地图，工具栏收起

## 可访问性设计

### 无障碍支持
- **文字对比度**: 符合WCAG 2.1 AA标准
- **字体大小**: 支持系统字体缩放
- **色彩传达**: 不仅依赖颜色传达信息
- **焦点状态**: 清晰的键盘导航焦点
- **语音支持**: VoiceOver/TalkBack优化

### 适老化设计
- **大字体模式**: 关键信息放大显示
- **简化操作**: 减少复杂的手势操作
- **语音助手**: 语音输入和播报功能
- **紧急联系**: 一键呼叫紧急联系人

## 品牌一致性

### 视觉识别
- **LOGO应用**: 标准色彩，最小尺寸规范
- **品牌色彩**: 严格遵循色彩规范
- **品牌字体**: 统一的字体应用
- **品牌图案**: 专属的装饰图案

### 文案风格
- **语气**: 温暖、亲切、专业
- **用词**: 简洁明了，避免专业术语
- **情感**: 传递安全感和温暖感
- **本地化**: 适应不同地区的表达习惯

## 设计交付

### 设计稿规范
- **画板尺寸**: 375x812 (iPhone X)基准
- **文件命名**: 模块_页面_状态_版本号
- **图层组织**: 清晰的图层命名和分组
- **标注说明**: 详细的尺寸和交互说明

### 切图规范
- **图片格式**: PNG/JPEG/SVG/WebP
- **分辨率**: @1x/@2x/@3x多倍图
- **命名规范**: 功能_状态_尺寸@倍数
- **压缩优化**: 在保证质量前提下最小化文件

### 组件库
- **设计组件**: Figma/Sketch组件库
- **代码组件**: React Native/Flutter组件
- **文档维护**: 组件使用说明和更新日志
- **版本管理**: 设计系统版本控制

这个完善的UI设计规范为Accompany产品提供了全面的设计指导，确保产品在视觉和交互上的一致性和专业性。